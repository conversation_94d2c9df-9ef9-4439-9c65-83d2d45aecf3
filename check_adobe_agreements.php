<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\License;
use App\Models\Agreement;
use App\Jobs\SendAgreement;
use Illuminate\Support\Facades\Queue;

echo "=== Adobe Sign Agreement Analysis ===\n\n";

try {
    // Check recent licenses and their agreements
    echo "1. Checking Recent Licenses and Adobe Sign Agreements:\n";
    
    $recentLicenses = License::orderBy('created_at', 'desc')->limit(10)->get();
    
    $successfulAgreements = 0;
    $failedAgreements = 0;
    $missingAgreements = 0;
    
    foreach ($recentLicenses as $license) {
        echo "   License ID: {$license->id} (Created: {$license->created_at})\n";
        
        $agreements = $license->agreement()->get();
        
        if ($agreements->count() === 0) {
            echo "      ❌ NO Adobe Sign agreement found\n";
            $missingAgreements++;
        } else {
            foreach ($agreements as $agreement) {
                echo "      Agreement ID: {$agreement->id}\n";
                echo "      Created: {$agreement->created_at}\n";
                echo "      Status: {$agreement->status}\n";
                echo "      Adobe Agreement ID: " . ($agreement->adobe_agreement_id ?? 'NULL') . "\n";
                
                if ($agreement->adobe_agreement_id) {
                    echo "      ✅ SUCCESS: Adobe Sign agreement created\n";
                    $successfulAgreements++;
                } else {
                    echo "      ❌ FAILED: No Adobe agreement ID\n";
                    $failedAgreements++;
                }
            }
        }
        echo "\n";
    }
    
    echo "2. Summary of Recent Agreements:\n";
    echo "   ✅ Successful: {$successfulAgreements}\n";
    echo "   ❌ Failed: {$failedAgreements}\n";
    echo "   ❌ Missing: {$missingAgreements}\n\n";
    
    // Check failed jobs
    echo "3. Checking Failed Jobs:\n";
    try {
        $failedJobs = \DB::table('failed_jobs')
            ->where('payload', 'like', '%SendAgreement%')
            ->orderBy('failed_at', 'desc')
            ->limit(5)
            ->get();
        
        if ($failedJobs->count() > 0) {
            echo "   ❌ Found " . $failedJobs->count() . " failed SendAgreement jobs:\n";
            foreach ($failedJobs as $job) {
                $payload = json_decode($job->payload, true);
                $exception = $job->exception;
                
                echo "      Failed at: {$job->failed_at}\n";
                echo "      Exception: " . substr($exception, 0, 200) . "...\n\n";
            }
        } else {
            echo "   ✅ No failed SendAgreement jobs found\n";
        }
    } catch (Exception $e) {
        echo "   ⚠️  Could not check failed jobs: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
    
    // Check queue status
    echo "4. Checking Queue Status:\n";
    try {
        $pendingJobs = \DB::table('jobs')
            ->where('payload', 'like', '%SendAgreement%')
            ->count();
        
        echo "   Pending SendAgreement jobs: {$pendingJobs}\n";
        
        if ($pendingJobs > 0) {
            echo "   ⚠️  There are pending jobs that haven't been processed\n";
            echo "   Make sure your queue worker is running: php artisan queue:work\n";
        }
    } catch (Exception $e) {
        echo "   ⚠️  Could not check queue: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
    
    // Analyze the pattern
    echo "5. Pattern Analysis:\n";
    
    $julyLicenses = License::whereYear('created_at', 2025)->whereMonth('created_at', 7)->count();
    $augustLicenses = License::whereYear('created_at', 2025)->whereMonth('created_at', 8)->count();
    
    $julySuccessfulAgreements = Agreement::where('type', 'license')
        ->whereYear('created_at', 2025)
        ->whereMonth('created_at', 7)
        ->whereNotNull('adobe_agreement_id')
        ->count();
        
    $augustSuccessfulAgreements = Agreement::where('type', 'license')
        ->whereYear('created_at', 2025)
        ->whereMonth('created_at', 8)
        ->whereNotNull('adobe_agreement_id')
        ->count();
    
    echo "   July 2025:\n";
    echo "      Licenses created: {$julyLicenses}\n";
    echo "      Successful Adobe agreements: {$julySuccessfulAgreements}\n";
    echo "      Success rate: " . ($julyLicenses > 0 ? round(($julySuccessfulAgreements / $julyLicenses) * 100, 1) : 0) . "%\n\n";
    
    echo "   August 2025:\n";
    echo "      Licenses created: {$augustLicenses}\n";
    echo "      Successful Adobe agreements: {$augustSuccessfulAgreements}\n";
    echo "      Success rate: " . ($augustLicenses > 0 ? round(($augustSuccessfulAgreements / $augustLicenses) * 100, 1) : 0) . "%\n\n";
    
    if ($augustSuccessfulAgreements == 0 && $augustLicenses > 0) {
        echo "   🚨 CONFIRMED: Adobe Sign integration stopped working in August 2025!\n";
    }
    
    echo "\n=== Recommendations ===\n";
    
    if ($failedAgreements > 0 || $missingAgreements > 0) {
        echo "1. Check Laravel logs for SendAgreement job errors\n";
        echo "2. Manually retry failed agreements\n";
        echo "3. Check if queue worker is running\n";
    }
    
    if ($augustSuccessfulAgreements == 0) {
        echo "4. The issue started in August - check what changed between July and August\n";
        echo "5. Adobe Sign API might have changed or access token expired\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
