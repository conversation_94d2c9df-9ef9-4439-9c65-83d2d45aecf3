@extends('layouts.app')

@section('content')
    
        <div class="page-title no-border-btm">
            <div class="w-100 d-flex align-items-center">
                <div class="w-100 title-left">
                    <div class="w-100 d-flex align-items-center">
                        <h3>lease: {{ $lease->studio->name }}</h3>
                        <span class="ms-3" style="font-size: 12px;text-transform: none;">
                            @include('partials.leases-badge', [
                                'status' => $lease->status,
                            ])
                        </span>
                    </div>
                    <a href="{{route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'lease'])}}" class="back-link">← Back</a>                 
                </div>
            </div>
        </div>
    

    <ul class="nav nav-tabs lh-1 mx-n12" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="payment-history-tab"
                data-bs-toggle="tab" data-bs-target="#payment-history" data-tab="payment-history" type="button"
                role="tab" aria-controls="payment-history" aria-selected="false">{{ __('PAYMENT HISTORY') }}</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="upcoming-payments-tab"
                data-bs-toggle="tab" data-bs-target="#upcoming-payments" data-tab="upcoming-payments" type="button"
                role="tab" aria-controls="upcoming-payments"
                aria-selected="false">{{ __('UPCOMING PAYMENTS') }}</button>
        </li>
        {{-- <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="files-tab" data-bs-toggle="tab"
                data-bs-target="#files" data-tab="files" type="button" role="tab" aria-controls="files"
                aria-selected="false">{{ __('AGREEMENTS (' . $lease_files_count . ')') }}</button>
        </li> --}}
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="notes-tab" data-bs-toggle="tab"
                data-bs-target="#notes" data-tab="notes" type="button" role="tab" aria-controls="notes"
                aria-selected="false">{{ __('INTERNAL NOTES (' . count($lease_notes) . ')') }}</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link active ps-0 ls-0.6px px-2 pe-sm-3 text-uppercase custom-fw-500" id="profile-tab"
                data-bs-toggle="tab" data-bs-target="#profile" data-tab="profile" type="button" role="tab"
                aria-controls="profile" aria-selected="true">{{ __('PROFILE') }}</button>
        </li>
    </ul>


    <div class="tab-content" id="myTabContent1">
        <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
            <div class="main-subtitle no-border-btm">
                <h5>{{ __('basic info') }}</h5>
            </div>
            <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Company Name:</div>
                        <div class="">
                            {{ $lease->studio->name }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Owner:</div>
                        <div class="">
                            {{ $lease->studio->owner_first_name }} {{ $lease->studio->owner_last_name }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Phone #:</div>
                        <div class="">
                            {{ $lease->studio->phone }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Email:</div>
                        <div class="">
                            {{ $lease->studio->email }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-subtitle no-border-btm">
                <h5>{{ __('Address info') }}</h5>
            </div>
            <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Address:</div>
                        <div class="">
                            {{ $lease->studio->address }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">City:</div>
                        <div class="">
                            {{ $lease->studio->city }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">State:</div>
                        <div class="">
                            {{ $lease->studio->state->name }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Zip Code:</div>
                        <div class="">
                            {{ $lease->studio->zip }}
                        </div>
                    </div>
                </div>
            </div>

            <h5 class="fs-14px mb-45 pt-45 form-section-title custom-fw-500 text-uppercase">{{ __('lease info') }}</h5>
            <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Machine:</div>
                        <div class="">
                            {{ ucfirst($lease->machine->name) }} x {{ $lease->machine_quantity }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Price:</div>
                        <div class="">
                            <span
                                class="text-success">{{ $admin_currency_symbol }}{{ number_format($leaseTotal, 2) }}</span>
                            &nbsp;
                            <span>({{ $admin_currency_symbol }}{{ number_format($leaseMonthly, 2) }}/month)</span>
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Deposit:</div>
                        <div class="">
                            {{ $admin_currency_symbol }}{{ number_format($leaseDeposit, 2) ?? 'No Deposit' }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Remaining:</div>
                        <div class="">
                            <span
                                class="text-danger">{{ $admin_currency_symbol }}{{ number_format($leaseRemaining['paymentSum'], 2) }}</span>
                            &nbsp;
                            <span>({{ $leaseRemaining['count'] }}/{{ $lease->duration }} months) </span>
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Start date:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($lease->starting_date)->format('m/d/Y') }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Ends on:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($lease->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y') }}
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                <a type="button" href="{{ route('admin.leases.edit', ['customer' => $customer, 'lease' => $lease]) }}">
                    <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
                </a>
            </div>
        </div>

        <div class="tab-pane fade" id="payment-history" role="tabpanel" aria-labelledby="payment-history-tab"
            tabindex="1">
        </div>

        <div class="tab-pane fade" id="upcoming-payments" role="tabpanel" aria-labelledby="upcoming-payments-tab"
            tabindex="2">

        </div>
        <div class="tab-pane fade" id="notes" role="tabpanel" aria-labelledby="notes-tab" tabindex="3">

        </div>
        <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab" tabindex="3">

        </div>


        <script type="module">
            $(document).ready(function() {
                $('button[data-bs-toggle="tab"]').on('click', function() {
                    var target = $(this).data('bs-target');
                    localStorage.setItem('leaseActiveTab', target);
                    loadTabContent(this);
                });

                var leaseActiveTab = localStorage.getItem('leaseActiveTab');
                if (leaseActiveTab) {
                    activateTab(leaseActiveTab);
                } else {
                    activateTab('#profile');
                }

                // Function to activate a tab and load its content
                function activateTab(target) {
                    // Remove active class from all tabs and content
                    $('.nav-link').removeClass('active');
                    $('.tab-pane').removeClass('show active');

                    // Add active class to the selected tab and content
                    $(`button[data-bs-target="${target}"]`).addClass('active');
                    $(target).addClass('show active');

                    // Load content for the selected tab
                    var tabElement = $(`button[data-bs-target="${target}"]`);
                    loadTabContent(tabElement);
                }


                function loadTabContent(tabElement) {
                    var target = $(tabElement).data('bs-target');
                    var tab = $(tabElement).data('tab');
                    var url =
                        '{{ route('admin.leases.load-tab-content', ['customer' => $customer, 'lease' => $lease]) }}' +
                        '?tab=' + tab;
                    var targetContent = $(target);

                    if (!$.trim(targetContent.html())) {
                        $.ajax({
                            url: url,
                            method: 'GET',
                            success: function(data) {
                                setTimeout(function() {
                                    targetContent.html(data);
                                }, 300);
                            },
                            error: function() {
                                flasherJS.error('', 'Error occurred while loading data.');
                            }
                        });
                    }
                }
            });
        </script>
    @endsection
