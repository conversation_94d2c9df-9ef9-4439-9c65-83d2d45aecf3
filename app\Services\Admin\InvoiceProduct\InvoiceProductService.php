<?php

namespace App\Services\Admin\InvoiceProduct;

use App\Models\Customer;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductItem;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceProductService implements IInvoiceProductService
{
    public function store(array $data): void
    {
        // echo '<pre>';
        // print_r($data->toArray());
        // die();        

        $lastInvoiceProduct = InvoiceProduct::withTrashed()->orderBy('id', 'desc')->first();
        $invoiceProduct = InvoiceProduct::create(
            array_merge($data, ['number' => $lastInvoiceProduct ? $lastInvoiceProduct->id + 1 : 1])
        );
        $invoiceProduct->items()->createMany($data['items']);
    }

    public function update(array $data, InvoiceProduct $invoiceProduct): void
    {
        // Always update the parent's updated_at timestamp, even if only items changed
        $data['updated_at'] = now();
        $invoiceProduct->update($data);

        $ids = [];
        $itemsChanged = false;

        foreach ($data['items'] as $item) {
            if (isset($item['id'])) {
                $ids[] = $item['id'];
                unset($item['product_name']);
                $invoiceProduct->items()->where('id', $item['id'])->update($item);
                $itemsChanged = true;
            } else {
                unset($item['product_name']);
                $newItem = $invoiceProduct->items()->create($item);
                $ids[] = $newItem->id;
                $itemsChanged = true;
            }
        }

        // Delete items that are no longer in the request
        $deletedCount = InvoiceProductItem::where('invoice_product_id', $invoiceProduct->id)
            ->whereNotIn('id', $ids)
            ->count();

        if ($deletedCount > 0) {
            InvoiceProductItem::where('invoice_product_id', $invoiceProduct->id)
                ->whereNotIn('id', $ids)
                ->delete();
            $itemsChanged = true;
        }

        // If items were changed but the main data wasn't, ensure updated_at is still updated
        if ($itemsChanged) {
            $invoiceProduct->touch();
        }
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $customerId): LengthAwarePaginator
    {
        $query = InvoiceProduct::with('customer');

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('status', $status);
        });

        $query->when($customerId !== '', function ($query) use ($customerId) {
            $query->where('customer_id', $customerId);
        });
        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('number', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('created_at', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('updated_at', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('send_date', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhereHas('customer', function ($q) use ($searchData) {
                      $q->where('name', 'LIKE', '%' . $searchData . '%');
                  });
            });
        });
        
        if ($orderParam !== 'customer.name') {
            $query->orderBy($orderParam, $orderType);
        } else {
            $query->with('customer')->orderBy(Customer::select('name')->whereColumn('customers.id', 'invoice_products.customer_id'), $orderType);
        }

        return $query->paginate($perPage);
    }

    public function delete(InvoiceProduct $invoiceProduct): void
    {
        $invoiceProduct->delete();
    }
}
