<?php

namespace App\Services\Admin\CustomerAgreement;

// use App\Models\CustomerAgreement;
use App\Models\Agreement;
use App\Models\Customer;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerAgreementService implements ICustomerAgreementService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer): LengthAwarePaginator
    {
        $query = Agreement::where(['customer_id' => $customer->id, 'custom' => 'custom']);
        $perPage = ($perPage != 0) ? $perPage : $query->count();
        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('type', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhere('file_name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhere('original_name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhere('location', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhere('status', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });
        $query->orderBy($orderParam, $orderType);
        return $query->paginate($perPage);
    }

    public function trimNumber(string $searchData): string
    {
        return ltrim($searchData, '0');
    }
}
 