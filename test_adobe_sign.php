<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\Admin\AdobeSign\AdobeSignService;
use App\Models\Customer;
use App\Models\License;
use App\Models\Company;
use App\Models\Agreement;

echo "=== Adobe Sign Integration Test ===\n\n";

try {
    // Test 1: Check Adobe Sign API connectivity
    echo "1. Testing Adobe Sign API connectivity...\n";
    $service = new AdobeSignService();
    $baseUri = $service->getBaseUri();
    echo "✓ Base URI retrieved: $baseUri\n\n";
    
    // Test 2: Check configuration
    echo "2. Checking configuration...\n";
    $accessToken = config('adobe-sign.access_token');
    $appUrl = env('APP_URL');
    $adobeSignName = env('ADOBE_SIGN_NAME');
    $adobeSignEmail = env('ADOBE_SIGN_EMAIL');
    
    echo "Access Token: " . (empty($accessToken) ? "❌ NOT SET" : "✓ SET") . "\n";
    echo "APP_URL: " . (empty($appUrl) ? "❌ NOT SET" : "✓ SET ($appUrl)") . "\n";
    echo "Adobe Sign Name: " . (empty($adobeSignName) ? "❌ NOT SET" : "✓ SET ($adobeSignName)") . "\n";
    echo "Adobe Sign Email: " . (empty($adobeSignEmail) ? "❌ NOT SET" : "✓ SET ($adobeSignEmail)") . "\n\n";
    
    // Test 3: Check template files
    echo "3. Checking template files...\n";
    $templateFiles = [
        'license-mini' => 'resources/views/adobe-files/license-mini.blade.php',
        'license-micro' => 'resources/views/adobe-files/license-micro.blade.php',
        'license-mega' => 'resources/views/adobe-files/license-mega.blade.php',
        'license-int-mini' => 'resources/views/adobe-files/license-int-mini.blade.php',
        'license-int-micro' => 'resources/views/adobe-files/license-int-micro.blade.php',
        'license-int-mega' => 'resources/views/adobe-files/license-int-mega.blade.php',
    ];
    
    foreach ($templateFiles as $name => $path) {
        if (file_exists($path)) {
            echo "✓ $name template exists\n";
        } else {
            echo "❌ $name template missing: $path\n";
        }
    }
    echo "\n";
    
    // Test 4: Check database connectivity
    echo "4. Testing database connectivity...\n";
    try {
        $customerCount = Customer::count();
        $licenseCount = License::count();
        $companyCount = Company::count();
        
        echo "✓ Database connected\n";
        echo "  - Customers: $customerCount\n";
        echo "  - Licenses: $licenseCount\n";
        echo "  - Companies: $companyCount\n\n";
        
        if ($customerCount > 0 && $companyCount > 0 && $licenseCount > 0) {
            // Test 5: Test HTML generation
            echo "5. Testing HTML template generation...\n";
            $customer = Customer::first();
            $company = Company::first();
            $license = License::first();

            if (!$license) {
                echo "❌ No licenses found in database\n";
            } else {
                echo "✓ Using license ID: {$license->id}\n";

                // Check if license has conversion rates
                $conversionRateCount = $license->conversionRate()->count();
                echo "  - Conversion rates: $conversionRateCount\n";

                if ($conversionRateCount === 0) {
                    echo "❌ License has no conversion rates - this will cause the error\n";
                    echo "  Creating conversion rates for testing...\n";

                    // Create conversion rates for all currencies
                    $currencies = \App\Models\Currency::all();
                    foreach ($currencies as $currency) {
                        $license->conversionRate()->create([
                            'currency_id' => $currency->id,
                            'rate' => \App\Helpers\CurrencyHelper::calculateRate($currency)
                        ]);
                    }
                    echo "✓ Conversion rates created\n";
                }

                $templateNames = Agreement::LICENSE_TEMPLATE_MINI;
                $prefillData = AdobeSignService::getPrefillData($license, 'license', $company, $templateNames);
            
            echo "✓ Prefill data generated\n";
            
            $fillHTML = AdobeSignService::fillHTML('license', $templateNames, $prefillData, $customer);
            
            if (!empty($fillHTML)) {
                echo "✓ HTML template generated: $fillHTML\n";
                
                // Check if the file was actually created
                $filePath = public_path($fillHTML);
                if (file_exists($filePath)) {
                    echo "✓ HTML file created successfully: $filePath\n";
                    $fileSize = filesize($filePath);
                    echo "  File size: $fileSize bytes\n";
                    
                    // Clean up test file
                    unlink($filePath);
                    echo "✓ Test file cleaned up\n";
                } else {
                    echo "❌ HTML file was not created: $filePath\n";
                }
            } else {
                echo "❌ HTML template generation failed\n";
            }
            }
        }

    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "If all tests pass, the issue might be with:\n";
    echo "1. Adobe Sign API rate limits\n";
    echo "2. Adobe Sign account permissions\n";
    echo "3. Network connectivity issues\n";
    echo "4. Specific customer/license data causing issues\n\n";
    echo "Check the Laravel logs for more detailed error information.\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
