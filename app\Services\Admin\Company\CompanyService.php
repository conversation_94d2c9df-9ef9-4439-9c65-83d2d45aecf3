<?php

namespace App\Services\Admin\Company;

use App\Helpers\FileHelper;
use App\Models\Company;
use Illuminate\Pagination\LengthAwarePaginator;

class CompanyService implements ICompanyService
{
    public function store(array $data)
    {
        $company = Company::create($data);

        if (isset($data['logo'])) {
            FileHelper::uploadImage(
                $company,
                'image',
                'admin/companies/' . $company->id,
                null,
                $data['logo']
            );
        }
    }

    public function update(array $data, Company $company)
    {
        $company->update($data);

        if (isset($data['logo_deleted']) && $data['logo_deleted'] == 'true') {
            FileHelper::deleteSingleFile($company->image->id);
        }

        if (isset($data['logo'])) {
            FileHelper::uploadImage(
                $company,
                'image',
                'admin/companies/' . $company->id,
                $company->image,
                $data['logo']
            );
        }
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator
    {
        $query = Company::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('status', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('id', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('address', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('address2', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('zip', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('city', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });

        return $query->paginate($perPage);
    }

    public function delete(Company $company)
    {
        $company->licenses()->delete();
        $company->leases()->delete();
        $company->delete();
    }
}
