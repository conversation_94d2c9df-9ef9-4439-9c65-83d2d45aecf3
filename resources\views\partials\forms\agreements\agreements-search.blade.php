@if (count($agreements) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Agreement') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="send_date" class="sortable-list-header" data-sort="send_date">{{ __('Send Date') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="singing_date" class="sortable-list-header" data-sort="singing_date">{{ __('Singing Date') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="completed_date" class="sortable-list-header" data-sort="completed_date">{{ __('Completed Date') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            @foreach ($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach ($agreements as $agreement)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <p class="my-0 hide-transp">{{ $agreement->sequential_id }}</p>
                <p class="my-0">Agreement #{{ $agreement->formatted_number }}</p>
                <p class="my-0">{{ \Carbon\Carbon::parse($agreement->send_date)->format('m/d/Y') }}</p>
                @if ($agreement->singing_date)
                    <p class="my-0">{{ \Carbon\Carbon::parse($agreement->singing_date)->format('m/d/Y') }}</p>
                @else
                    <p class="my-0">-</p>
                @endif
                @if ($agreement->completed_date)
                    <p class="my-0 text-center">
                        {{ \Carbon\Carbon::parse($agreement->completed_date)->format('m/d/Y') }}</p>
                @else
                    <p class="my-0">-</p>
                @endif
                @include('partials.status-badge', [
                    'status' => $agreement->status,
                ])
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-content" id="agreements-dropdown-content">
                        @if ($agreement->completed_date)
                            <a href="{{ route('admin.agreements.download', $agreement) }}">{{ __('Read Agreement') }} </a>
                        @else
                            <a href="javascript:void(0)" class="download-link disabled text-secondary"
                                style="pointer-events: none">{{ __('Download') }}</a>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
        <div id="paginate paginate-agreements">
            @if ($agreements)
                <div class="">
                    {!! $agreements->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
    <p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const agreementsCount = @json($agreements).total;
    const descriptiveLabel = agreementsCount === 1 ? ' Item' : ' Items';

    $('#agreementsCount').text(agreementsCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.download-link').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
