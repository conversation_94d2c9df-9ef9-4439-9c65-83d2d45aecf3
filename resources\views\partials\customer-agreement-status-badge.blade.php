@if( $status == \App\Models\Agreement::STATUSES['Active'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-success" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Active'] }}">
        {{ __('Active') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Signed (payment)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-secondary text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Signed (payment)'] }}">
        {{ __('Signed (payment)') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Awaiting Delivery'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Awaiting Delivery'] }}">
        {{ __('Awaiting Delivery') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Deposit Paid'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Deposit Paid'] }}">
        {{ __('Deposit Paid') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Signed 0/2'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-warning text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Signed 0/2'] }}">
        {{ __('Signed 0/2') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Signed 1/2'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-warning text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Signed 1/2'] }}">
        {{ __('Signed 1/2') }}
    </div>
@else
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-black text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Uploaded'] }}">
        {{ __('Uploaded') }}
    </div>
@endif