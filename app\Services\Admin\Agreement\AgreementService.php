<?php

namespace App\Services\Admin\Agreement;

use App\Models\Agreement;
use App\Models\Customer;
use App\Models\Invoice;
use Illuminate\Pagination\LengthAwarePaginator;

class AgreementService implements IAgreementService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer): LengthAwarePaginator
    {
        $number = self::trimNumber($searchData);

        $query = Agreement::where(['customer_id', $customer->id, 'custom' => 'custom']);

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData, $number) {
            $query->where('id', 'LIKE', '%' . $number . '%');
        });

        return $query->paginate($perPage);
    }

    public function searchAll(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator
    {
        $query = Agreement::with([
            'customer.owner',
            'customer.country',
            'lease.studio',
            'license.studio',
            'purchase.studio'
        ])->where('status', '!=', '13')->whereNot('custom', 'custom');

        if ($orderParam == 'customer.name') {
            $query->with('customer')->orderBy(Customer::select('name')->whereColumn('customers.id', 'agreements.customer_id'), $orderType);
        } else if ($orderParam == 'location') {
            // Order by studio name across lease, license, and purchase relationships
            $query->orderBy(function ($query) {
                $query->selectRaw('
                    CASE
                        WHEN agreements.lease_id IS NOT NULL THEN (
                            SELECT studios.name
                            FROM studios
                            JOIN leases ON leases.studio_id = studios.id
                            WHERE leases.id = agreements.lease_id
                        )
                        WHEN agreements.license_id IS NOT NULL THEN (
                            SELECT studios.name
                            FROM studios
                            JOIN licenses ON licenses.studio_id = studios.id
                            WHERE licenses.id = agreements.license_id
                        )
                        WHEN agreements.purchase_id IS NOT NULL THEN (
                            SELECT studios.name
                            FROM studios
                            JOIN purchase ON purchase.studio_id = studios.id
                            WHERE purchase.id = agreements.purchase_id
                        )
                        ELSE NULL
                    END
                ');
            }, $orderType);
        } else {
            $query->orderBy($orderParam, $orderType);
        }

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                // Search by customer name
                $q->whereHas('customer', function ($customerQuery) use ($searchData) {
                    $customerQuery->where('name', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer owner first name
                ->orWhereHas('customer.owner', function ($ownerQuery) use ($searchData) {
                    $ownerQuery->where('first_name', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer owner last name
                ->orWhereHas('customer.owner', function ($ownerQuery) use ($searchData) {
                    $ownerQuery->where('last_name', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer email (through owner relationship)
                ->orWhereHas('customer.owner', function ($ownerQuery) use ($searchData) {
                    $ownerQuery->where('email', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer address
                ->orWhereHas('customer', function ($customerQuery) use ($searchData) {
                    $customerQuery->where('address', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer city
                ->orWhereHas('customer', function ($customerQuery) use ($searchData) {
                    $customerQuery->where('city', 'LIKE', '%' . $searchData . '%');
                })
                // Search by customer country name
                ->orWhereHas('customer.country', function ($countryQuery) use ($searchData) {
                    $countryQuery->where('name', 'LIKE', '%' . $searchData . '%');
                })
                // Search by agreement type (license, lease, purchase)
                ->orWhere(function ($typeQuery) use ($searchData) {
                    $searchLower = strtolower($searchData);
                    if (str_contains($searchLower, 'license')) {
                        $typeQuery->whereNotNull('license_id');
                    } elseif (str_contains($searchLower, 'lease')) {
                        $typeQuery->whereNotNull('lease_id');
                    } elseif (str_contains($searchLower, 'purchase')) {
                        $typeQuery->whereNotNull('purchase_id');
                    }
                })
                // Search by studio/location name for licenses
                ->orWhereHas('license.studio', function ($studioQuery) use ($searchData) {
                    $studioQuery->where('name', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('address', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('city', 'LIKE', '%' . $searchData . '%');
                })
                // Search by studio/location name for leases
                ->orWhereHas('lease.studio', function ($studioQuery) use ($searchData) {
                    $studioQuery->where('name', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('address', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('city', 'LIKE', '%' . $searchData . '%');
                })
                // Search by studio/location name for purchases
                ->orWhereHas('purchase.studio', function ($studioQuery) use ($searchData) {
                    $studioQuery->where('name', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('address', 'LIKE', '%' . $searchData . '%')
                    ->orWhere('city', 'LIKE', '%' . $searchData . '%');
                });
            });
        });

        return $query->paginate($perPage);
    }

    public function trimNumber(string $searchData): string
    {
        return ltrim($searchData, '0');
    }


    public static function store(array $data): void
    {
        Agreement::create($data);
    }
    public function delete(Agreement $agreement)
    {
        $agreement->items()->delete();
        $agreement->delete();
    }

    public function deleteMultiple(array $ids)
    {
        foreach ($ids as $id) {
            $agreement = Agreement::find($id);
            $this->delete($agreement);
        }
    }

}
