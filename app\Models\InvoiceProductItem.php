<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceProductItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_product_id',
        'product_id',
        'price',
        'quantity',
        'discount',
        'deposit',
        'discount_type',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // Touch the parent InvoiceProduct whenever an item is created, updated, or deleted
        static::created(function (InvoiceProductItem $item) {
            $item->invoice?->touch();
        });

        static::updated(function (InvoiceProductItem $item) {
            $item->invoice?->touch();
        });

        static::deleted(function (InvoiceProductItem $item) {
            $item->invoice?->touch();
        });
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(InvoiceProduct::class, 'invoice_product_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }
}
