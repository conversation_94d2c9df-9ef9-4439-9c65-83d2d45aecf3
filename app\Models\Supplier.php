<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'email2',
        'phone',
        'address',
        'address2',
        'city',
        'state_id',
        'country_id',
        'zip',
        'is_active',
        'location',
    ];

    public function contact(): MorphOne
    {
        return $this->morphOne(ContactPerson::class, 'contactable');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
