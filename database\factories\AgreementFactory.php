<?php

namespace Database\Factories;

use App\Models\Agreement;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Agreement>
 */
class AgreementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_id' => Customer::factory(),
            'adobe_template_id' => $this->faker->uuid(),
            'adobe_agreement_id' => $this->faker->uuid(),
            'send_date' => $this->faker->date(),
            'singing_date' => null,
            'completed_date' => null,
            'status' => $this->faker->randomElement(['0', '1', '2', '3']),
            'type' => $this->faker->randomElement(['license', 'lease', 'purchase', 'custom']),
            'file_name' => $this->faker->word() . '.pdf',
            'original_name' => $this->faker->word() . '.pdf',
            'location' => $this->faker->city(),
            'custom' => 'auto',
        ];
    }
}
