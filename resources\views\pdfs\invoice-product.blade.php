<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoiceProduct->formatted_number }}</title>
    <style>
        /* Add your custom styling here */
        body {
            font-family: Graphik, sans-serif;
            font-size: 14px;
            margin-top: 20px;
            margin-left: 30px;
            margin-right: 30px;
        }
        /* .data-table,
        .header-table {
            width: 100%;
            max-width: 800px;
            margin: auto;
        } */
        .header-table, .items-table, .note-table {
            width: 100%;
            border-collapse: collapse;
        }
        .header-table td, .items-table th, .items-table td, .note-table td {
            padding: 8px;
        }
        .logo-cell {
            width: 20%;
            text-align: left;
        }
        .logo-cell img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }
        .items-table {
            margin-top: 35px;
            /*border: 1px solid #ddd;*/
        }
        .items-table th, .items-table td {
            border-top: 1px solid #F0F0F0;
            border-bottom: 1px solid #F0F0F0;
            padding-bottom: 15px;
            padding-top: 15px;
            text-align: left;
            font-size: 16px;
        }

        .items-table th:nth-child(1), .items-table td:nth-child(1) {
            width: 20px; /* Set width for the ID column */
        }

        .items-table th:nth-child(2), .items-table td:nth-child(2) {
            width: auto; /* Let the Item column take up the remaining space */
        }

        .items-table th:nth-child(3), .items-table td:nth-child(3) {
            width: 30px; /* Set width for the Quantity column */
            text-align: center; /* Align Quantity column to the right */

        }
        .items-table th:nth-child(5), .items-table td:nth-child(5) {
            /* width: 30px; Set width for the Quantity column */
            text-align: right; /* Align Quantity column to the right */

        }

        .note-table {
            margin-top: 30px;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .note-table td {
            padding: 10px;
            vertical-align: top;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
            min-height: 50px; /* Adjust as needed */
            overflow: auto;
            font-size: 12px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-family: Arial, sans-serif;
        }

        .data-table th, .data-table td {
            font-weight: normal;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #F0F0F0;
            border-top: 1px solid #F0F0F0;
        }

        .data-table th {
            font-size: 12px;
            color: #000000;
            padding: 10px;
        }

        .data-table td {
            font-size: 14px;
            color: #000000;
            padding: 10px;
        }

        .data-table tbody tr td[colspan="6"] {
            font-size: 16px;
            /*padding: 20px;*/
            background-color: #f9f9f9;
        }

        .note {
            font-size: 10px;
            white-space: pre-wrap;
        }

        .page-break {
            page-break-after: always;
        }
        .light-line {
            width: 100%;
            border-bottom: 1px solid #F0F0F0;
        }
    </style>
</head>
<body>
<!-- Header Section -->
{{-- {{ dd($invoiceProduct->customer) }} --}}
<table class="header-table">
    <tr>
        <td class="logo-cell">
            <img src="{{$invoiceProduct->company?->image?->path ? public_path('/storage/' . $invoiceProduct->company->image->path) : public_path('/img_placeholder.svg') }}" alt="Company Logo">
            {{-- <img src="{{$invoiceProduct->company?->image?->path ? URL::asset('/storage/' . $invoiceProduct->company->image->path) : URL::asset('/img_placeholder.svg') }}" alt="Company Logo"> --}}
        </td>
        @if($invoiceProduct->company)
        <td class="info-cell">
            <strong>{{$invoiceProduct->company->name}}</strong><br>
            <span>{{$invoiceProduct->company->address}}</span><br>
            <span>{{$invoiceProduct->company->phone}}</span>
        </td>
        @endif
        <td style="text-align:right;">
            <strong style="font-size: 14px;"><span>Invoice #{{$invoiceProduct->formatted_number}}</span></strong><br>
            <span>
                Invoice Date: 
                @if($invoiceProduct->updated_at) 
                    {{\Carbon\Carbon::parse($invoiceProduct->updated_at)->format('m/d/Y')}} 
                @else 
                    {{\Carbon\Carbon::parse($invoiceProduct->send_date)->format('m/d/Y')}}
                @endif
            </span>
        </td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 15px; margin-top: 15px">
    <tr>
        <td></td>
    </tr>
</table>
@if ($invoiceProduct->customer)
<table class="company-table">
    <tr>
        <td style="width: 50%">
            <strong>Bill To</strong><br>
            <span>{{ $invoiceProduct->customer?->name ? $invoiceProduct->customer?->name : 'CUSTOMER' }}</span><br>
            <span>{{ $invoiceProduct->customer?->address }}</span><br>
            <span>{{ $invoiceProduct->customer?->zip . ', ' . $invoiceProduct->customer?->city }}, {{($invoiceProduct->customer?->location === \App\Helpers\Constants::LOCATION_TYPE[0]) ? $invoiceProduct->customer?->state?->name : $invoiceProduct->customer?->country?->name }}</span><br>
            <span>{{ $invoiceProduct->customer?->owner?->phone }}</span>
        </td>
        <td style="width: 100px">&nbsp;</td>
        <td style="width: 50%">
            <strong>Ship To</strong><br>
            <span>{{ $invoiceProduct->customer?->name ? $invoiceProduct->customer?->name : 'CUSTOMER' }}</span><br>
            @if ($invoiceProduct->customer?->use_billing_address == '1')
                <span>{{ $invoiceProduct->customer?->address }}</span><br>
                <span>{{ $invoiceProduct->customer?->zip . ', ' . $invoiceProduct->customer?->city }}, {{($invoiceProduct->customer?->location === \App\Helpers\Constants::LOCATION_TYPE[0]) ? $invoiceProduct->customer?->state?->name : $invoiceProduct->customer?->country?->name }}</span><br>
                <span>{{ $invoiceProduct->customer?->owner?->phone }}</span>
            @else
                <span>{{ $invoiceProduct->customer?->shipping_address ?? $invoiceProduct->customer?->address}}</span><br>
                <span>{{ ($invoiceProduct->customer?->shipping_zip ?? $invoiceProduct->customer?->zip) . ', ' . ($invoiceProduct->customer?->shipping_city ?? $invoiceProduct->customer?->city) }}, {{ ($invoiceProduct->customer?->shipping_location === \App\Helpers\Constants::LOCATION_TYPE[0]) ? $invoiceProduct->customer?->shipping_state?->name : ($invoiceProduct->customer?->shipping_country?->name ?? $invoiceProduct->customer?->country?->name) }}</span><br>
                <span>{{ $invoiceProduct->customer?->owner?->phone }}</span>
            @endif
        </td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 15px; margin-top: 15px">
    <tr>
        <td></td>
    </tr>
</table>
@endif

@if ($invoiceProduct->items)
<h4>ORDER ITEMS</h4>

<!-- Order Items Section -->
<table class="data-table">
    <thead>
    <tr>
        <th><b>ID</b></th>
        <th><b>ITEM</b></th>
        <th><b>UNIT PRICE</b></th>
        <th><b>QTY</b></th>
        <th><b>DISCOUNT</b></th>
        <th><b>DEPOSIT</b></th>
        <th style="text-align:right;"><b style="text-align:right;">TOTAL</b></th>
    </tr>
    </thead>
    <tbody>
        @php
            $discount_sum = 0;
            $deposit_sum = 0;
            $c=0;
        @endphp
    @foreach($invoiceProduct->items as $item)
        @php
            $discount_val = 0;
            $c++;
            $discount = (int)($item->discount_type == '%' ? number_format($item->price * $item->quantity * ($item->discount / 100), 2, '.', ',') . '%' : '$' . number_format($item->discount * $item->quantity, 2, '.', ','));
            $discount_val = (int)($item->discount_type == '%' ? $item->price * $item->quantity * ($item->discount / 100) : $item->discount);
            $discount_sum += $discount_val;
            $deposit_sum += $item->deposit;
        @endphp
        <tr>
            <td>{{$c}}</td>
            <td>{{$item->product->name}}</td>
            <td>${{ number_format($item->price, 2, '.', ',') }}</td>
            <td>{{$item->quantity}}</td>
            <td>{{ $item->discount_type == '%' ? $item->discount . '%' : '$' . $item->discount}}</td>
            <td>${{ number_format($item->deposit, 2, '.', ',') }}</td>
            <td style="padding-right: 0px"><span  style="text-align:right;display: block; width: 100%">${{ number_format($item->price * $item->quantity, 2, '.', ',') }}</span></td>
        </tr>
    @endforeach
    </tbody>
</table>

@php
    $tax = $invoiceProduct->show_tax == 1 ? 0.095 : 0;
    $subTotal = $invoiceProduct->items->map(function ($item) {
        return $item->price * $item->quantity;
    })->sum()
@endphp
<table style="margin-top: 20px; margin-right: 0; margin-left: 0; float:right; border-collapse: collapse; text-align: right;">
    <tr>
        <td style="padding: 5px; font-weight: bold;">Sub-Total:</td>
        <td style="padding: 5px 0px 5px 5px;">
            ${{ number_format($subTotal, 2, '.', ',') }}
        </td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">Discount:</td>
        <td style="padding: 5px 0px 5px 5px;">- ${{number_format($discount_sum, 2, '.', ',')}}</td>
    </tr>
    @if($invoiceProduct->shipping_fee OR $invoiceProduct->handling_fee)
    <tr>
        <td style="padding: 5px; font-weight: bold;">Shipping & Handling:</td>
        <td style="padding: 5px 0px 5px 5px;">
            @if($invoiceProduct->shipping_fee OR $invoiceProduct->handling_fee)
                ${{number_format(($invoiceProduct->shipping_fee + $invoiceProduct->handling_fee), 2, '.', ',')}}
            @endif
        </td>
    </tr>
    @endif
    @if($invoiceProduct->show_tax == 1)
    <tr>
        <td style="padding: 5px; font-weight: bold;">Tax:</td>
        <td style="padding: 5px 0px 5px 5px;">${{number_format(($subTotal - $discount_sum) * $tax, 2, '.', ',')}}</td>
    </tr>
    @endif
    <tr>
        <td style="padding: 5px; font-weight: bold;">Deposit:</td>
        <td style="padding: 5px 0px 5px 5px;">- ${{number_format($deposit_sum, 2, '.', ',')}}</td>
    </tr>
    <tr>
        <td colspan="2" style="height: 20px;"></td>
    </tr>
    <tr>
        <td class="light-line"></td>
        <td class="light-line"></td>
    </tr>
    <tr>
        <td colspan="2" style="height: 20px;"></td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">Grand Total:</td>
        <td style="padding: 5px 0px 5px 5px;">
            ${{number_format($subTotal - $discount_sum - $deposit_sum + (($subTotal - $discount_sum) * $tax) + $invoiceProduct->shipping_fee + $invoiceProduct->handling_fee, 2, '.', ',')}}
        </td>
    </tr>
    @if($invoiceProduct->paid == 1)
    <tr>
        <td colspan="2" style="padding-top: 20px;padding-bottom: 30px;">
            <img src="{{ public_path('/paid-invoice.svg') }}" alt="" style="width: 95px;height: 37px;" />
        </td>
    </tr>
    @endif
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 330px">
    <tr>
        <td></td>
    </tr>
</table>
@endif

@if($invoiceProduct->note)
    <table class="invoice-table">
        <tr>
            <td class="header"><b>NOTE</b></td>
        </tr>
        <tr>
            <td class="terms">
                {{$invoiceProduct->note}}
            </td>
        </tr>
    </table>
@endif

</body>
</html>
