<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
// use App\Models\CustomerAgreement;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    protected $fillable = [
        'name',
        'licensee2_first_name',
        'licensee2_last_name',
        'owner_id',
        'address',
        'address2',
        'city',
        'state_id',
        'country_id',
        'zip',
        'location',
        'type',
        'use_billing_address',
        'shipping_address',
        'shipping_city',
        'shipping_state_id',
        'shipping_country_id',
        'shipping_zip',
        'shipping_location'
    ];

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'users'   => ['id', 'customers.owner_id'],
    ];

    public function sortableFields(): array
    {
        return [
            'id',
            'name',
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function getPrimaryAndForeignKeys(string $relation)
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id')
            ->select(['id', 'first_name', 'last_name', 'email', 'email2', 'phone', 'is_active']);
    }

    public function notes(): HasMany
    {
        return $this->hasMany(Note::class);
    }

    public function licenses(): HasMany
    {
        return $this->hasMany(License::class);
    }

    public function leases(): HasMany
    {
        return $this->hasMany(Lease::class);
    }

    public function purchases(): HasMany
    {
        return $this->hasMany(Purchase::class);
    }

    public function invoiceProducts(): HasMany
    {
        return $this->hasMany(InvoiceProduct::class);
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Countries::class);
    }
    public function shipping_state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function shipping_country(): BelongsTo
    {
        return $this->belongsTo(Countries::class);
    }

    // public function agreements(): HasMany
    // {
    //     return $this->hasMany(CustomerAgreement::class, 'customer_id');
    // }
    public function agreements(): HasMany
    {
        return $this->hasMany(Agreement::class, 'customer_id');
    }

    // public function files()
    // {
    //     return $this->hasMany(CustomerAgreement::class);
    // }
    public function files()
    {
        return $this->hasMany(Agreement::class);
    }

    // public function studio(): HasOne
    // {
    //     return $this->hasOne(Studio::class);
    // }
}
