@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('new lease') }}</h3>
            <a href="{{ route('admin.customers.dashboard', $customer) }}" class="back-link">← Back</a>
        </div>
    </div>
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.leases.store', $customer) }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('Lagree Company') }}</h5>

            @include('partials.forms.select', [
              'field_name' => 'company_id',
              'field_label' => null,
              'values' => $companies,
              'field' => 'state',
              'option_key' => 'id',
              'option_label' => 'name',
              'field_value' => old('company_id'),
              'field_label_class' => 'd-none',
              'include_empty' => true,
            ])

            <h5 class="form-section-title">{{ __('Select Location') }}</h5>

            @include('partials.forms.select', [
               'field_name' => 'studio_id',
               'field_label' => null,
               'values' => $studios,
               'field' => 'state',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('studio_id', NULL),
               'field_label_class' => 'd-none',
               'include_empty' => true,
               'field_id' => 'lease_id'
           ])

            <h5 class="form-section-title">{{ __('LEASE info') }}</h5>

            @include('partials.forms.input',  [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Select',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', NULL),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field'
            ])

            @include('partials.forms.select', [
                'field_name' => 'machine_id',
                'field_label' => 'MACHINE *',
                'values' => $machines,
                'field' => 'machine',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('machine_id', NULL),
                'include_empty' => true,
            ])
            
            <div class="parent_machines_field" @if(old('configuration_id') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'configuration_id',
                    'field_label' => 'CONFIGURATION *',
                    'values' => NULL,
                    'field' => 'CONFIGURATION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('configuration', NULL),
                    'include_empty' => true,
                ])
            </div>

            <div class="condition_field" @if(old('condition') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'condition',
                    'field_label' => 'CONDITION *',
                    'values' => ['new' => 'New', 'used' => 'Used', 'restored' => 'Restored'],
                    'field' => 'CONDITION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('condition', NULL),
                    'include_empty' => true,
                ])
            </div>

            @include('partials.forms.input', [
                'field_name' => 'machine_price',
                'field_label' => 'UNIT PRICE *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'field_value' => old('machine_price', NULL),
                'input_field_class' => 'decimal-field'
            ])

            @include('partials.forms.input', [
                'field_name' => 'monthly_installment',
                'field_label' => 'MONTHLY INSTALLMENT *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'field_value' => old('monthly_installment', NULL),
                'input_field_class' => 'decimal-field'
            ])

            @include('partials.forms.input', [
                'field_name' => 'machine_quantity',
                'field_label' => '# OF MACHINES *',
                'placeholder' => '0',
                'field_type' => 'text',
                'field_right_mark' => 'qty',
                'field_value' => old('machine_quantity', NULL),
                'input_field_class' => 'integer-field'
            ])

            @include('partials.forms.input', [
                'field_name' => 'duration',
                'field_label' => 'DURATION *',
                'field_type' => 'text',
                'placeholder' => '0',
                'field_right_mark' => 'months',
                'field_value' => old('duration', NULL),
                'input_field_class' => 'integer-field'
            ])

            @include('partials.forms.input', [
               'field_name' => 'deposit_amount',
               'field_label' => 'DEPOSIT (PER UNIT) *',
               'field_type' => 'text',
               'placeholder' => '0',
               'currency' => true,
               'field_value' => old('deposit_amount', NULL),
               'input_field_class' => 'decimal-field'
            ])

            @include('partials.forms.input', [
               'field_name' => 'buy_out',
               'field_label' => 'BUY-OUT AMOUNT *',
               'field_type' => 'text',
               'placeholder' => '0',
               'currency' => true,
               'field_value' => old('buy_out', NULL),
               'input_field_class' => 'decimal-field'
            ])

            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', NULL),
            ])

            @include('partials.forms.input',  [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d')
            ])

            @include('partials.forms.input',  [
                'field_name' => 'status',
                'field_type' => 'hidden',
                'field_value' => 0
            ])

            <div class="w100 border-top mt-7">
                <p class="lh-1 mt-7 ">Monthly installment: <span class="monthly_installment_val">$0</span></p>
                <p class="lh-1 deposit-wrap">Deposit: <span class="deposit_val">$0</span></p>
            </div>

            <div class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary" onclick="$(this).addClass('btn--loading-small')">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.leases.create', $customer)}}" class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <div class="ms-auto d-flex align-items-center gap-3">
                    <span class="agreements-err"></span>
                    <a type="button" href="javascript:;" class="btn cancel-btn preview_agreement" style="margin-left: auto !important">{{ __('PREVIEW AGREEMENT') }}</a>
                </div>
            </div>

        </form>
    </div>
    <div class="agreement_popup_overlay" style="display: none;" onclick="$('.agreement_popup_overlay').fadeOut();">
        <div class="agreement_popup">
            <span class="close-popup" onclick="$('.agreement_popup_overlay').fadeOut();">×</span>
            <div class="agreement-html"></div>
        </div>
    </div>

@endsection
@push('scripts')
<script>
    var selected_machine = [];
    var selected_machine_conf = [];
    document.addEventListener('DOMContentLoaded', function () {
        @if (old("configuration_id"))
        setTimeout(function(){
            $('#machine_id-machine').trigger('change');        
        }, 500);
        @endif
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        function calculate_installments_deposit(){
            console.log('ff calculate_installments_deposit');
            
            var deposit = parseInt($('#deposit_amount').val());
            var monthly_installment = parseInt($('#monthly_installment').val());
            var qty = $('#machine_quantity').val() != '' ? $('#machine_quantity').val() : 1;
            
            console.log('deposit', deposit);
            console.log('monthly_installment', monthly_installment);
            console.log('qty', qty);
                        
            $('.deposit_val').html('$' + (deposit * qty) + ' ($' + deposit + ' x ' + qty + ')').addClass('text-success');
            $('.monthly_installment_val').html('$' + (monthly_installment * qty) + ' ($' + monthly_installment + ' x ' + qty + ')').addClass('text-success');
        }

        $('#machine_quantity').on('change', function() { calculate_installments_deposit(); });
        $('#deposit_amount').on('change', function() { calculate_installments_deposit(); });
        $('#monthly_installment').on('change', function() { calculate_installments_deposit(); });

        $('#configuration_id-CONFIGURATION').change(function() {
            var machine_id = $(this).val();
            let url = '{{ route('admin.machines.get-machine') }}';

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                }
            });
            $.ajax({
                url: url,
                method: 'POST',
                dataType: 'json',
                data: {
                    machine_id
                },
                success: function(data) {
                    console.log('conf:', data);                        
                    if (data.success) {
                        selected_machine_conf = data.machine;
                    }
                },
                error: function() {
                    // flasherJS.error('', 'Error occurred while loading data.');
                }
            });
        });
        $('#condition-CONDITION').change(function() {
            var val = $(this).val();

            console.log('val: ', val);
            
            if(val == 'Used' || val == 'Restored'){
                $('#machine_price').val('');
                $('#duration').val('');
                $('#machine_quantity').val('');
                $('#monthly_installment').val('');
                $('#deposit_amount').val('');
                $('.deposit_val').html('$0').removeClass('text-success');
                $('.monthly_installment_val').html('$0').removeClass('text-success');
            }else if(val == 'New' && selected_machine_conf){
                $('#machine_price').val(selected_machine_conf.price);
                $('#machine_quantity').val(1);
                $('#duration').val(selected_machine_conf.lease_duration);
                $('#monthly_installment').val(selected_machine_conf.monthly_installment);
                $('#deposit_amount').val(selected_machine_conf.lease_deposit);
                setTimeout(function(){
                    calculate_installments_deposit();
                }, 300);
            }
        });
        $('#machine_id-machine').change(function() {
            let machine_id = $(this).val();
            let for_conf = ["8","3","4"];

            console.log(machine_id);
            
            if (machine_id) {
                $('.condition_field').show();
                
                let url = '{{ route('admin.machines.get-machine') }}';

                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    }
                });
                $.ajax({
                    url: url,
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        machine_id
                    },
                    success: function(data) {
                        console.log(data);                        
                        if (data.success) {
                            selected_machine = data.machine;
                            if($.inArray(machine_id, for_conf) !== -1){
                                $('.parent_machines_field').show();
                                if (data.conf_machines.length) {
                                    $('#configuration_id-CONFIGURATION').html('');                                        
                                    $('#configuration_id-CONFIGURATION').append('<option></option>');
                                    $.each(data.conf_machines, function(index, single_machine){
                                        var sel = single_machine.id == '{{ old("configuration_id") }}' ? "SELECTED" : "";
                                        $('#configuration_id-CONFIGURATION').append('<option value="' + single_machine.id + '" ' + sel + '>' + single_machine.name + '</option>');
                                    });
                                    // $("select").select2("destroy").select2();
                                    $('#configuration_id-CONFIGURATION').select2("destroy").select2({
                                        minimumResultsForSearch: 10,
                                        placeholder: "Select",
                                        closeOnSelect: true
                                    });
                                }
                            }else{
                                $('.parent_machines_field').hide();
                            }
                        }
                    },
                    error: function() {
                        flasherJS.error('', 'Error occurred while loading data.');
                    }
                });
            }
        });
        $('.preview_agreement').on('click', function() {
            $('.preview_agreement').addClass('btn--loading-small-black');
            $('.agreements-err').text('').hide();
            var tempName = "IMS-LEASE-FINAL";
            var file = "lease";
            var company_name = $('#company_id-state option:selected').text().trim();
            var studio_name = $('#studio_id-state option:selected').text().trim();
            var studio_id = $('#studio_id-state option:selected').val();
            var deposit = $('#deposit_amount').val() != 0 ? $('#deposit_amount').val() : 0;
            var monthly_per_machine = $('#monthly_installment').val() != 0 ? $('#monthly_installment').val() : 0;
            var months_count = $('#duration').val() != 0 ? $('#duration').val() : 0;
            var installment_cost = $('#monthly_installment').val() != 0 ? $('#monthly_installment').val() * $('#machine_quantity').val() : 0;
            var deposit_price = $('#deposit_amount').val() != 0 ? $('#deposit_amount').val() : 0;
            var deposit_total = $('#deposit_amount').val() != 0 ? $('#deposit_amount').val() * $('#machine_quantity').val() : 0;
            var machine_quantity = $('#machine_quantity').val() != 0 ? $('#machine_quantity').val() : 0;
            var machine_name = $('#machine_id-machine option:selected').text().trim();
            var buy_out = $('#buy_out').val();

            // Enhanced debugging with length and type info
            console.log('company_name: "' + company_name + '" (length: ' + company_name.length + ', trimmed: "' + company_name.trim() + '")');
            console.log('studio_name: "' + studio_name + '" (length: ' + studio_name.length + ', trimmed: "' + studio_name.trim() + '")');
            console.log('deposit: "' + deposit + '" (type: ' + typeof deposit + ')');
            console.log('monthly_per_machine: "' + monthly_per_machine + '" (type: ' + typeof monthly_per_machine + ')');
            console.log('months_count: "' + months_count + '" (type: ' + typeof months_count + ')');
            console.log('installment_cost: "' + installment_cost + '" (type: ' + typeof installment_cost + ')');
            console.log('deposit_price: "' + deposit_price + '" (type: ' + typeof deposit_price + ')');
            console.log('deposit_total: "' + deposit_total + '" (type: ' + typeof deposit_total + ')');
            console.log('machine_name: "' + machine_name + '" (type: ' + typeof machine_name + ')');
            console.log('machine_quantity: "' + machine_quantity + '" (type: ' + typeof machine_quantity + ')');
            console.log('buy_out: "' + buy_out + '" (type: ' + typeof buy_out + ')');

            console.log('tempName: ', tempName);
            console.log('file: ', file);

            // Helper function to check if value is valid
            function isValidValue(value) {
                return value !== null && value !== undefined && value !== '' &&
                        String(value).trim() !== '' && !String(value).includes('Select');
            }

            // Individual condition checks
            var conditions = {
                company_name: isValidValue(company_name),
                studio_name: isValidValue(studio_name),
                monthly_per_machine: isValidValue(monthly_per_machine) && parseFloat(monthly_per_machine) > 0,
                months_count: isValidValue(months_count) && parseFloat(months_count) > 0,
                installment_cost: isValidValue(installment_cost) && parseFloat(installment_cost) >= 0,
                deposit_price: isValidValue(deposit_price) && parseFloat(deposit_price) >= 0,
                deposit_total: isValidValue(deposit_total) && parseFloat(deposit_total) >= 0,
                machine_quantity: isValidValue(machine_quantity) && parseFloat(machine_quantity) > 0,
                machine_name: isValidValue(machine_name),
                buy_out: isValidValue(buy_out) && parseFloat(buy_out) >= 0,
                file: isValidValue(file),
                tempName: isValidValue(tempName),
            };

            console.log('Individual conditions:', conditions);

            var result = conditions.company_name && 
                         conditions.studio_name && 
                         conditions.monthly_per_machine &&
                         conditions.months_count &&
                         conditions.installment_cost &&
                         conditions.deposit_price &&
                         conditions.deposit_total &&
                         conditions.machine_name &&
                         conditions.machine_quantity &&
                         conditions.buy_out &&
                         conditions.tempName &&
                         conditions.file


            console.log('FINAL RESULT: ', result);

            // Additional debugging for common issues
            if (!result) {
                console.log('=== DEBUGGING FALSE RESULT ===');
                setTimeout(function(){
                    $('.preview_agreement').removeClass('btn--loading-small-black');
                }, 300);
                if (!conditions.company_name) {
                    $('.agreements-err').text('Company name is empty or invalid').show();
                    return false;
                }
                if (!conditions.studio_name) {
                    $('.agreements-err').text('Studio name is empty or invalid').show();
                    return false;
                }
                if (!conditions.monthly_per_machine) {
                    $('.agreements-err').text('Monthly Installment is empty or invalid.').show();
                    return false;
                }
                if (!conditions.months_count) {
                    $('.agreements-err').text('Duration is empty or invalid').show();
                    return false;
                }
                if (!conditions.installment_cost) {
                    $('.agreements-err').text('Monthly Installment is empty or invalid').show();
                    return false;
                }
                if (!conditions.deposit_price) {
                    $('.agreements-err').text('Deposit is empty or invalid').show();
                    return false;
                }
                if (!conditions.deposit_total) {
                    $('.agreements-err').text('Deposit or Duration field is empty or invalid').show();
                    return false;
                }
                if (!conditions.machine_name) {
                    $('.agreements-err').text('Please select Machine').show();
                    return false;
                }
                if (!conditions.machine_quantity) {
                    $('.agreements-err').text('# of machines is empty or invalid').show();
                    return false;
                }
                if (!conditions.buy_out) {
                    $('.agreements-err').text('Buy-out field is empty or invalid').show();
                    return false;
                }
            }

            if(result){
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    }
                });
                $.ajax({
                    type: 'POST',
                    url: '{{ route('admin.agreements.preview-agreement') }}',
                    data: {
                        customer_id: {{ $customer->id }},
                        company_name,
                        studio_name,
                        studio_id,
                        monthly_per_machine,
                        months_count,
                        installment_cost,
                        deposit_price,
                        deposit_total,
                        machine_quantity,
                        machine_name,
                        buy_out,
                        templateNames: tempName,
                        type: 'lease',
                        file: file,
                    },
                    dataType: 'json',
                    success: function(data) {
                        console.log(data);
                        console.log('SUCCESS');
                        $('.agreement-html').html('');
                        if (data.success) {
                            $('.preview_agreement').removeClass('btn--loading-small-black');
                            $('.agreement_popup_overlay').fadeIn();
                            var rep1 = (data.html).replace("@{{Sig_es_:signer1:signature}}", "  ").replace("@{{Dte_es_:signer1:date}}", "  ").replace("@{{Sig_es_:signer2:signature}}", "  ").replace("@{{Dte_es_:signer2:date}}", "  ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ");
                            $('.agreement-html').html(rep1);
                        }else{
                            $('.agreements-err').html('There was a problem generating agreement preview. <br>Please try again.').show();
                            $('.preview_agreement').removeClass('btn--loading-small-black');
                        }
                    },
                    error: function(request, status, error) {
                        console.log('PHP Error');
                        $('.agreements-err').html('There was a problem generating agreement preview. <br>Please try again.').show();
                        $('.preview_agreement').removeClass('btn--loading-small-black');
                    }
                });
            }else{
                $('.agreements-err').html('Preview can not generate unless all fields are valid.').show();
                $('.preview_agreement').removeClass('btn--loading-small-black');
            }
        });
        $('.agreement_popup').on('click', function(e){
            e.stopPropagation();
        });
    });
</script>
@endpush
