@extends('layouts.app')
@section('content')
    <div class="page-title ttl-dropdown no-border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ $customer->name }}</h3>
                <span class="ms-3">
                    @include('partials.status-badge', [
                        'status' => $customer->owner->is_active ? 'active' : 'inactive',
                    ])
                </span>
            </div>
            <a href="{{ route('admin.customers.studio.index') }}" class="back-link">← Back</a>
        </div>
        @if ($customer->type !== 'b2c')
            <div id="add-license-button" class="ms-auto d-none d-sm-inline-block">
                <button type="button" class="btn btn-primary ms-auto d-none d-sm-inline-block no-uppercase-no-bold ">
                    {{ __('ADD NEW') }} <span class="dropdown-arrow">&#9662;</span>
                </button>
                <div id="add-license-button-dropdown" style="max-width: 250px;">
                    <a href="{{ route('admin.licenses.create', ['customer' => $customer->id]) }}">License</a>
                    <a href="{{ route('admin.leases.create', ['customer' => $customer->id]) }}">Lease</a>
                    <a href="{{ route('admin.purchases.create', ['customer' => $customer->id]) }}">Purchase</a>
                    <a style="white-space: nowrap; cursor:pointer;" data-bs-toggle="modal"
                        data-bs-target="#addUploadAgreement">Upload agreement</a>
                </div>
            </div>
            <div class="ms-auto d-flex d-sm-none">
                <div class="custom-round-button-dropdown">
                    <button class="custom-dropbtn btn btn-primary float-right rounded-5">+</button>
                    <div class="custom-dropdown-content" id="customers-dropdown-content">
                        <a href="{{ route('admin.licenses.create', ['customer' => $customer->id]) }}">License</a>
                        <a href="{{ route('admin.leases.create', ['customer' => $customer->id]) }}">Lease</a>
                        <a href="{{ route('admin.purchases.create', ['customer' => $customer->id]) }}">Purchase</a>
                        <a href="javascript:;" style="white-space: nowrap; cursor:pointer;" data-bs-toggle="modal"
                            data-bs-target="#addUploadAgreement">Upload agreement</a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <div class="nav-tabs-container" id="nav-tabs-customers">
        <ul class="nav nav-tabs lh-1 mx-n12" id="myTab" role="tablist">
            @if ($customer->type === 'b2c')
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link active ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="profile-tab"
                        data-bs-toggle="tab" data-bs-target="#profile" data-tab="profile" type="button" role="tab"
                        aria-controls="profile" aria-selected="false">{{ __('profile') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="payment-history-tab"
                        data-bs-toggle="tab" data-bs-target="#payment-history" data-tab="payment-history" type="button"
                        role="tab" aria-controls="payment-history"
                        aria-selected="false">{{ __('payment history') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="invoices-tab"
                        data-bs-toggle="tab" data-bs-target="#invoices" data-tab="invoices" type="button" role="tab"
                        aria-controls="invoices" aria-selected="false">{{ __('invoices') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase semibold text-uppercase" id="notes-tab"
                        data-bs-toggle="tab" data-bs-target="#notes" data-tab="notes" type="button" role="tab"
                        aria-controls="notes"
                        aria-selected="false">{{ __('Internal Notes' . ' (' . $notesCount . ')') }}</button>
                </li>
            @else
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link active ps-0 ls-0.6px px-2 pe-sm-3 semibold text-uppercase" id="dashboard-tab"
                        data-bs-toggle="tab" data-bs-target="#dashboard" data-tab="dashboard" type="button" role="tab"
                        aria-controls="dashboard" aria-selected="true">{{ __('dashboard') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="licence-tab"
                        data-bs-toggle="tab" data-bs-target="#licence" data-tab="licence" type="button" role="tab"
                        aria-controls="licence"
                        aria-selected="false">{{ __('Locations') . ' ' . '(' . $licenseCount . ')' }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="lease-tab"
                        data-bs-toggle="tab" data-bs-target="#lease" data-tab="lease" type="button" role="tab"
                        aria-controls="lease" aria-selected="false">{{ __('leases') . ' (' . $leaseCount . ')' }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="purchases-tab"
                        data-bs-toggle="tab" data-bs-target="#purchases" data-tab="purchases" type="button"
                        role="tab" aria-controls="purchases" aria-selected="false">{{ __('purchases') . ' (' . $purchaseCount . ')' }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="invoices-tab"
                        data-bs-toggle="tab" data-bs-target="#invoices" data-tab="invoices" type="button"
                        role="tab" aria-controls="invoices" aria-selected="false">{{ __('invoices') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="payment-history-tab"
                        data-bs-toggle="tab" data-bs-target="#payment-history" data-tab="payment-history" type="button"
                        role="tab" aria-controls="payment-history"
                        aria-selected="false">{{ __('payment history') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="agreements-tab"
                        data-bs-toggle="tab" data-bs-target="#agreements" data-tab="agreements" type="button"
                        role="tab" aria-controls="agreements"
                        aria-selected="false">{{ __('UPLOADS') . ' (' . $agreementCount . ')' }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase semibold text-uppercase" id="notes-tab"
                        data-bs-toggle="tab" data-bs-target="#notes" data-tab="notes" type="button" role="tab"
                        aria-controls="notes"
                        aria-selected="false">{{ __('Notes' . ' (' . $notesCount . ')') }}</button>
                </li>
                <li class="nav-item f-12" role="presentation">
                    <button class="nav-link ls-0.6px px-2 px-sm-3 semibold text-uppercase" id="profile-tab"
                        data-bs-toggle="tab" data-bs-target="#profile" data-tab="profile" type="button" role="tab"
                        aria-controls="profile" aria-selected="false">{{ __('profile') }}</button>
                </li>
            @endif



        </ul>
    </div>

    <div class="tab-content" id="myTabContent1">
        <div class="tab-pane fade show active" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab"
            tabindex="0">

        </div>

        <div class="tab-pane fade pt-05" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="1">
            @if ($customer->type === 'b2c')
                <div class="main-subtitle no-border-btm">
                    <h5>{{ __('basic info') }}</h5>
                </div>
                <div>
                    <div class="profile-info-div">
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Customer Name:</div>
                            <div class="">
                                {{ $customer->name }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Custer Type:</div>
                            {{-- <div class="">
                        {{$customer->owner->first_name}} {{$customer->owner->last_name}}
                    </div> --}}
                            <span class="ms-3 flex align-center" style="display: flex; align-items: center;">
                                @include('partials.b2c-badge', [
                                    'status' => 'active',
                                    'label' => 'B2C',
                                ])
                            </span>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Phone #:</div>
                            <div class="">
                                {{ $customer->owner->phone }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Email (default):</div>
                            <div class="">
                                {{ $customer->owner->email }}
                            </div>
                        </div>
                        @if ($customer->owner->email2 != '')
                            <div class="instructor-basic-info">
                                <div class="text-secondary">Email:</div>
                                <div class="">
                                    {{ $customer->owner->email2 }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="main-subtitle no-border-btm">
                    <h5>ADDRESS INFO</h5>
                </div>
                <div>
                    <div class="profile-info-div">
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Address:</div>
                            <div class="">
                                {{ $customer?->address }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">City:</div>
                            <div class="">
                                {{ $customer?->city }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">ZIP Code:</div>
                            <div class="">
                                {{ $customer?->zip }}
                            </div>
                        </div>
                        @if ($customer->location === \App\Helpers\Constants::LOCATION_TYPE[0])
                            <div class="instructor-basic-info">
                                <div class="text-secondary">State:</div>
                                <div class="">
                                    {{ $customer?->state?->name }}
                                </div>
                            </div>
                        @else
                            <div class="instructor-basic-info">
                                <div class="text-secondary">Country:</div>
                                <div class="">
                                    {{ $customer?->country?->name }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="main-subtitle no-border-btm">
                    <h5>{{ __('basic info') }}</h5>
                </div>
                <div>
                    <div class="profile-info-div">
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Customer Name:</div>
                            <div class="">
                                {{ $customer->name }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Licensee name:</div>
                            <div class="">
                                {{ $customer->owner->first_name }} {{ $customer->owner->last_name }}@if($customer->licensee2_first_name != '' OR $customer->licensee2_last_name != ''), {{ $customer->licensee2_first_name }} {{ $customer->licensee2_last_name }}@endif
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Phone #:</div>
                            <div class="">
                                {{ $customer->owner->phone }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Email (default):</div>
                            <div class="">
                                {{ $customer->owner->email }}
                            </div>
                        </div>
                        @if ($customer->owner->email2 != '')
                            <div class="instructor-basic-info">
                                <div class="text-secondary">Email:</div>
                                <div class="">
                                    {{ $customer->owner->email2 }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="main-subtitle no-border-btm">
                    <h5>BILLING ADDRESS</h5>
                </div>
                <div>
                    <div class="profile-info-div">
                        <div class="instructor-basic-info">
                            <div class="text-secondary">Address:</div>
                            <div class="">
                                {{ $customer?->address }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">City:</div>
                            <div class="">
                                {{ $customer?->city }}
                            </div>
                        </div>
                        <div class="instructor-basic-info">
                            <div class="text-secondary">ZIP Code:</div>
                            <div class="">
                                {{ $customer?->zip }}
                            </div>
                        </div>
                        @if ($customer->location === \App\Helpers\Constants::LOCATION_TYPE[0])
                            <div class="instructor-basic-info">
                                <div class="text-secondary">State:</div>
                                <div class="">
                                    {{ $customer?->state?->name }}
                                </div>
                            </div>
                        @else
                            <div class="instructor-basic-info">
                                <div class="text-secondary">Country:</div>
                                <div class="">
                                    {{ $customer?->country?->name }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                @if ($customer?->use_billing_address == '0')
                    <div class="main-subtitle no-border-btm">
                        <h5>SHIPPING ADDRESS</h5>
                    </div>
                    <div>
                        <div class="profile-info-div">
                            <div class="instructor-basic-info">
                                <div class="text-secondary">Address:</div>
                                <div class="">
                                    {{ $customer?->shipping_address }}
                                </div>
                            </div>
                            <div class="instructor-basic-info">
                                <div class="text-secondary">City:</div>
                                <div class="">
                                    {{ $customer?->shipping_city }}
                                </div>
                            </div>
                            <div class="instructor-basic-info">
                                <div class="text-secondary">ZIP Code:</div>
                                <div class="">
                                    {{ $customer?->shipping_zip }}
                                </div>
                            </div>
                            @if ($customer->shipping_location === \App\Helpers\Constants::LOCATION_TYPE[0])
                                <div class="instructor-basic-info">
                                    <div class="text-secondary">State:</div>
                                    <div class="">
                                        {{ $customer?->shipping_state?->name }}
                                    </div>
                                </div>
                            @else
                                <div class="instructor-basic-info">
                                    <div class="text-secondary">Country:</div>
                                    <div class="">
                                        {{ $customer?->shipping_country?->name }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @else
                    <div class="main-subtitle no-border-btm">
                        <h5>SHIPPING ADDRESS SAME AS BILLING</h5>
                    </div>
                @endif

            @endif

            <div
                class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                <a type="button" href="{{ route('admin.customers.edit', ['customer' => $customer]) }}">
                    <button type="submit" class="btn btn-primary fw-normal">{{ __('Edit') }}</button>
                </a>
            </div>
        </div>

        <div class="tab-pane fade" id="licence" role="tabpanel" aria-labelledby="licence-tab" tabindex="2">

        </div>

        <div class="tab-pane fade" id="lease" role="tabpanel" aria-labelledby="lease-tab" tabindex="3">

        </div>

        <div class="tab-pane fade" id="purchases" role="tabpanel" aria-labelledby="purchases-tab" tabindex="4">

        </div>

        <div class="tab-pane fade" id="payment-history" role="tabpanel" aria-labelledby="payment-history-tab"
            tabindex="5">

        </div>

        <div class="tab-pane fade" id="invoices" role="tabpanel" aria-labelledby="invoices-tab" tabindex="6">

        </div>

        <div class="tab-pane fade" id="agreements" role="tabpanel" aria-labelledby="agreements-tab" tabindex="8">

        </div>

        <div class="tab-pane fade" id="notes" role="tabpanel" aria-labelledby="notes-tab" tabindex="7">

        </div>

        <script type="module">
            $(document).ready(function() {
                const currentPath = window.location.pathname;
                const currentUrlQuery = "{{ request()->query('tab') }}";

                console.log('currentUrlQuery', currentUrlQuery);
                
                // Clear localStorage key if the current path is not the dashboard
                if (!currentPath.startsWith('/admin/customers-dashboard')) {
                    localStorage.removeItem('dashboardActiveTab');
                }

                // // Handle tab click event
                $('button[data-bs-toggle="tab"]').on('click', function() {
                    var target = $(this).data('bs-target');
                    localStorage.setItem('dashboardActiveTab', target);
                    loadTabContent(this);
                });

                // // Retrieve the active tab from localStorage
                let dashboardActiveTab = localStorage.getItem('dashboardActiveTab');

                if (dashboardActiveTab) {
                    activateTab(dashboardActiveTab);
                } else {
                    if(currentUrlQuery) {
                        activateTab(`#${currentUrlQuery}`);
                    }else{
                        @if ($customer->type === 'b2c')
                            activateTab('#profile');
                        @else
                            activateTab('#dashboard');
                        @endif
                    }
                }

                // // Function to activate a tab and load its content
                function activateTab(target) {
                    // Remove active class from all tabs and content
                    $('.nav-link').removeClass('active');
                    $('.tab-pane').removeClass('show active');

                    // Add active class to the selected tab and content
                    $(`button[data-bs-target="${target}"]`).addClass('active');
                    $(target).addClass('show active');

                    // Load content for the selected tab
                    var tabElement = $(`button[data-bs-target="${target}"]`);
                    loadTabContent(tabElement);
                }

                // Function to load tab content via AJAX
                function loadTabContent(tabElement) {
                    var target = $(tabElement).data('bs-target');
                    var tab = $(tabElement).data('tab');
                    console.log('tab', tab);
                    
                    var url = '{{ route('admin.customers.load-tab-content', ['customer' => $customer->id]) }}' + '?tab=' + tab;
                    var targetContent = $(target);

                    console.log('url: ', url);

                    if (!$.trim(targetContent.html())) {
                        $.ajax({
                            url: url,
                            method: 'GET',
                            success: function(data) {
                                setTimeout(function() {
                                    targetContent.html(data);
                                }, 300);
                            },
                            error: function() {
                                flasherJS.error('', 'Error occurred while loading data.');
                            }
                        });
                    }
                }
            });
        </script>
        @include('partials.modals.upload-agreements', [
            'locations' => $locations,
            'types' => $types,
            'include_empty' => true,
            'customer' => $customer,
        ])
    @endsection
