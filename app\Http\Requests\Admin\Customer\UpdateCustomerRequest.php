<?php

namespace App\Http\Requests\Admin\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    // public function rules(): array
    // {
    //     $customer = $this->route('customer');
    //     return [
    //         'is_active'             => ['required', 'boolean'],
    //         'location'              => ['required', 'string', Rule::in(['USA', 'International'])],
    //         'address'               => ['nullable', 'string', 'max:255'],
    //         'city'                  => ['nullable', 'string', 'max:255'],
    //         'zip'                   => ['nullable', 'string', 'max:255'],
    //         'state_id'              => ['nullable', 'integer', 'exists:states,id'],
    //         'country_id'            => ['nullable', 'integer', 'exists:countries,id'],
    //         'use_billing_address'   => ['nullable', 'string', 'max:255'],
    //         'shipping_address'      => ['nullable', 'string', 'max:255'],
    //         'shipping_city'         => ['nullable', 'string', 'max:255'],
    //         'shipping_zip'          => ['nullable', 'string', 'max:255'],
    //         'shipping_state_id'     => ['nullable', 'integer', 'exists:states,id'],
    //         'shipping_country_id'   => ['nullable', 'integer', 'exists:countries,id'],
    //         'shipping_location'     => ['nullable', 'string', 'max:255'],
    //         'customer_name'         => ['required', 'string', 'max:255'],
    //         'owner_first_name'      => ['required', 'string', 'max:255'],
    //         'owner_last_name'       => ['required', 'string', 'max:255'],
    //         'phone'                 => ['nullable', 'string', 'max:255'],
    //         'email'                 => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $customer->owner->id],
    //         'email2'                => ['nullable', 'string', 'email', 'max:255'],
    //         'password'              => ['nullable', Password::min(8)->letters()->mixedCase()->numbers(), 'confirmed'],
    //     ];
    // }
    public function rules(): array
    {
        $customer = $this->route('customer');
        $rules = [
            'is_active'             => ['required', 'boolean'],
            'location'              => ['required', 'string', Rule::in(['USA', 'International'])],
            'address'               => ['nullable', 'string', 'max:255'],
            'address2'              => ['nullable', 'string', 'max:255'],
            'city'                  => ['nullable', 'string', 'max:255'],
            'zip'                   => ['nullable', 'string', 'max:255'],
            'type'                  => ['required', 'string', 'max:255'],
            'state_id'              => ['nullable', 'integer', 'exists:states,id'],
            'country_id'            => ['nullable', 'integer', 'exists:countries,id'],
            'use_billing_address'   => ['nullable', 'string', 'max:255'],
            'shipping_address'      => ['nullable', 'string', 'max:255'],
            'shipping_city'         => ['nullable', 'string', 'max:255'],
            'shipping_zip'          => ['nullable', 'string', 'max:255'],
            'shipping_state_id'     => ['nullable', 'integer', 'exists:states,id'],
            'shipping_country_id'   => ['nullable', 'integer', 'exists:countries,id'],
            'shipping_location'     => ['nullable', 'string', 'max:255'],
            'licensee2_first_name'  => ['nullable', 'string', 'max:255'],
            'licensee2_last_name'   => ['nullable', 'string', 'max:255'],
            'customer_name'         => ['required', 'string', 'max:255'],
            'owner_first_name'      => ['required', 'string', 'max:255'],
            'owner_last_name'       => ['required', 'string', 'max:255'],
            'phone'                 => ['nullable', 'string', 'max:255'],
            'email'                 => [
                'required',
                'string',
                'email',
                'max:255',
                // Rule::unique('users', 'email')->ignore($customer->owner->id)
            ],
            'email2'                => ['nullable', 'string', 'email', 'max:255'],
            'password'              => ['nullable', Password::min(8)->letters()->mixedCase()->numbers(), 'confirmed'],
        ];

        if ($customer && $customer->type === 'b2c') {
            unset(
                $rules['shipping_address'],
                $rules['shipping_city'],
                $rules['shipping_zip'],
                $rules['shipping_state_id'],
                $rules['shipping_country_id'],
                $rules['customer_name'],
                $rules['use_billing_address'],
                $rules['shipping_location']
            );
        }

        return $rules;
    }

    // protected function failedValidation(Validator $validator)
    // {
    //     // dd($validator->errors()); // This will dump validation errors
    //     throw new HttpResponseException(response()->json($validator->errors(), 422));
    // }
}
