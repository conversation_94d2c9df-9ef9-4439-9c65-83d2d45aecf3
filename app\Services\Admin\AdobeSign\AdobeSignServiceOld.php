<?php

namespace App\Services\Admin\AdobeSign;

use App\Helpers\NumberToWordsConverterHelper;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Lease;
use App\Services\Admin\Agreement\AgreementService;
use Automattic\WooCommerce\HttpClient\Request;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\AdminNotification as AdminNotificationModel;
use App\Notifications\AdminNotification;
use Pino\Numera;

class AdobeSignServiceOld
{
    public static function getBaseUri(): string
    {
        $url = config('adobe-sign.base_url');
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);

        // dd($response);

        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getBody(), true);
            return $responseData['apiAccessPoint'];

        } else {
            throw new \Exception($response->getBody());
        }
    }

    public static function getTemplate(array $templateNames, string $baseUri)
    {
        $url = $baseUri . config('adobe-sign.templates');
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);

        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getBody(), true);
            $templates = collect($responseData['libraryDocumentList']);
            return self::searchTemplates($templates, $templateNames);

        } else {
            Log::info('GET templates error: ' . $response->getBody());
            throw new \Exception($response->getBody());
        }

    }

    public static function getTemplatesList(string $baseUri)
    {
        $url = $baseUri . config('adobe-sign.templates');
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);

        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getBody(), true);
            $templates = collect($responseData['libraryDocumentList']);

            dd($templates);
            // return self::searchTemplates($templates, $templateNames);

        } else {
            Log::info('GET templates error: ' . $response->getBody());
            throw new \Exception($response->getBody());
        }

    }

    public static function searchTemplates(Collection $templates, array $searchTerms): array
    {
        $filtered = $templates->filter(function ($template) use ($searchTerms) {
            foreach ($searchTerms as $term) {
                if (stripos($template['name'], $term) !== false) {
                    return true;
                }
            }
            return false;
        });
//        $filtered = $templates->filter(function ($template) use ($searchTerm) {
//            return stripos($template['name'], $searchTerm) !== false;
//        });

        return $filtered->sortByDesc('modifiedDate')->first();
    }

    public static function getPrefillData(array $templateFieldsNames, Model $model, string $templateType, Company $company, array $templateNames): array
    {
        if (!empty($templateFieldsNames)) {
            if ($templateType === Agreement::LEASE_TYPE) {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;

                $sumPrice = round($model->monthly_installment / $conversionRate / 100,2) * $model->machine_quantity * $model->duration;
                $monthlySum =  round($model->monthly_installment / $conversionRate / 100,2) * $model->machine_quantity;
                $monthlyPerMachine =  round($model->monthly_installment / $conversionRate / 100,2);
                $depositAmount = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $depositTotal = ($model->deposit_amount) ? round(($model->deposit_amount * $model->machine_quantity) / $conversionRate / 100, 2) : 0;
                $machinePrice = round($model->machine_price / $conversionRate / 100, 2);
                $lease_number_model = NumberToWordsConverterHelper::convert($model->machine_quantity) . ' (' . $model->machine_quantity . ') ' . $model->machine->name . ', Model ' . $model->machine->name;

                // 'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                // 'agreement_lagree_company' => $company->name,
                // 'machine_count' => ucfirst(NumberToWordsConverterHelper::convert($model->machine_quantity)) . ' (' . $model->machine_quantity . ')',
                // 'machine_name' => $model->machine->name,
                // 'monthly_price' => NumberToWordsConverterHelper::convert($machinePrice) . ' Dollars' . ' ($' . $machinePrice . ')',
                // 'months_count' => NumberToWordsConverterHelper::convert($model->duration) . '(' . $model->duration . ')',
                // 'monthly_instalment' => NumberToWordsConverterHelper::convert($monthlySum) . ' Dollars' . ' ($' . $monthlySum . ')',
                // 'sum_price' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                // 'deposit_price' => ($depositAmount) ? NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')' : 'Zero Dollars' . ' ($0)',
                // 'agreement_company' => $model->customer->name,

                return [
                    'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                    'agreement_lagree_company' => $company->name,
                    'agreement_company' => $model->customer->name,
                    'monthly_per_machine' => NumberToWordsConverterHelper::convert($monthlyPerMachine) . ' Dollars' . ' ($' . $monthlyPerMachine . ')',
                    'months_count' => NumberToWordsConverterHelper::convert($model->duration) . '(' . $model->duration . ')',
                    'installment_cost' => NumberToWordsConverterHelper::convert($monthlySum) . ' Dollars' . ' ($' . $monthlySum . ')',
                    // 'sum_price' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'deposit_total' => ($depositAmount) ? NumberToWordsConverterHelper::convert($depositTotal) . ' Dollars' . ' ($' . $depositTotal . ')' : 'Zero Dollars' . ' ($0)',
                    'deposit_price' => ($depositAmount) ? NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')' : 'Zero Dollars' . ' ($0)',
                    'lease_number_model' => $lease_number_model,
                    'buy_out' => NumberToWordsConverterHelper::convert($model->buy_out) . ' Dollars' . ' ($' . $model->buy_out . ')',
                ];
            }else if ($templateType === Agreement::PURCHASE_TYPE) {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
                $sumPrice = round($model->machine_price / $conversionRate / 100,2) * $model->machine_quantity;
                $monthlySum =  round($model->price / $conversionRate / 100,2) * $model->machine_quantity;
                $depositAmount = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $machinePrice = round($model->machine_price / $conversionRate / 100, 2);
                $left_to_pay = round($sumPrice - $depositAmount, 2);
                // Fourteen (14) Megaformer, Model MEGA PRO
                $purchase_number_model = NumberToWordsConverterHelper::convert($model->machine_quantity) . ' (' . $model->machine_quantity . ') ' . $model->machine->name . ', Model ' . $model->machine->name;
                // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
                $purchase_amount = NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')';
                // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
                $purchase_per_machine = $model->machine->name . ' is ' . NumberToWordsConverterHelper::convert($model->machine_price) . ' Dollars' . ' ($' . $machinePrice . ')';

                return [
                    'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                    'agreement_lagree_company' => $company->name,
                    'agreement_company' => $model->customer->name,
                    'agreement_zip' => $model->studio->zip,
                    'agreement_annual_fee' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'agreement_deposit' => NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')',
                    'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                    'agreement_annual' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    'purchase_number_model' => $purchase_number_model, // Fourteen (14) Megaformer, Model MEGA PRO
                    'purchase_amount' => $purchase_amount, // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
                    'purchase_per_machine' => $purchase_per_machine, // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
                ];
            } else {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;

                $licenseFee = round($model->price / $conversionRate / 100, 2);
                $deposit = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $left_to_pay = round(($model->price - $model->deposit_amount) / $conversionRate / 100, 2);

                if($templateNames[0] == 'IMS-MINI-FINAL') {
                    Log::info('IMS-MINI-FINAL');

                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-MICRO-FINAL') {
                    Log::info('IMS-MICRO-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-MEGA-FINAL') {
                    Log::info('IMS-MEGA-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MINI-FINAL') {
                    Log::info('IMS-INT-MINI-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MICRO-FINAL') {
                    Log::info('IMS-INT-MICRO-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MEGA-FINAL') {
                    Log::info('IMS-INT-MEGA-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,
                        'agreement_zip' => $model->studio->zip,
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else{
                    return [
                        'current_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'customer_name' => $model->customer->owner->first_name . ' ' . $model->customer->owner->last_name,
                        'customer_company_name' => $model->customer->name,
                        'zip_code' => $model->studio->zip,
                        'monthly_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'company_name' => $company->name,
                        'state' => $company->state->name,
                        'company_address' => $company->address,
                    ];
                }
            }
        }
        return $templateFieldsNames;
    }

    public static function mergeArrays(array $prefillData, array $templateFieldsNames)
    {
        $mergedArray = [];

        foreach ($templateFieldsNames as $key) {
            if (array_key_exists($key, $prefillData)) {
                $mergedArray[$key] = $prefillData[$key];
            }
        }

        return $mergedArray;
    }

    public static function reformatMergeFields(array $mergeFields): array
    {
        $newArray = [];

        foreach ($mergeFields as $key => $value) {
            $newArray[] = [
                'fieldName' => $key,
                'defaultValue' => $value,
            ];
        }

        return $newArray;
    }

    public static function createAgreement(array $template, Customer $customer, Model $model, string $templateType, Company $company, string $baseUri, array $templateNames)
    {
        $templateFieldsNames = self::getTemplateDetails($template, $baseUri);

        $prefillData = self::getPrefillData($templateFieldsNames, $model, $templateType, $company, $templateNames);

        $mergeFields = self::mergeArrays($prefillData, $templateFieldsNames);

        $reformatedMergeFields  = self::reformatMergeFields($mergeFields);

        $agreementsEndpoint = $baseUri . config('adobe-sign.agreements');

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];

        $agreementInfo = [
            'fileInfos' => [
                [
                    'libraryDocumentId' => $template['id'],
                ],
            ],
            'mergeFieldInfo' => $reformatedMergeFields,
            'name' => 'Agreement',
            'participantSetsInfo' => [
                [
                    'memberInfos' => [
                        [
                            'name' => $customer->name,
                            'email' => $customer->owner->email,
                            'securityOption' => [
                                'authenticationMethod' => 'NONE'
                            ],
                        ]
                    ],
                    'order' => 1,
                    'role' => 'SIGNER',
                ],
                [
                    'memberInfos' => [
                        [
                            'name' => env('ADOBE_SIGN_NAME'),
                            'email' => env('ADOBE_SIGN_EMAIL')
                        ]
                    ],
                    'order' => 2,
                    'role' => 'APPROVER',
                ]
            ],
            'signatureType' => 'ESIGN',
            'state' => 'IN_PROCESS',
        ];

        $response = Http::withHeaders($headers)->post($agreementsEndpoint, $agreementInfo);

        $responseData = $response->json();

        Log::info('Create agreement response: ' . json_encode($responseData));

        if ($responseData['id']) {            
            $agreementData = [
                'adobe_template_id' => $template['id'],
                'customer_id' => $customer->id,
                'adobe_agreement_id' => $responseData['id'],
                'send_date' => now(),
                'status' => 0,
                'license_id' => $templateType === 'license' ? $model->id : NULL,
                'lease_id' => $templateType === 'lease' ? $model->id : NULL,
                'purchase_id' => $templateType === 'purchase' ? $model->id : NULL,
                'location' => $model->studio_id,
                'type' => $templateType
            ];
            AgreementService::store($agreementData);
        } else {
            throw new \Exception($responseData);
        }
    }

    public static function getAgreementInfo(string $agreement_id)
    {
        $baseUri = self::getBaseUri();
        $url = $baseUri . config('adobe-sign.agreements') . '/' . $agreement_id . '/signingUrls';

        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];
        
        $response = Http::withHeaders($headers)->get($url);

        $responseData = $response->json();

        return $responseData;
    }

    public static function getTemplateDetails(array $template, string $baseUri): array
    {
        $url = $baseUri . config('adobe-sign.templates') . '/' . $template['id'] . '/' . 'formFields';

        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);

        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getBody(), true);

            $fields = [];

            foreach ($responseData['fields'] as $field)
            {
                if($field['inputType'] === "TEXT_FIELD" && $field["contentType"] === "DATA")
                    $fields[] = $field['name'];
            }

            return $fields;
        } else {
            Log::info('GET template form fields error: ' . $response->getBody());
            throw new \Exception($response->getBody());
        }
    }

    public static function getDocumentAndSaveToStorage(Agreement $agreement, string $baseUri): void
    {
        $agreementEndpoint = $baseUri . config('adobe-sign.agreements') . '/' . $agreement->adobe_agreement_id . '/combinedDocument';

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];

        $response = Http::withHeaders($headers)
            ->get($agreementEndpoint);

        if ($response->ok())
        {
            $fileName = 'agreement_' . $agreement->adobe_agreement_id . '.pdf';
            $filePath = 'customer/' . $agreement->customer_id . '/agreements/' .  $fileName;
            Storage::disk('public')->put($filePath, $response->body());

            $agreement->update([
                'filepath' => $filePath,
                'completed_date' => now(),
                'status' => Agreement::STATUSES['Awaiting Delivery'],
            ]);

        } else {
            throw new \Exception($response->body());
        }
    }
}
