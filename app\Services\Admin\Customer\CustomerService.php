<?php

namespace App\Services\Admin\Customer;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Models\ConversionRate;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\AdminSettings;
use App\Models\Payment;
use App\Models\PaymentStatus;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;

class CustomerService implements ICustomerService
{
    public function store(array $data)
    {
        $user = User::create([
            'first_name' => $data['owner_first_name'],
            'last_name'  => $data['owner_last_name'],
            'phone'      => $data['phone'],
            'email'      => $data['email'],
            'email2'     => $data['email2'],
            'password'   => Hash::make($data['password']),
            'is_active'  => $data['is_active'],
        ]);

        // dd($data['type']);
        $user->assignRole($data['type']);

        $user->customer()->create([
            'name'                  => $data['customer_name'],
            'licensee2_first_name'  => $data['licensee2_first_name'],
            'licensee2_last_name'   => $data['licensee2_last_name'],
            'location'              => $data['location'],
            'address'               => $data['address'],
            'address2'              => $data['address2'] ?? null,
            'city'                  => $data['city'],
            'zip'                   => $data['zip'],
            'state_id'              => $data['state_id'],
            'type'                  => $data['type'],
            'country_id'            => $data['country_id'],
            'shipping_location'     => $data['shipping_location'],
            'shipping_address'      => $data['shipping_address'],
            'shipping_city'         => $data['shipping_city'],
            'shipping_zip'          => $data['shipping_zip'],
            'shipping_state_id'     => $data['shipping_state_id'],
            'shipping_country_id'   => $data['shipping_country_id'],
            'use_billing_address'   => $data['use_billing_address'],
        ]);
    }

    public function update(array $data, Customer $customer)
    {
        $customer->owner()->update([
            'first_name' => $data['owner_first_name'],
            'last_name'  => $data['owner_last_name'],
            'phone'      => $data['phone'],
            'email'      => $data['email'],
            'email2'     => $data['email2'],
            'is_active'  => $data['is_active'],
        ]);

        // dd($data);

        if (isset($data['password'])) {
            $customer->owner()->update([
                'password'   => Hash::make($data['password']),
            ]);
        }

        $customer->update([
            'name'                  => $data['customer_name'],
            'location'              => $data['location'],
            'licensee2_first_name'  => $data['licensee2_first_name'],
            'licensee2_last_name'   => $data['licensee2_last_name'],
            'address'               => $data['address'],
            'address2'              => $data['address2'] ?? null,
            'city'                  => $data['city'],
            'zip'                   => $data['zip'],
            'state_id'              => $data['state_id'],
            'type'                  => $data['type'],
            'country_id'            => $data['country_id'],
            'shipping_location'     => $data['shipping_location'],
            'shipping_address'      => $data['shipping_address'],
            'shipping_city'         => $data['shipping_city'],
            'shipping_zip'          => $data['shipping_zip'],
            'shipping_state_id'     => $data['shipping_state_id'],
            'shipping_country_id'   => $data['shipping_country_id'],
            'use_billing_address'   => $data['use_billing_address'],
        ]);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $type): LengthAwarePaginator
    {
        // For 'remaining' field sorting, we need to get all records first, then sort and paginate
        if ($orderParam === 'remaining') {
            return $this->searchWithRemainingSort($searchData, $orderType, $perPage, $status, $type);
        }

        $query = Customer::with(['owner', 'licenses.payments', 'leases.payments', 'licenses.studio', 'leases.studio', 'leases.machine']);
        $query->where('type', $type);
        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->whereHas('owner', function ($query) use ($status) {
                $query->where('is_active', '=', $status);
            });
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                ->orWhere('licensee2_first_name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                ->orWhere('licensee2_last_name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                ->orWhereHas('owner', function ($q) use ($searchData) {
                    $q->where('first_name', 'LIKE', '%' . $searchData . '%')
                        ->orWhere('last_name', 'LIKE', '%' . $searchData . '%')
                        ->orWhere('email', 'LIKE', '%' . $searchData . '%');
                });
            });
        });

        // Handle special sorting cases
        if ($orderParam === 'owner') {
            $query->with('owner')->orderBy(User::select('first_name')->whereColumn('users.id', 'customers.owner_id'), $orderType);
        } else {
            $query->orderBy($orderParam, $orderType);
        }

        return $query->paginate($perPage);
    }

    /**
     * Search customers with sorting by remaining amount
     * This method gets all records, calculates remaining amounts, sorts, then paginates
     */
    private function searchWithRemainingSort(string $searchData, string $orderType, int $perPage, string $status, string $type): LengthAwarePaginator
    {
        $query = Customer::with(['owner', 'licenses.payments', 'leases.payments', 'licenses.studio', 'leases.studio', 'leases.machine']);
        $query->where('type', $type);

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->whereHas('owner', function ($query) use ($status) {
                $query->where('is_active', '=', $status);
            });
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhereHas('owner', function ($q) use ($searchData) {
                        $q->where('first_name', 'LIKE', '%' . $searchData . '%')
                            ->orWhere('last_name', 'LIKE', '%' . $searchData . '%');
                    });
            });
        });

        // Get all records (without pagination first)
        $allCustomers = $query->get();

        // Calculate remaining amounts for each customer
        $currentCurrency = AdminSettings::first()->currency;

        foreach ($allCustomers as $customer) {
            $licenseType = $customer->licenses->where('type', Constants::LICENSE_TYPES[0]);
            $allLicenseTypePayments = $licenseType->flatMap(function ($license) {
                return $license->payments;
            });
            $licensesRemaining = $this->licensesRemainingSum($allLicenseTypePayments, $currentCurrency);

            $exclusivityType = $customer->licenses->where('type', Constants::LICENSE_TYPES[1]);
            $allExclusivityTypePayments = $exclusivityType->flatMap(function ($license) {
                return $license->payments;
            });
            $exclusivityRemaining = $this->licensesRemainingSum($allExclusivityTypePayments, $currentCurrency);

            $allLeasePayments = $customer->leases->flatMap(function ($lease) {
                return $lease->payments;
            });
            $leasesById = $customer->leases->keyBy('id');
            $leaseRemaining = $this->leasesRemainingSum($allLeasePayments, $currentCurrency, $leasesById);

            // Set the calculated remaining amount
            $customer->calculated_remaining = $licensesRemaining + $exclusivityRemaining + $leaseRemaining;
        }

        // Sort by remaining amount
        if ($orderType === 'desc') {
            $sortedCustomers = $allCustomers->sortByDesc('calculated_remaining');
        } else {
            $sortedCustomers = $allCustomers->sortBy('calculated_remaining');
        }

        // Manual pagination
        $currentPage = \Illuminate\Pagination\Paginator::resolveCurrentPage();
        $perPage = ($perPage != 0) ? $perPage : $sortedCustomers->count();
        $currentItems = $sortedCustomers->slice(($currentPage - 1) * $perPage, $perPage)->values();

        $paginatedItems = new LengthAwarePaginator(
            $currentItems,
            $sortedCustomers->count(),
            $perPage,
            $currentPage,
            [
                'path' => \Illuminate\Pagination\Paginator::resolveCurrentPath(),
                'pageName' => 'page',
            ]
        );

        return $paginatedItems;
    }

    public function delete(Customer $customer)
    {
        $licenses = $customer->licenses;
        foreach ($licenses as $license) {
            $payments = $license->payments;
            foreach ($payments as $payment) {
                $payment->invoice()->delete();
                $payment->delete();
            }
        }
        $leases = $customer->leases;
        foreach ($leases as $lease) {
            $payments = $lease->payments;
            foreach ($payments as $payment) {
                $payment->invoice()->delete();
                $payment->delete();
            }
        }
        $customer->licenses()->delete();
        $customer->leases()->delete();
        $invoiceProducts = $customer->invoiceProducts();
        foreach ($invoiceProducts->get() as $invoiceProduct) {
            $invoiceProduct->items()->delete();
        }
        $invoiceProducts->delete();
        $customer->delete();
    }

    public function licensesRemainingSum(Collection $collection, Currency $currency): float
    {
        $remainingPayments = $collection->where('status', Constants::PAYMENT_STATUS['not_paid']);
        $licenseIds = $remainingPayments->pluck('license_id')->unique();
        $conversionRates = ConversionRate::where('rateable_type', 'App\Models\License')
            ->where('currency_id', $currency->id)
            ->whereIn('rateable_id', $licenseIds)->get();
        $remainingLicensePaymentsSum = 0;
        foreach ($remainingPayments as $payment) {
            $licenseId = $payment->license_id;
            $conversionRate = $conversionRates->where('rateable_id', $licenseId)->first()->rate;
            $remainingLicensePaymentsSum += CurrencyConversionHelper::calculateLicensePaymentAmount($payment, $conversionRate);
        }
        return $remainingLicensePaymentsSum;
    }

    public function leasesRemainingSum(Collection $collection, Currency $currency, $leasesById): float
    {
        $remainingPayments = $collection->where('status', Constants::PAYMENT_STATUS['not_paid']);

        $leaseIds = $remainingPayments->pluck('lease_id')->unique();
        $conversionRates = ConversionRate::where('rateable_type', 'App\Models\Lease')
            ->where('currency_id', $currency->id)
            ->whereIn('rateable_id', $leaseIds)
            ->get();
        $remainingPaymentsSum = 0;
        foreach ($remainingPayments as $payment) {
            $leaseId = $payment->lease_id;
            $lease = $leasesById->get($leaseId);

            $conversionRate = $conversionRates->where('rateable_id', $leaseId)->first()->rate;
            $remainingPaymentsSum += CurrencyConversionHelper::calculateLeasePaymentAmount($payment, $lease, $conversionRate);
        }

        return $remainingPaymentsSum;
    }

    public function update_setting(array $data, Customer $customer)
    {
        $adminId = $customer->owner_id;
        $settings = \App\Models\AdminSettings::where('admin_id', $adminId)->first();

        if ($settings) {
            foreach ($data as $key => $value) {
                if (in_array($key, $settings->getFillable())) {
                    $settings->$key = $value;
                }
            }
            $settings->save();
        }
    }
}
