<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\NumberToWordsConverterHelper;
use App\Http\Controllers\Controller;
use App\Models\Agreement;
use App\Models\Customer;
use App\Models\Studio;
use App\Services\Admin\AdobeSign\AdobeSignService;
use App\Services\Admin\Agreement\IAgreementService;
use App\Services\Admin\Invoice\IInvoiceService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AgreementController extends Controller
{
    private  $agreementService;

    public function __construct(
        IAgreementService $agreementService,
    )
    {
        $this->agreementService = $agreementService;
    }

    public function index()
    {
        $agreements = Agreement::all();
        return view('admin.agreements.all', compact('agreements'));
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'number';
        $orderType  = $request->get('order_type') ?? 'desc';

        $agreements = $this->agreementService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($agreements as $key => $agreement) {
            $agreement['sequential_id'] = $sequential_id++;
            $agreement['orderParam'] = $orderParam;
            $agreement['orderType'] = $orderType;
            // if($agreement->type == 'license'){
            //     $agreements[$key]->license = License::find($agreement->license_id);
            // }else if($agreement->type == 'lease'){
            //     $agreements[$key]->lease = Lease::find($agreement->lease_id);
            // }
        }

        $viewContent = view('partials.forms.agreements.agreements-search', compact('agreements', 'customer'))->render();

        return response()->json($viewContent);
    }

    public function searchAll(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'id';
        $orderType  = $request->get('order_type') ?? 'desc';

        $agreements = $this->agreementService->searchAll(
            $searchData,
            $orderParam,
            $orderType,
            $perPage            
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($agreements as $key => $agreement) {
            $agreement['sequential_id'] = $sequential_id++;
            $agreement['orderParam'] = $orderParam;
            $agreement['orderType'] = $orderType;
            // Use the new model attributes for cleaner code
            $agreement->name = $agreement->agreement_name;
            $agreement->type = $agreement->agreement_type;
            $agreement->location = $agreement->location_name;
            if($agreement->status == Agreement::STATUSES['Signed 1/2']){
                $sign = AdobeSignService::getAgreementInfo($agreement->adobe_agreement_id);
                if(isset($sign['message'])){
                    $agreement['sign_url'] = 'javascript:;';
                }else{
                    $agreement['sign_url'] = $sign['signingUrlSetInfos'][0]['signingUrls'][0]['esignUrl'];
                }                
            }
        }

        $viewContent = view('partials.forms.agreements.agreements-search-all', compact('agreements'))->render();

        return response()->json($viewContent);
    }

    public function download(Agreement $agreement)
    {
        if ($agreement->completed_date) {
            if (Storage::disk('public')->exists($agreement->filepath)) {
                return response()->download(storage_path('app/public/' . $agreement->filepath));
            } else {
                toastr()->addError('File not found.');
                return redirect()->back();
            }
        } else {
            toastr()->addError('Agreement is not completed.');
            return redirect()->back();
        }
    }
    public function destroy(Agreement $agreement)
    {
        try {
            $this->agreementService->delete($agreement);
            toastr()->addSuccess('', 'Agreement deleted successfully.');
            return redirect(route('admin.agreements.index'));
        } catch (\Exception $e) {
            toastr()->addError('Agreement delete failed');
            return redirect()->back();
        }
    }
    public function previewAgreement(Request $request)
    {
        $customer = Customer::where('id', $request->customer_id)->first();

        if($request->type == 'license'){
            $data = [
                'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                'agreement_lagree_company' => 'Lagree Fitness Inc.',
                'agreement_company' => $request->company_name,

                'agreement_address' => $request->address,
                'agreement_city' => $request->city,
                'agreement_zip' => $request->zip,
                'agreement_coutry_state' => $request->country ? $request->country : $request->state,

                'agreement_annual_fee' => NumberToWordsConverterHelper::convert($request->licenseFee) . ' Dollars' . ' ($' . $request->licenseFee . ')',
                'agreement_deposit' => NumberToWordsConverterHelper::convert($request->deposit) . ' Dollars' . ' ($' . $request->deposit . ')',
                'agreement_six_months' => NumberToWordsConverterHelper::convert($request->left_to_pay) . ' Dollars' . ' ($' . $request->left_to_pay . ')',
                'agreement_annual' => NumberToWordsConverterHelper::convert($request->licenseFee) . ' Dollars' . ' ($' . $request->licenseFee . ')',
                'agreement_company_rep' => $customer->owner->first_name . ' ' . $customer->owner->last_name,
            ];

            $html = view('adobe-files.' . $request->file, compact('data'))->render();
            return response()->json(['success' => true, 'html' => $html, 'data' => $data, 'request' => $request->all()]);
        }else if($request->type == 'lease'){
            $lease_number_model = NumberToWordsConverterHelper::convert($request->machine_quantity) . ' (' . $request->machine_quantity . ') ' . $request->machine_name . ', Model ' . $request->machine_name;

            if($request->studio_id){
                $studio = Studio::where('id', $request->studio_id)->first();
            }else{
                $studio = NULL;
            }

            $data = [
                'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                'agreement_lagree_company' => 'Lagree Fitness Inc.',
                'agreement_company' => $request->company_name,

                'agreement_address' => $customer->address ? $customer->address : ($studio ? $studio->address : ''),
                'agreement_city' => $customer->city ? $customer->city : ($studio ? $studio->city : ''),
                'agreement_zip' => $customer->zip ? $customer->zip : ($studio ? $studio->zip : ''),
                'agreement_coutry_state' => $customer->country ? $customer->country?->name : $studio->state?->name,

                'agreement_company_rep' => $customer->owner->first_name . ' ' . $customer->owner->last_name,
                'monthly_per_machine' => NumberToWordsConverterHelper::convert($request->monthly_per_machine) . ' Dollars' . ' ($' . $request->monthly_per_machine . ')',
                'months_count' => NumberToWordsConverterHelper::convert($request->months_count) . '(' . $request->months_count . ')',
                'installment_cost' => NumberToWordsConverterHelper::convert($request->installment_cost) . ' Dollars' . ' ($' . $request->installment_cost . ')',
                // 'sum_price' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                'deposit_total' => ($request->deposit_total) ? NumberToWordsConverterHelper::convert($request->deposit_total) . ' Dollars' . ' ($' . $request->deposit_total . ')' : '/',
                'deposit_price' => ($request->deposit_price) ? NumberToWordsConverterHelper::convert($request->deposit_price) . ' Dollars' . ' ($' . $request->deposit_price . ')' : '/',
                'lease_number_model' => $lease_number_model,
                'buy_out' => NumberToWordsConverterHelper::convert($request->buy_out) . ' Dollars' . ' ($' . $request->buy_out . ')',
            ];

            $html = view('adobe-files.' . $request->file, compact('data'))->render();
            return response()->json(['success' => true, 'html' => $html, 'data' => $data, 'request' => $request->all()]);
        }else if($request->type == 'purchase'){
            if($request->studio_id){
                $studio = Studio::where('id', $request->studio_id)->first();
            }else{
                $studio = NULL;
            }

            $data = [
                'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                'agreement_lagree_company' => 'Lagree Fitness Inc.',
                'agreement_company' => $request->company_name,

                'agreement_address' => $customer->address ? $customer->address : ($studio ? $studio->address : ''),
                'agreement_city' => $customer->city ? $customer->city : ($studio ? $studio->city : ''),
                'agreement_zip' => $customer->zip ? $customer->zip : ($studio ? $studio->zip : ''),
                'agreement_coutry_state' => $customer->country ? $customer->country?->name : $studio->state?->name,

                'agreement_annual_fee' => NumberToWordsConverterHelper::convert($request->licenseFee) . ' Dollars' . ' ($' . $request->licenseFee . ')',
                'agreement_deposit' => NumberToWordsConverterHelper::convert($request->deposit) . ' Dollars' . ' ($' . $request->deposit . ')',
                'agreement_six_months' => NumberToWordsConverterHelper::convert($request->left_to_pay) . ' Dollars' . ' ($' . $request->left_to_pay . ')',
                'agreement_annual' => NumberToWordsConverterHelper::convert($request->licenseFee) . ' Dollars' . ' ($' . $request->licenseFee . ')',
                'agreement_company_rep' => $customer->owner->first_name . ' ' . $customer->owner->last_name,
            ];

            $html = view('adobe-files.' . $request->file, compact('data'))->render();
            return response()->json(['success' => true, 'html' => $html, 'data' => $data, 'request' => $request->all()]);
        }

    }

}
