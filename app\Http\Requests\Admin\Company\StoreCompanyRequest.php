<?php

namespace App\Http\Requests\Admin\Company;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class StoreCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'status'           => 'required|boolean',
            'logo'             => 'nullable|mimes:jpeg,png,jpg,svg',
            'name'             => 'required|string|max:255',
            'owner_first_name' => 'nullable|string|max:255',
            'owner_last_name'  => 'nullable|string|max:255',
            'email'            => 'nullable|string|email|max:255|unique:companies,email',
            'phone'            => 'nullable|string|max:255',
            'address'          => 'nullable|string|max:255',
            'address2'         => 'nullable|string|max:255',
            'city'             => 'nullable|string|max:255',
            'state_id'         => 'nullable|integer|exists:states,id',
            'zip'              => 'nullable|string|max:255',
        ];
    }
}
