<div class="dashboard-side collapse collapse-horizontal flex-shrink-0 overflow-auto show" id="dashboard-side">
    <div class="dashboard-wrap px-5 py-6">
        <div class="logo text-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="134.799" height="15.001" viewBox="0 0 134.799 15.001">
                <path id="Path_7119" data-name="Path 7119"
                    d="M-143.154-1.223a.4.4,0,0,0,.4.4h7.625a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4h-5.313V-15.016a.41.41,0,0,0-.4-.4h-1.917a.4.4,0,0,0-.4.4Zm11.73.4h1.813a.628.628,0,0,0,.6-.417l1.021-2.25h6.23l1.021,2.25a.6.6,0,0,0,.6.417h1.813a.368.368,0,0,0,.354-.542l-6.417-14.022a.354.354,0,0,0-.354-.229h-.208a.374.374,0,0,0-.354.229l-6.48,14.022A.368.368,0,0,0-131.424-.827Zm4.459-4.979,2.042-4.584h.063l2.083,4.584ZM-115.132-8.1a7.429,7.429,0,0,0,7.48,7.459,10.527,10.527,0,0,0,5.167-1.354.4.4,0,0,0,.167-.333V-7.661a.392.392,0,0,0-.375-.4h-4a.383.383,0,0,0-.4.4v1.646a.379.379,0,0,0,.4.375h1.667v1.771a6.518,6.518,0,0,1-2.479.521,4.74,4.74,0,0,1-4.688-4.771,4.777,4.777,0,0,1,4.667-4.834,4.723,4.723,0,0,1,3.146,1.208.356.356,0,0,0,.542,0l1.292-1.354a.4.4,0,0,0-.021-.583,7.876,7.876,0,0,0-5.084-1.938A7.483,7.483,0,0,0-115.132-8.1Zm17.959,6.875a.4.4,0,0,0,.4.4h1.917a.41.41,0,0,0,.4-.4V-6.536h2.313l2.771,5.521a.357.357,0,0,0,.333.188h2.188a.394.394,0,0,0,.354-.6l-2.854-5.292a4.6,4.6,0,0,0,3.084-4.209,4.513,4.513,0,0,0-4.542-4.479h-5.959a.4.4,0,0,0-.4.4Zm2.729-7.542v-4.146h3.4a2.073,2.073,0,0,1,2.063,2.021,2.122,2.122,0,0,1-2.063,2.125Zm13.23,7.542a.4.4,0,0,0,.4.4h8.48a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-78.5V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-78.5v-3.417h6.167a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm14.334,0a.4.4,0,0,0,.4.4H-58a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-64.17V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-64.17v-3.417H-58a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm21.022,0a.41.41,0,0,0,.4.4h1.938a.41.41,0,0,0,.4-.4V-15.016a.41.41,0,0,0-.4-.4h-1.938a.41.41,0,0,0-.4.4Zm7.646-.083a.382.382,0,0,0,.4.479h1.875a.4.4,0,0,0,.375-.313l1.208-7.9h.063l3.688,8.188a.374.374,0,0,0,.354.229h.375a.354.354,0,0,0,.354-.229l3.646-8.188h.063l1.229,7.9a.452.452,0,0,0,.4.313h1.9a.362.362,0,0,0,.375-.479l-2.458-14a.351.351,0,0,0-.375-.313h-.333a.359.359,0,0,0-.354.208l-4.584,9.834h-.063l-4.584-9.834a.38.38,0,0,0-.354-.208h-.333a.351.351,0,0,0-.375.313Zm20.48-.938A6.7,6.7,0,0,0-13.147-.619c2.938,0,4.792-1.979,4.792-4.125,0-2.688-2.333-3.9-4.354-4.709-1.688-.688-2.458-1.354-2.458-2.333a1.659,1.659,0,0,1,1.833-1.458,6.854,6.854,0,0,1,2.9,1.146.548.548,0,0,0,.729-.25l.792-1.188a.516.516,0,0,0-.125-.688,7.335,7.335,0,0,0-4.167-1.4c-3.313,0-4.688,2.146-4.688,4,0,2.458,1.958,3.709,3.917,4.5,1.75.708,2.646,1.438,2.646,2.5a1.739,1.739,0,0,1-1.9,1.6,6.9,6.9,0,0,1-3.146-1.292.483.483,0,0,0-.708.146l-.75,1.292C-18.022-2.536-17.918-2.432-17.73-2.244Z"
                    transform="translate(143.154 15.62)" fill="#fff" />
            </svg>

            <?php
            $host = $_SERVER['HTTP_HOST'];

            if (strpos($host, 'staging-') !== false) {
                echo '<style>.dashboard-side .logo:after {content:"Staging"; background-color: #E8AF44 !important; font-size: 12px; font-weight:500; border-radius: 50px;padding: 5px 10px; color:#fff; position: absolute; margin-top: 82px; left: 50%; margin-left: -40px;}.dashboard-side .logo{padding-bottom: 90px !important;}</style>';
            }
            ?>
            
        </div>

        <button class="hamburger d-block d-md-none position-absolute" data-bs-toggle="collapse"
            data-bs-target="#dashboard-side" aria-controls="dashboard-side" aria-expanded="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14">
                <g id="Group_4666" data-name="Group 4666" transform="translate(-1711 -18)">
                    <rect id="Rectangle_1337" data-name="Rectangle 1337" width="17" height="2"
                        transform="translate(1711 18)" />
                    <rect id="Rectangle_1338" data-name="Rectangle 1338" width="20" height="2"
                        transform="translate(1711 24)" />
                    <rect id="Rectangle_1339" data-name="Rectangle 1339" width="17" height="2"
                        transform="translate(1711 30)" />
                </g>
            </svg>
        </button>

        <nav class="menu d-flex flex-column gap-5">
            <a class="menu-item {{ Route::is('admin.dashboard.*') ? 'active' : '' }}"
                href="{{ route('admin.dashboard.index') }}">
                {{ __('Dashboard') }}
            </a>
            <div class="menu-item menu-item-has-children d-flex align-items-center justify-content-between {{ Route::is('admin.customers.*') ? '' : 'collapsed' }}"
                data-bs-toggle="collapse" role="button"
                aria-expanded="{{ Route::is('admin.customers.*') ? 'true' : 'false' }}" href="#sidebar-customers"
                aria-controls="sidebar-customers">
                <span>{{ __('Customers') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="7.41" viewBox="0 0 12 7.41">
                    <path id="Path_5671" data-name="Path 5671"
                        d="M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z"
                        transform="translate(-435 -1414.295)" fill="#fff" />
                </svg>
            </div>
            <div id="sidebar-customers" class="collapse {{ Route::is('admin.customers.*') ? 'show' : '' }}">
                <div class="menu-items-wrap d-flex flex-column gap-25">
                    <a class="menu-item {{ Route::is('admin.customers.studio.index') ? 'active' : '' }}"
                        href="{{ route('admin.customers.studio.index') }}">
                        {{ __('Studios') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.customers.b2c.*') ? 'active' : '' }}"
                        href="{{ route('admin.customers.b2c.index') }}">
                        {{ __('B2C Customer') }}
                        <sup>V2</sup>
                    </a>
                    <a class="menu-item {{ Route::is('admin.customers.trainer.*') ? 'active' : '' }}"
                        href="{{ route('admin.customers.trainers') }}">
                        {{ __('Trainers') }}
                        <sup>V2</sup>
                    </a>
                    <a class="menu-item {{ Route::is('admin.customers.master-trainer*') ? 'active' : '' }}"
                        href="{{ route('admin.customers.master-trainers') }}">
                        {{ __('Master Trainers') }}
                        <sup>V2</sup>
                    </a>
                </div>
            </div>
            <!-- <a class="menu-item {{ Route::is('admin.invoice-products.*') ? 'active' : '' }}" href="{{ route('admin.invoice-products.index') }}">
                {{ __('Invoices') }}
            </a> -->
            <div class="menu-item menu-item-has-children d-flex align-items-center justify-content-between {{ Route::is('admin.invoice-products.*') || Route::is('admin.invoice-templates.*') ? '' : 'collapsed' }}"
                data-bs-toggle="collapse" role="button"
                aria-expanded="{{ Route::is('admin.invoice-products.*') || Route::is('admin.invoice-templates.*') ? 'true' : 'false' }}"
                href="#sidebar-invoices" aria-controls="sidebar-invoices">
                <span>{{ __('Invoices') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="7.41" viewBox="0 0 12 7.41">
                    <path id="Path_5671" data-name="Path 5671"
                        d="M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z"
                        transform="translate(-435 -1414.295)" fill="#fff" />
                </svg>
            </div>
            <div id="sidebar-invoices"
                class="collapse {{ Route::is('admin.invoice-products.*') || Route::is('admin.invoice-templates.*') ? 'show' : '' }}">
                <div class="menu-items-wrap d-flex flex-column gap-25">
                    <a class="menu-item {{ Route::is('admin.invoice-products.*') ? 'active' : '' }}"
                        href="{{ route('admin.invoice-products.index') }}">
                        {{ __('Invoices') }}
                        {{-- <sup>V2</sup> --}}
                    </a>
                    <a class="menu-item {{ Route::is('admin.invoice-templates.*') ? 'active' : '' }}"
                        href="{{ route('admin.invoice-templates.index') }}">
                        {{ __('Templates') }}
                    </a>
                </div>
            </div>
            <a class="menu-item {{ Route::is('admin.orders.*') ? 'active' : '' }}"
                href="{{ route('admin.orders.index') }}">
                {{ __('Orders (Po)') }}
            </a>
            <a class="menu-item {{ Route::is('admin.agreements.*') ? 'active' : '' }}"
                href="{{ route('admin.agreements.index') }}">
                {{ __('Agreements') }}
            </a>
            <div class="menu-item menu-item-has-children d-flex align-items-center justify-content-between {{ Route::is('admin.inventory.*') ? '' : 'collapsed' }}"
                data-bs-toggle="collapse" role="button"
                aria-expanded="{{ Route::is('admin.inventory.*') ? 'true' : 'false' }}" href="#sidebar-inventory"
                aria-controls="sidebar-inventory">
                <span>{{ __('Inventory') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="7.41" viewBox="0 0 12 7.41">
                    <path id="Path_5671" data-name="Path 5671"
                        d="M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z"
                        transform="translate(-435 -1414.295)" fill="#fff" />
                </svg>
            </div>
            <div id="sidebar-inventory"
                class="collapse {{ Route::is('admin.inventory.*') || Route::is('admin.products.*') || Route::is('admin.productfees.*') ? 'show' : '' }}">
                <div class="menu-items-wrap d-flex flex-column gap-25">
                    {{-- <a class="menu-item {{ (Route::is('admin.products.*')) ? 'active' : '' }}" href="{{ route('admin.products.index') }}">
                        {{ __('Products') }}
                        <sup>V2</sup>
                    </a> --}}
                    <a class="menu-item {{ Route::is('admin.productfees.*') ? 'active' : '' }}"
                        href="{{ route('admin.productfees.index') }}">
                        {{ __('Fees') }}
                        {{-- <sup>V2</sup> --}}
                    </a>
                    <a class="menu-item {{ Route::is('admin.bundles.*') ? 'active' : '' }}"
                        href="{{ route('admin.bundles.index') }}">
                        {{ __('Bundles') }}
                        {{-- <sup>V2</sup> --}}
                    </a>
                </div>
            </div>
            <a class="menu-item {{ Route::is('admin.reports.*') ? 'active' : '' }}"
                href="{{ route('admin.dashboard.index') }}">
                {{ __('Reports') }}
            </a>
            <div class="menu-item menu-item-has-children d-flex align-items-center justify-content-between {{ Route::is('admin.settings.*') || Route::is('admin.companies.*') || Route::is('admin.suppliers.*') || Route::is('admin.machines.*') || Route::is('admin.notifications.*') || Route::is('admin.email-templates.*') ? '' : 'collapsed' }}"
                data-bs-toggle="collapse" role="button"
                aria-expanded="{{ Route::is('admin.settings.*') || Route::is('admin.companies.*') || Route::is('admin.suppliers.*') || Route::is('admin.machines.*') || Route::is('admin.notifications.*') || Route::is('admin.email-templates.*') ? 'true' : 'false' }}"
                href="#sidebar-submenu" aria-controls="sidebar-submenu">
                <span>{{ __('Settings') }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="7.41" viewBox="0 0 12 7.41">
                    <path id="Path_5671" data-name="Path 5671"
                        d="M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z"
                        transform="translate(-435 -1414.295)" fill="#fff" />
                </svg>
            </div>
            <div id="sidebar-submenu"
                class="collapse {{ Route::is('admin.settings.*') || Route::is('admin.companies.*') || Route::is('admin.suppliers.*') || Route::is('admin.machines.*') || Route::is('admin.notifications.*') || Route::is('admin.email-templates.*') ? 'show' : '' }}">
                <div class="menu-items-wrap d-flex flex-column gap-25">
                    <a class="menu-item {{ Route::is('admin.settings.*') ? 'active' : '' }}"
                        href="{{ route('admin.settings.index') }}">
                        {{ __('System Settings') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.companies.*') ? 'active' : '' }}"
                        href="{{ route('admin.companies.index') }}">
                        {{ __('Companies') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.suppliers.*') ? 'active' : '' }}"
                        href="{{ route('admin.suppliers.index') }}">
                        {{ __('Suppliers') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.licenseSettings.*') ? 'active' : '' }}"
                        href="{{ route('admin.licenseSettings.index') }}">
                        {{ __('Licenses') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.machines.*') ? 'active' : '' }}"
                        href="{{ route('admin.machines.index') }}">
                        {{ __('Machines') }}
                    </a>
                    {{-- <a class="menu-item {{ (Route::is('admin.products.*')) ? 'active' : '' }}" href="{{ route('admin.products.index') }}">
                        {{ __('Products') }}
                    </a> --}}
                    <a class="menu-item {{ Route::is('admin.notifications.*') ? 'active' : '' }}"
                        href="{{ route('admin.notifications.index') }}">
                        {{ __('Notifications') }}
                    </a>
                    <a class="menu-item {{ Route::is('admin.email-templates.*') ? 'active' : '' }}"
                        href="{{ route('admin.email-templates.index') }}">
                        {{ __('Email Templates') }}
                    </a>
                </div>
            </div>
            <a class="menu-item text-secondary" href="{{ route('logout') }}"
                onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                {{ __('Logout') }}
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                @csrf
            </form>
        </nav>
    </div>
</div>

{{--  <div class="dashboard-side collapse collapse-horizontal" id="dashboard-icons"> 
   <div class="dashboard-wrap"> 
       <nav class="menu d-flex flex-column"> 
           <a class="menu-item" href="{{ route('admin.dashboard.index') }}"> 
           <a class="menu-item {{ (Route::is('dashboard')) ? 'active' : '' }}" href="{{ route('admin.dashboard.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="13.333" height="15" viewBox="0 0 13.333 15"> 
                   <path id="home_FILL1_wght400_GRAD0_opsz48" d="M8,21V11l6.667-5,6.667,5V21h-5V15.167H13V21Z" transform="translate(-8 -6)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item {{ (Route::is('document-categories.index')) ? 'active' : '' }}" href="{{ route('admin.customers.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.537" viewBox="0 0 15 10.537"> 
                   <path id="group_FILL1_wght400_GRAD0_opsz48" d="M1.9,19.487v-1.6a1.976,1.976,0,0,1,.305-1.077,1.859,1.859,0,0,1,.848-.721,13.453,13.453,0,0,1,2.231-.781,8.755,8.755,0,0,1,2.045-.238,8.648,8.648,0,0,1,2.036.238,13.568,13.568,0,0,1,2.223.781,1.91,1.91,0,0,1,.857.721,1.937,1.937,0,0,1,.314,1.077v1.6Zm11.878,0v-1.6a2.747,2.747,0,0,0-.543-1.756,3.974,3.974,0,0,0-1.425-1.111,16.828,16.828,0,0,1,2.206.4,7.775,7.775,0,0,1,1.68.6,2.626,2.626,0,0,1,.882.8,1.86,1.86,0,0,1,.322,1.069v1.6ZM7.33,14.04A2.441,2.441,0,0,1,4.785,11.5,2.441,2.441,0,0,1,7.33,8.95,2.441,2.441,0,0,1,9.875,11.5,2.441,2.441,0,0,1,7.33,14.04ZM13.438,11.5a2.441,2.441,0,0,1-2.545,2.545,3.794,3.794,0,0,1-.416-.025,1.8,1.8,0,0,1-.416-.093,2.785,2.785,0,0,0,.619-1.044,4.25,4.25,0,0,0,.212-1.383,3.95,3.95,0,0,0-.212-1.349,3.607,3.607,0,0,0-.619-1.077,3.581,3.581,0,0,1,.416-.085,2.86,2.86,0,0,1,.416-.034A2.441,2.441,0,0,1,13.438,11.5Z" transform="translate(-1.9 -8.95)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item {{ (Route::is('customers')) ? 'active' : '' }}" href="{{ route('admin.customers.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="11.906" height="14.883" viewBox="0 0 11.906 14.883"> 
                   <path id="description_FILL1_wght400_GRAD0_opsz48_1_" data-name="description_FILL1_wght400_GRAD0_opsz48(1)" d="M10.958,15.72h5.99V14.6h-5.99Zm0-3.163h5.99V11.441h-5.99ZM8,18.883V4h7.832l4.074,4.074V18.883ZM15.274,8.576H18.79l-3.516-3.46Z" transform="translate(-8 -4)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item {{ (Route::is('agreements')) ? 'active' : '' }}" href="{{ route('admin.customers.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13.5" viewBox="0 0 15 13.5"> 
                   <path id="leaderboard_FILL1_wght400_GRAD0_opsz48" d="M4,19.5v-9H7.75v9Zm5.625,0V6h3.75V19.5Zm5.625,0V12H19v7.5Z" transform="translate(-4 -6)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item {{ (Route::is('reports')) ? 'active' : '' }}" href="{{ route('admin.customers.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="13.395" height="15.627" viewBox="0 0 13.395 15.627"> 
                   <path id="local_mall_FILL1_wght400_GRAD0_opsz48" d="M6,17.627V5.535H9.163a3.491,3.491,0,0,1,.986-2.5A3.257,3.257,0,0,1,12.6,2a3.564,3.564,0,0,1,2.549,1.033,3.335,3.335,0,0,1,1.079,2.5h3.163V17.627ZM10.279,5.535h4.837A2.383,2.383,0,0,0,12.7,3.116a2.383,2.383,0,0,0-2.418,2.418ZM12.7,11.116a3.485,3.485,0,0,0,2.549-1.079,3.485,3.485,0,0,0,1.079-2.549H15.209A2.5,2.5,0,0,1,10.93,9.255a2.414,2.414,0,0,1-.744-1.767H9.07a3.485,3.485,0,0,0,1.079,2.549A3.485,3.485,0,0,0,12.7,11.116Z" transform="translate(-6 -2)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item {{ (Route::is('reports')) ? 'active' : '' }}" href="{{ route('admin.customers.index') }}"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15"> 
                   <path id="settings_FILL1_wght400_GRAD0_opsz48" d="M9.775,19,9.4,16.638a5.024,5.024,0,0,1-.75-.356,5.153,5.153,0,0,1-.694-.469L5.744,16.825,4,13.75l2.025-1.481a2.229,2.229,0,0,1-.047-.384q-.009-.216-.009-.384t.009-.384a2.229,2.229,0,0,1,.047-.384L4,9.25,5.744,6.175,7.956,7.188a5.153,5.153,0,0,1,.694-.469,3.88,3.88,0,0,1,.75-.337L9.775,4h3.45L13.6,6.363a6.079,6.079,0,0,1,.759.347,3.227,3.227,0,0,1,.684.478l2.213-1.012L19,9.25l-2.025,1.444a2.642,2.642,0,0,1,.047.4q.009.216.009.4t-.009.394a2.611,2.611,0,0,1-.047.394L19,13.75l-1.744,3.075-2.213-1.012a6.22,6.22,0,0,1-.684.478,3.267,3.267,0,0,1-.759.347L13.225,19ZM11.5,13.938A2.433,2.433,0,0,0,13.938,11.5,2.433,2.433,0,0,0,11.5,9.063,2.433,2.433,0,0,0,9.063,11.5,2.433,2.433,0,0,0,11.5,13.938Z" transform="translate(-4 -4)" fill="#fff"/> 
               </svg> 
           </a> 
           <a class="menu-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();"> 
               <svg xmlns="http://www.w3.org/2000/svg" width="13.395" height="13.395" viewBox="0 0 13.395 13.395"> 
                   <path id="logout_FILL1_wght400_GRAD0_opsz48" d="M16.158,15.953l-1.172-1.172L15.767,14H10.744V11.4H15.73l-.781-.781L16.12,9.442l3.274,3.274ZM6,19.395V6h7.274V8.295H8.295v8.8h4.979v2.295Z" transform="translate(-6 -6)" fill="#969696"/> 
               </svg> 
           </a> 
           <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none"> 
               @csrf 
           </form> 
       </nav> 
   </div> 
 </div>  --}}
