<style>
    #all-agreements-table .list-group-item,
    #all-agreements-table .list-header {
        grid-template-columns: 0px 0px 130px 130px 115px auto 135px 60px;
    }
</style>

@if (count($agreements) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $agreements[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="customer.name" class="sortable-list-header {{ $agreements[0]->orderParam == 'customer.name' ? 'active' : '' }}" data-sort="customer.name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="name" class="">{{ __('Agreement') }}</div>
        <div id="type" class="sortable-list-header {{ $agreements[0]->orderParam == 'type' ? 'active' : '' }}" data-sort="type">{{ __('Type') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="location" class="sortable-list-header {{ $agreements[0]->orderParam == 'location' ? 'active' : '' }}" data-sort="location">{{ __('Location') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="created_at" class="sortable-list-header {{ $agreements[0]->orderParam == 'created_at' ? 'active' : '' }}" data-sort="created_at">{{ __('Created') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header {{ $agreements[0]->orderParam == 'status' ? 'active' : '' }}" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>
    <div class="multi-select-actions">
        <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
            <div class="form-check my-0">
                <input class="form-check-input form-select-all" type="checkbox" value="" id="flexCheckDefault_all">
                <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
            </div>
            <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

            {{--Delete selected with comfirmation modal--}}
            <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                    href="#deleteSelectedModalToggle" role="button" onclick="document.querySelectorAll('.open-multi-actions:checked').length > 1 ? document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete these orders?') }}' : document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete this order?') }}'">{{ __('Delete') }}</span>
        </div>
    </div>

    {{--Selected items actions--}}
    <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content delete-selected-modal-content">
                <div class="modal-body p-7 text-center">
                    <h5 class="mb-1" id="multi-delete-title">{{ __('Are you sure you want to delete these orders?') }}</h5>
                    <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                    <div class="d-flex justify-content-center gap-3 mt-5">
                        <button type="submit"
                                formaction="{{ route('admin.orders.delete-multiple') }}"
                                class="btn btn-primary">{{ __('Delete') }}</button>
                        <div class="btn btn-outline-light text-info"
                                data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            @foreach ($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach ($agreements as $agreement)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <div class="form-check mb-0">
                    <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                        value="{{ $agreement->id }}" id="flexCheckDefault_{{ $agreement->id }}">
                    <label class="form-check-label d-none" for="flexCheckDefault_{{ $agreement->id }}"></label>
                </div>
                <p class="my-0 hide-transp">{{ $agreement->sequential_id }}</p>
                <p class="my-0">
                    <a href="{{ route('admin.customers.dashboard', $agreement->customer->id) }}" class="fw-medium" style="text-decoration: none;">
                        {{ $agreement->customer->name }}
                        @if ($agreement->customer->notes->count() > 0)
                        <span class="small-badge badge bg-warning rounded-5">{{ $agreement->customer->notes->count() }}</span>
                        @endif
                    </a>
                </p>
                <p class="my-0">
                    {{ $agreement->name }}
                    <span class="d-block">(Agreement)</span>
                </p>
                <p class="my-0 text-secondary">{{ ucfirst($agreement->type) }}</p>
                <p class="my-0">
                    {{-- @if ($agreement->purchase_id) --}}
                        {{ $agreement->location }}
                    {{-- @endif --}}
                    {{-- {{ $agreement->customer->studio->name }} --}}
                </p>
                <p class="my-0">{{ \Carbon\Carbon::parse($agreement->send_date)->format('m/d/Y') }}</p>
                <div class="status-div">
                @include('partials.agreement-status-badge', [
                    'status' => $agreement->status,
                    'type' => $agreement->type,
                ])
                </div>
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                <script>
                    function openIframePopup(url){
                        var iframe = document.createElement('iframe');
                        iframe.src = url;
                        var wrapper = document.createElement('div');
                        var close_iframe = document.createElement('span');
                        close_iframe.classList.add('close_iframe');
                        close_iframe.textContent = '×';
                        close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
                        wrapper.id = 'iframe-popup';
                        document.body.appendChild(wrapper);
                        wrapper.appendChild(iframe);
                        wrapper.append(close_iframe);
                        iframe.focus();
                        iframe.contentWindow.focus(); 
                    }
                </script>
                    <div class="dropdown-content" id="agreements-dropdown-content">
                        @if ($agreement->status == \App\Models\Agreement::STATUSES['Signed 1/2'])
                        <a href="javascript:;" onclick="openIframePopup('{{ $agreement->sign_url }}')">{{ __('SIGN NOW') }}</a>                            
                        @endif
                        {{-- @if ($agreement->status == \App\Models\Agreement::STATUSES['Signed 2/2 (wait4pay)'] AND $agreement->type == 'license')
                        <a href="{{ route('admin.licenses.payment-link', ['customer' => $agreement->customer_id, 'license' => $agreement->license_id]) }}">{{ __('Send Payment Link') }}</a>
                        @endif
                        @if ($agreement->status == \App\Models\Agreement::STATUSES['Signed 2/2 (wait4pay)'] AND $agreement->type == 'lease')
                        <a href="{{ route('admin.leases.payment-link', ['customer' => $agreement->customer_id, 'lease' => $agreement->lease_id]) }}">{{ __('Send Payment Link') }}</a>
                        @endif
                        @if ($agreement->status == \App\Models\Agreement::STATUSES['Signed 2/2 (wait4pay)'] AND $agreement->type == 'purchase')
                        <a href="{{ route('admin.purchases.payment-link', ['customer' => $agreement->customer_id, 'purchase' => $agreement->purchase_id]) }}">{{ __('Send Payment Link') }}</a>
                        @endif --}}

                        {{-- <a href="javascript:void(0)">{{ __('Mark as Active') }} </a> --}}
                        @if ($agreement->status == '2' AND ($agreement->type == 'lease' OR $agreement->type == 'purchase'))
                        <a href="javascript:void(0);" class="text-success toggle-status" data-id="{{ $agreement->id }}"
                            data-status="{{ $agreement->status }}">
                            Mark as Active
                        </a>                        
                        @endif
                        @if ($agreement->status != '0' AND $agreement->status != '6' AND $agreement->status != '5' AND $agreement->completed_date)
                        <a href="javascript:void(0);" class="download-agreement" data-id="{{ $agreement->id }}">
                            {{ __('Read Agreement') }}
                        </a>
                        <a href="javascript:void(0)">{{ __('Send to customer') }} </a>
                        @endif
                        {{-- <a href="javascript:void(0)">{{ __('Download') }} </a> --}}
                        {{-- @if ($agreement->completed_date)
                            <a href="{{ route('admin.agreements.download', $agreement) }}">{{ __('Download') }} </a>
                        @else
                            <a href="javascript:void(0)" class="download-link disabled text-secondary"
                                style="pointer-events: none">{{ __('Download') }}</a>
                        @endif --}}
                        <a href="javascript:void(0)" class="delete text-danger delete-agreement"
                            data-id="{{ $agreement->id }}">{{ __('Delete') }} </a>
                    </div>
                </div>
            </div>
            @include('partials.modals.delete', [
                'type' => 'Agreements',
                'id' => $agreement->id,
                'route' => route('admin.agreements.destroy', ['agreement' => $agreement]),
                'title' => 'Agreement #' . $agreement->id,
            ])
        @endforeach
        <div id="paginate paginate-agreements">
            @if ($agreements)
                <div class="">
                    {!! $agreements->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
    <p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const agreementsCount = @json($agreements).total;
    const descriptiveLabel = agreementsCount === 1 ? ' Agreement' : ' Agreements';

    $('#agreementsCount').text(agreementsCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        if(dropdownContent.hasClass('show')){
            dropdownContent.removeClass('show');
        }else{
            dropdownContent.addClass('show');
        }
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.download-link').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
    $('.toggle-status').click(function() {
        let button = $(this);
        let agreementId = button.data('id');
        let currentStatus = button.data('status');
        let newStatus = currentStatus == '3' ? 'Inactive' : 'Active';

        $.ajax({
            url: "{{ route('admin.customers.agreements.toggle') }}",
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                agreement_id: agreementId
            },
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                }
            },
        });
    });
    $('.download-agreement').click(function(e) {
        e.preventDefault();
        let agreementId = $(this).data('id');

        $.ajax({
            url: "{{ route('admin.customer.agreements.download') }}",
            type: "POST",
            data: {
                _token: "{{ csrf_token() }}",
                agreement_id: agreementId
            },
            success: function(response) {
                if (response.success) {
                    let link = document.createElement('a');
                    link.href = response.file_url;
                    link.download = response.file_name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr) {
                console.log(xhr.responseJSON);
                
                alert('Error downloading file.');
            }
        });
    });
    $('.delete-agreement').click(function(e) {
        e.preventDefault();
        let agreementId = $(this).data('id');
        $.ajax({
            url: "{{ route('admin.customer.agreements.delete') }}",
            type: "DELETE",
            data: {
                _token: "{{ csrf_token() }}",
                agreement_id: agreementId
            },
            success: function(response) {
                window.location.reload();
            }
        });
    });
</script>
