<?php

namespace App\Services\Admin\Purchase;

use App\Helpers\CurrencyHelper;
use App\Jobs\SendAgreement;
use App\Models\Customer;
use App\Models\Purchase;
use App\Models\Machine;
use App\Models\Payment;
use App\Models\Company;
use App\Models\Currency;
use App\Models\User;
use App\Models\AdminSettings;
use App\Models\Agreement;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class PurchaseService implements IPurchaseService
{
    public function store(array $data, Customer $customer)
    {
        DB::beginTransaction();

        try {
            $data['customer_id'] = $customer->id;

            $settings = AdminSettings::first();
            if ($settings->currency->code !== Currency::USD) {
                $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);

                $data['machine_price'] = round($currencyToDollarRate * $data['machine_price'] * 100);
                // $data['monthly_installment'] = round($currencyToDollarRate * $data['monthly_installment'] * 100);

                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                }
            } else {
                $data['machine_price'] = round($data['machine_price'] * 100);
                // $data['monthly_installment'] = round($data['monthly_installment'] * 100);
                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                }
            }

            $data['initial_currency_id'] = $settings->currency_id;
            $machine = Machine::find($data['machine_id']);
            $data['description'] = 'Purchase of ' . $machine->name;

            // echo '<pre>';
            // print_r($data);
            // die();
            
            $purchase = Purchase::create($data);

            // if (isset($data['description']) AND $data['description'] != '') {
            //     PurchaseNotes::create([
            //         'purchase_id' => $purchase->id,
            //         'body' => $data['description']
            //     ]);
            // }

            foreach (Currency::all() as $currency) {
                $purchase->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }

            SendAgreement::dispatch(Agreement::PURCHASE_TYPE, Agreement::PURCHASE_TEMPLATE, $customer, $purchase, Company::find($purchase->company_id));

            DB::commit();

            return $purchase;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

    }

    public function update(array $data, Purchase $purchase, Customer $customer) 
    {
        DB::beginTransaction();

        try {
            // $studio_id = $data['studio_id'] ?? null;
            // if (!$data['studio_exists']) {
            //     $studio = Studio::create($data['studio']);
            //     $studio_id = $studio->id;
            // }

            // $data['studio_id'] = $studio_id;
            $data['customer_id'] = $customer->id;


            $settings = AdminSettings::first();
            if ($settings->currency->code !== Currency::USD) {
                $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);

                $data['machine_price'] = round($currencyToDollarRate * $data['machine_price'] * 100);
                $data['monthly_installment'] = round($currencyToDollarRate * $data['monthly_installment'] * 100);

                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                }
            } else {
                $data['machine_price'] = round($data['machine_price'] * 100);
                $data['monthly_installment'] = round($data['monthly_installment'] * 100);
                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                }
            }

            $data['initial_currency_id'] = $settings->currency_id;

            $purchase->update($data);

            // if (isset($data['description']) and $data['description'] != '') {
            //     PurchaseNotes::firstOrCreate([
            //         'purchase_id' => $purchase->id,
            //         'body' => $data['description']
            //     ]);
            // }

            $purchase->conversionRate()->delete();
            foreach (Currency::all() as $currency) {
                $purchase->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }

            SendAgreement::dispatch(Agreement::PURCHASE_TYPE, Agreement::PURCHASE_TEMPLATE, $customer, $purchase, Company::find($purchase->company_id));

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

    }
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator
    {
        $query = Purchase::where('customer_id', $customer->id)->with('payments');
        // $query = Purchase::where('customer_id', $customer->id)->with('payments');

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            // $query->whereHas('purchase.machine', function ($query) use ($searchData) {
            //     $query->where('name', 'LIKE', '%' . $searchData . '%');
            // });
        });

       $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }
    public function delete(Purchase $purchase)
    {
        $payments = $purchase->payments()->get();
        foreach ($payments as $payment) {
            $payment->invoice()->delete();
            $payment->delete();
        }
        $purchase->delete();
    }
}
