@if(  $status == \App\Helpers\Constants::LICENSE_STATUSES['License Sent (0/2)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-warning text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['License Sent (0/2)'] }}">
        {{ __('License Sent (0/2)') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Signed 1/2'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Signed 1/2'] }}">
        {{ __('Signed 1/2') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Signed 2/2 (wait4pay)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Signed 2/2 (wait4pay)'] }}">
        {{ __('Signed 2/2 (wait4pay)') }}
    </div>
@elseif( $status == \App\Helpers\Constants::LICENSE_STATUSES['Active'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-success" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Active'] }}">
        {{ __('Active') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Inactive'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-light text-dark" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Inactive'] }}">
        {{ __('Inactive') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Deposit paid'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Deposit paid'] }}">
        {{ __('Deposit paid') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Canceled'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-danger text-danger" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Canceled'] }}">
        {{ __('Canceled') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::LICENSE_STATUSES['Payment failed'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-danger text-danger" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Payment failed'] }}">
        {{ __('Payment failed') }}
    </div>
@else
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-light text-dark" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::LICENSE_STATUSES['Other'] }}">
        {{ __('Other') }}
    </div>
@endif