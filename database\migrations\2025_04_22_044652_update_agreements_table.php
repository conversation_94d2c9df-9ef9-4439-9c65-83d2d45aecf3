<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            if (Schema::hasColumn('agreements', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('agreements', 'filepath')) {
                $table->dropColumn('filepath');
            }
        });

        Schema::table('agreements', function (Blueprint $table) {
            $table->string('custom')->default('auto');
            $table->string('file_path')->nullable();
            $table->string('status')->nullable();
            $table->string('type')->nullable();
            $table->string('file_name')->nullable();
            $table->string('original_name')->nullable();
            $table->string('location')->nullable();
            $table->string('adobe_template_id')->nullable()->change();
            $table->string('adobe_agreement_id')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->dropColumn([
                'file_path',
                'status',
                'type',
                'file_name',
                'original_name',
                'location',
            ]);

            $table->string('filepath')->nullable();
            $table->enum('status', ['Pending', 'Signed (payment)', 'Awaiting deliver', 'Active'])->default('Pending');
            $table->string('adobe_template_id')->change();
            $table->string('adobe_agreement_id')->change();
        });
    }
};
