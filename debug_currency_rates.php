<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Currency;
use App\Models\AdminSettings;
use App\Helpers\CurrencyHelper;

echo "=== Currency Rate Debug ===\n\n";

try {
    // Check currencies
    echo "1. Checking Currencies:\n";
    $currencies = Currency::all();
    
    if ($currencies->isEmpty()) {
        echo "❌ No currencies found in database!\n";
        exit(1);
    }
    
    foreach ($currencies as $currency) {
        echo "   Currency ID: {$currency->id}\n";
        echo "   Name: {$currency->name}\n";
        echo "   Code: {$currency->code}\n";
        echo "   Rate: {$currency->rate}\n";
        
        try {
            $calculatedRate = CurrencyHelper::calculateRate($currency);
            echo "   Calculated Rate: {$calculatedRate}\n";
        } catch (Exception $e) {
            echo "   ❌ Error calculating rate: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    // Check admin settings
    echo "2. Checking Admin Settings:\n";
    $settings = AdminSettings::first();
    
    if (!$settings) {
        echo "❌ No admin settings found!\n";
        exit(1);
    }
    
    echo "   Settings Currency ID: {$settings->currency_id}\n";
    
    if ($settings->currency) {
        echo "   Settings Currency: {$settings->currency->name} ({$settings->currency->code})\n";
        echo "   Settings Currency Rate: {$settings->currency->rate}\n";
    } else {
        echo "❌ Settings currency relationship not found!\n";
    }
    
    echo "\n";
    
    // Test conversion rate creation
    echo "3. Testing Conversion Rate Creation:\n";
    
    foreach ($currencies as $currency) {
        try {
            $rate = CurrencyHelper::calculateRate($currency);
            echo "   ✓ Currency {$currency->code}: Rate = {$rate}\n";
        } catch (Exception $e) {
            echo "   ❌ Currency {$currency->code}: Error = " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // Check for zero or null rates
    echo "4. Checking for Problem Currencies:\n";
    $problemCurrencies = Currency::where('rate', 0)->orWhereNull('rate')->get();
    
    if ($problemCurrencies->isEmpty()) {
        echo "   ✓ No currencies with zero or null rates\n";
    } else {
        echo "   ❌ Found currencies with zero or null rates:\n";
        foreach ($problemCurrencies as $currency) {
            echo "      - {$currency->code}: rate = " . ($currency->rate ?? 'NULL') . "\n";
        }
    }
    
    echo "\n";
    
    // Simulate license creation conversion rate process
    echo "5. Simulating License Creation Process:\n";
    
    if ($settings && $settings->currency_id) {
        echo "   Initial Currency ID would be: {$settings->currency_id}\n";
        
        echo "   Conversion rates that would be created:\n";
        foreach ($currencies as $currency) {
            try {
                $rate = CurrencyHelper::calculateRate($currency);
                echo "      - Currency {$currency->id} ({$currency->code}): {$rate}\n";
            } catch (Exception $e) {
                echo "      - Currency {$currency->id} ({$currency->code}): ERROR - " . $e->getMessage() . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
