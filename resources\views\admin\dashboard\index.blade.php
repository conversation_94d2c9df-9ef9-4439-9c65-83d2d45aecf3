@extends('layouts.app')

@section('content')
    <div class="page-title ttl-dropdown">
        <div class="title-left">
            <h3>{{ __('welcome admin!') }}</h3>
            <!--<h3 class="d-flex align-items-center mb-0 custom-fs-14px d-md-none custom-fw-500 text-uppercase">{{ __('welcome admin!') }}</h3>-->
        </div>

        <div id="add-license-button" class="ms-auto d-inline-block">
            <button type="button" class="drop-add-btn btn btn-primary">
                {{ __('ADD NEW') }} <span class="dropdown-arrow">&#9662;</span>
            </button>
            <div class="add-license-button-dropdown" id="add-license-button-dropdown">
                <a href="{{ route('admin.companies.create') }}">Company</a>
                <a href="{{ route('admin.customers.studio.create') }}">Customer</a>
                <a href="{{ route('admin.orders.create') }}">Order</a>
                <a href="{{ route('admin.suppliers.create') }}">Supplier</a>
            </div>
        </div>

        {{--        <div class="ms-auto d-flex d-sm-none">--}}
        {{--            <div class="custom-round-button-dropdown">--}}
        {{--                <button class="custom-dropbtn btn btn-primary float-right rounded-5">+</button>--}}
        {{--                <div class="custom-dropdown-content" id="customers-dropdown-content">--}}
        {{--                    <a href="{{route('admin.customers.create')}}">Customer</a>--}}
        {{--                    <a href="{{route('admin.orders.create')}}">Order</a>--}}
        {{--                    <a href="{{route('admin.suppliers.create')}}">Supplier</a>--}}
        {{--                    <a href="{{route('admin.companies.create')}}">Company</a>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}
    </div>

    <div class="border-bottom border-light mt-6 pb-4">
        <div class="row flex-nowrap overflow-x-1280 px-3 widgets dashboard-widgets">
        {{-- <div class="row flex-nowrap px-3 widgets"> --}}
            <div class="col-8 col-md-4 col-xl-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">+ {{number_format($paymentsSum['sum'], 0)}} </h3>
                        {{-- <h3 class="customer-dashboard-card-title">+ {{number_format($paymentsSum['sum'] / 1000, 2)}} </h3> --}}
                        <p class="customer-dashboard-card-text text-secondary" id="dashboard-sum-card">In the last 7
                            days</p>

                        <p class="stat-status mb-0 custom-fs-12px ls-0.5px {{ $paymentsSum['percentage'] < 0 ? 'text-danger':'text-success' }}">
                            <span class="{{ ($paymentsSum['percentage'] < 0) ? 'arrow-down':'arrow-up' }}"></span>
                            {{ abs($paymentsSum['percentage']) }}% since last period
                        </p>
                    </div>

                    <div class="round-button-dropdown position-absolute top-0 end-0 m-2">
                        <button class="d-md-none dropbtn-mobile" data-bs-toggle="offcanvas"
                                data-bs-target="#offcanvasFilterPayments" aria-controls="offcanvasFilterPayments"
                                id="mobile-filter-opener"
                        >
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        @include('partials.sidebar.filter-payments')
                        <button class="d-none d-md-block dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="dashboard-dropdown-content">
                            <a href="#" class="dropdown-option text-secondary" data-value="week" id="week">Last 7
                                days</a>
                            <a href="#" class="dropdown-option" data-value="month" id="month">Last 30 days</a>
                            <a href="#" class="dropdown-option" data-value="year" id="year">Year to date</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-8 col-md-4 col-xl-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">{{$customerCount}}</h3>
                        <p class="customer-dashboard-card-text text-muted">{{($customerCount == 1) ? 'Customer' : 'Customers'}}</p>
                    </div>
                </div>
            </div>

            <div class="col-8 col-md-4 col-xl-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">{{$activeLicensesCount['licenses_count'] + $activeLicensesCount['exclusivities_count']}}</h3>
                        <p class="customer-dashboard-card-text text-muted">Active licenses</p>
                        <p class="customer-dashboard-card-text text-warning colorYellow">
                            ({{$activeLicensesCount['exclusivities_count']}} exclusivity)</p>
                    </div>
                </div>
            </div>

            <div class="col-8 col-md-4 col-xl-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">{{$activeLeases}}</h3>
                        <p class="customer-dashboard-card-text text-muted">Active leases</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="main-subtitle no-border-btm">
        <h5 id="customersCount">last 10 payments</h5>

        <!--<div class="d-flex align-items-center justify-content-end">
            <a href="{{ route('admin.dashboard.payments.all') }}">
                <button type="button"
                        class="btn btn-ghost-light-bigger ms-auto d-inline-block no-uppercase-no-bold text-uppercase">{{ __('view all') }}</button>
            </a>
        </div>-->
    </div>

    <div id="dashboard-table" class="entity-table mb-10">
        <div class="list-group">
            @if(count($payments) !== 0)
                <div class="list-header d-grid border-bottom border-light text-info text-uppercase">
                    <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
                        <div class="sort-icon asc"></div>
                    </div>
                    <div id="customer.name" class="sortable-list-header" data-sort="customer.name">{{ __('Customer') }}
                        <div class="sort-icon asc"></div>
                    </div>
                    <div>{{ __('Type') }}</div>
                    <div>{{ __('Payment') }}</div>
                    <div id="payment_amount" class="sortable-list-header" data-sort="payment_amount">{{ __('Amount') }}
                        <div class="sort-icon asc"></div>
                    </div>
                    <div>{{ __('Invoice #') }}</div>
                    <div id="payment_date" class="sortable-list-header" data-sort="payment_date">{{ __('Date') }}
                        <div class="sort-icon asc"></div>
                    </div>
                </div>

                @foreach($payments as $payment)
                    <div class="list-group-item list-group-item-action d-grid align-items-center">
                        <p class="my-0 hide-transp" data-column="id">{{$payment->sequential_id}}</p>
                        <p class="my-0" data-column="customer.name"><a href="{{ route('admin.customers.dashboard', ['customer' => ($payment->customer ?? 0)]) }}" class="fw-medium" style="text-decoration: none;">{{$payment->customer->name ?? '-'}}</a></p>
                        @if($payment->type === \App\Helpers\Constants::LICENSE_TYPES[0])
                            <p class="my-0 text-secondary" data-column="payment_amount">License <span class="d-block">({{ \App\Helpers\Constants::LICENSE_PACKAGES_KEYS[$payment->license->package] }})</span></p>
                        @elseif($payment->type === \App\Helpers\Constants::LICENSE_TYPES[1])
                            <p class="my-0 text-secondary">Exclusivity <span class="d-block">({{ \App\Helpers\Constants::LICENSE_PACKAGES_KEYS[$payment->license->package] }})</span></p>
                        @elseif($payment->type === 'purchase')
                            <p class="my-0 text-secondary">Purchase <span class="d-block">({{$payment->name}})</span></p>
                        @else
                            <p class="my-0 text-secondary">Lease <span class="d-block">({{$payment->name}})</span></p>
                        @endif

                        @if($payment->type === 'license')
                            <p class="my-0" data-column="payment_amount">{{ __('Yearly') }}</p>
                        @elseif($payment->type === 'purchase')
                            <p class="my-0" data-column="payment_number">{{ __('One-time') }}</p>
                        @elseif($payment->type === 'lease')
                            <p class="my-0" data-column="payment_number">{{ $payment->payment_number }} of {{ $payment->lease->duration }}</p>
                        @endif
                        <p class="my-0 text-{{ $payment->status == \App\Helpers\Constants::PAYMENT_STATUS['paid'] ? 'success' : 'danger' }}" data-column="payment_amount">+ {{$admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                        <p class="my-0 text-secondary" data-column="payment_amount">
                            {{ $payment->invoice ? 'Invoice #' . $payment->invoice?->formatted_number : 'No invoice found' }}
                        </p>
                        <p class="my-0" data-column="payment_date">{{Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y')}}</p>
                        <div class="my-0" data-column="status">
                            @if($payment->status == \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                            <div class="d-inline-block rounded-pill bg-success text-success medium">
                                {{ __('Paid') }}
                            </div>
                            @endif
                        </div>
                        @if ($payment->invoice)
                        <div class="round-button-dropdown">
                            <button class="dropbtn">
                                <i class="fa fa-ellipsis-h"></i>
                            </button>
                            <div class="dropdown-content" id="payment-history-dropdown-content">
                                <a href="{{route('admin.download-invoice', ['customer' => $payment->customer, 'invoice' => $payment->invoice])}}">{{ __('Download Invoice') }}</a>
                                <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$payment->invoice->id}}">{{ __('Email Invoice') }}</a>
                            </div>
                        </div>
                        @include('partials.modals.email-invoice', [
                            'id' => $payment->invoice->id,
                            'route' => route('admin.email-invoice', ['customer' => $payment->customer, 'invoice' => $payment->invoice]),
                            'invoice' => $payment->invoice
                        ])                        
                        @endif
                    </div>
                @endforeach

            @else
                <p class="no-results-txt">There are no results.</p>                
            @endif
        </div>
    </div>
@endsection

<script type="module">
    document.addEventListener('DOMContentLoaded', function () {

        function sortTable(column, order) {
            let rows = $('.list-group .list-group-item').get();

            rows.sort(function (a, b) {
                let valA = $(a).find(`[data-column="${column}"]`).text().toLowerCase();
                let valB = $(b).find(`[data-column="${column}"]`).text().toLowerCase();

                if (column === 'payment_amount') {
                    valA = parseFloat(valA.replace(/[^0-9.-]+/g, ""));
                    valB = parseFloat(valB.replace(/[^0-9.-]+/g, ""));
                }

                if (order === 'asc') {
                    return valA > valB ? 1 : valA < valB ? -1 : 0;
                } else {
                    return valA < valB ? 1 : valA > valB ? -1 : 0;
                }
            });

            // Append the sorted rows back to the list
            $.each(rows, function (index, row) {
                $('.list-group').append(row);
            });
        }

        // Click event for sorting
        $('#dashboard-table .sortable-list-header').on('click', function () {
            const column = $(this).attr('data-sort');
            const currentOrder = $(this).find('.sort-icon').hasClass('asc') ? 'asc' : 'desc';
            const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';

            // Update sort icons
            $('.sortable-list-header .sort-icon').removeClass('asc desc');
            $(this).find('.sort-icon').addClass(newOrder);

            // Sort the table
            sortTable(column, newOrder);
        });


        // Define the mapping for period display text
        let periodText = {
            'week': 'In the last 7 days',
            'month': 'In the last 30 days',
            'year': 'In the current year'
        };


        $('.dropbtn').on('click', function (event) {
            event.preventDefault();
            event.stopPropagation();
            var dropdownContent = $(this).siblings('.dropdown-content');
            $('.dropdown-content').removeClass('show');
            dropdownContent.addClass('show');
        });

        $('.drop-add-btn').on('click', function (event) {
            event.preventDefault();
            event.stopPropagation();
            var dropdownContent = $(this).siblings('.add-license-button-dropdown');
            $('.add-license-button-dropdown').removeClass('show');
            dropdownContent.addClass('show');
        });

        $(document).on('click', function () {
            $('.dropdown-content').removeClass('show');
            $('.add-license-button-dropdown').removeClass('show');
        });

        $('.dropdown-option').click(function (e) {
            e.preventDefault();
            e.stopPropagation();

            // Get the selected value
            let period = $(this).data('value');

            // Update the text of the period on the card
            $('#dashboard-sum-card').text(periodText[period]);

            // Add 'text-secondary' class to the selected option and remove from others
            $('.dropdown-content .dropdown-option').removeClass('text-secondary');

            $(this).addClass('text-secondary');

            window.location.href = "{{ route('admin.dashboard.index', ['period' => '__period__']) }}".replace('__period__', period);
        });

        // On page load, set the correct text and class based on current period (from server-side)
        let currentPeriod = "{{ $paymentsSum['selected_period'] }}";  // You'll need to pass this from the controller
        $('#dashboard-sum-card').text(periodText[currentPeriod]);

        // Add 'text-secondary' class to the selected option on load
        $('.dropdown-content .dropdown-option').removeClass('text-secondary');
        $(`.dropdown-option[data-value="${currentPeriod}"]`).addClass('text-secondary');

        $('#mobile-filter-opener').on('click', function () {
            let currentPeriod = "{{ $paymentsSum['selected_period'] }}";  // You'll need to pass this from the controller
            console.log(currentPeriod);
            // Add 'text-secondary' class to the selected option on load
            $('.mobile-dropdown-content .mobile-dropdown-option').removeClass('text-secondary');
            $(`.mobile-dropdown-option[data-value="${currentPeriod}"]`).addClass('text-secondary');
        });
    })
</script>
