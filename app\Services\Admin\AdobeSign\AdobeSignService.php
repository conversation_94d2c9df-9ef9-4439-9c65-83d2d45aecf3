<?php

namespace App\Services\Admin\AdobeSign;

use App\Helpers\NumberToWordsConverterHelper;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Lease;
use App\Services\Admin\Agreement\AgreementService;
use Automattic\WooCommerce\HttpClient\Request;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\AdminNotification as AdminNotificationModel;
use App\Notifications\AdminNotification;
use Pino\Numera;

class AdobeSignService
{
    public static function getBaseUri(): string
    {
        $url = config('adobe-sign.base_url');
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);

        // dd($response);

        if ($response->getStatusCode() == 200) {
            $responseData = json_decode($response->getBody(), true);
            return $responseData['apiAccessPoint'];

        } else {
            throw new \Exception($response->getBody());
        }
    }

    public static function getPrefillData(Model $model, string $templateType, Company $company, array $templateNames): array
    {
        if (!empty($templateType)) {
            if ($templateType === Agreement::LEASE_TYPE) {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;

                $sumPrice = round($model->monthly_installment / $conversionRate / 100,2) * $model->machine_quantity * $model->duration;
                $monthlySum =  round($model->monthly_installment / $conversionRate / 100,2) * $model->machine_quantity;
                $monthlyPerMachine =  round($model->monthly_installment / $conversionRate / 100,2);
                $depositAmount = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $depositTotal = ($model->deposit_amount) ? round(($model->deposit_amount * $model->machine_quantity) / $conversionRate / 100, 2) : 0;
                $machinePrice = round($model->machine_price / $conversionRate / 100, 2);
                $lease_number_model = NumberToWordsConverterHelper::convert($model->machine_quantity) . ' (' . $model->machine_quantity . ') ' . $model->machine->name . ', Model ' . $model->machine->name;

                return [
                    'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                    'agreement_lagree_company' => $company->name,
                    'agreement_company' => $model->customer->name,

                    'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                    'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                    'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                    'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                    'monthly_per_machine' => NumberToWordsConverterHelper::convert($monthlyPerMachine) . ' Dollars' . ' ($' . $monthlyPerMachine . ')',
                    'months_count' => NumberToWordsConverterHelper::convert($model->duration) . '(' . $model->duration . ')',
                    'installment_cost' => NumberToWordsConverterHelper::convert($monthlySum) . ' Dollars' . ' ($' . $monthlySum . ')',
                    'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    // 'sum_price' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'deposit_total' => ($depositAmount) ? NumberToWordsConverterHelper::convert($depositTotal) . ' Dollars' . ' ($' . $depositTotal . ')' : '/',
                    'deposit_price' => ($depositAmount) ? NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')' : '/',
                    'lease_number_model' => $lease_number_model,
                    'buy_out' => NumberToWordsConverterHelper::convert($model->buy_out) . ' Dollars' . ' ($' . $model->buy_out . ')',
                ];
            }else if ($templateType === Agreement::PURCHASE_TYPE) {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
                $sumPrice = round($model->machine_price / $conversionRate / 100,2) * $model->machine_quantity;
                $monthlySum =  round($model->price / $conversionRate / 100,2) * $model->machine_quantity;
                $depositAmount = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $machinePrice = round($model->machine_price / $conversionRate / 100, 2);
                $left_to_pay = round($sumPrice - $depositAmount, 2);
                // Fourteen (14) Megaformer, Model MEGA PRO
                $purchase_number_model = NumberToWordsConverterHelper::convert($model->machine_quantity) . ' (' . $model->machine_quantity . ') ' . $model->machine->name . ', Model ' . $model->machine->name;
                // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
                $purchase_amount = NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')';
                // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
                $purchase_per_machine = $model->machine->name . ' is ' . NumberToWordsConverterHelper::convert($machinePrice) . ' Dollars' . ' ($' . $machinePrice . ')';

                return [
                    'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                    'agreement_lagree_company' => $company->name,
                    'agreement_company' => $model->customer->name,

                    'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                    'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                    'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                    'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                    'agreement_annual_fee' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'agreement_deposit' => NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')',
                    'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                    'agreement_annual' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
                    'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    'purchase_number_model' => $purchase_number_model, // Fourteen (14) Megaformer, Model MEGA PRO
                    'purchase_amount' => $purchase_amount, // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
                    'purchase_per_machine' => $purchase_per_machine, // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
                ];
            } else {
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;

                $licenseFee = round($model->price / $conversionRate / 100, 2);
                $deposit = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
                $left_to_pay = round(($model->price - $model->deposit_amount) / $conversionRate / 100, 2);

                if($templateNames[0] == 'IMS-MINI-FINAL') {
                    Log::info('IMS-MINI-FINAL');

                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,

                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-MICRO-FINAL') {
                    Log::info('IMS-MICRO-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-MEGA-FINAL') {
                    Log::info('IMS-MEGA-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,

                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MINI-FINAL') {
                    Log::info('IMS-INT-MINI-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MICRO-FINAL') {
                    Log::info('IMS-INT-MICRO-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else if($templateNames[0] == 'IMS-INT-MEGA-FINAL') {
                    Log::info('IMS-INT-MEGA-FINAL');
                    return [
                        'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'agreement_lagree_company' => $company->name,
                        'agreement_company' => $model->customer->name,

                        'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
                        'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
                        'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
                        'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                        
                        'agreement_annual_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_deposit' => NumberToWordsConverterHelper::convert($deposit) . ' Dollars' . ' ($' . $deposit . ')',
                        'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
                        'agreement_annual' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
                    ];    
                }else{
                    return [
                        'current_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
                        'customer_name' => $model->customer->owner->first_name . ' ' . $model->customer->owner->last_name,
                        'customer_company_name' => $model->customer->name,
                        'zip_code' => $model->studio->zip,
                        'monthly_fee' => NumberToWordsConverterHelper::convert($licenseFee) . ' Dollars' . ' ($' . $licenseFee . ')',
                        'company_name' => $company->name,
                        'state' => $company->state->name,
                        'company_address' => $company->address,
                    ];
                }
            }
        }
        return [];
    }

    public static function fillHTML(string $templateType, array $templateNames, array $prefillData, object $customer)
    {
        $fileName = '';

        if($templateType == Agreement::LEASE_TYPE) {
            $fillHTML = view('adobe-files.lease', ['data' => $prefillData])->render();
            $fileName = 'IMS-LEASE-FINAL-' . $customer->id . '.html';
            $filePath = public_path($fileName);
            file_put_contents($filePath, $fillHTML);
        }else if($templateType == Agreement::LICENSE_TYPE) {
            if($templateNames[0] == 'IMS-MINI-FINAL') {
                $fillHTML = view('adobe-files.license-mini', ['data' => $prefillData])->render();
                $fileName = 'IMS-MINI-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }else if($templateNames[0] == 'IMS-MICRO-FINAL') {
                $fillHTML = view('adobe-files.license-micro', ['data' => $prefillData])->render();
                $fileName = 'IMS-MICRO-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }else if($templateNames[0] == 'IMS-MEGA-FINAL') {
                $fillHTML = view('adobe-files.license-mega', ['data' => $prefillData])->render();
                $fileName = 'IMS-MEGA-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }else if($templateNames[0] == 'IMS-INT-MINI-FINAL') {
                $fillHTML = view('adobe-files.license-int-mini', ['data' => $prefillData])->render();
                $fileName = 'IMS-INT-MINI-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }else if($templateNames[0] == 'IMS-INT-MICRO-FINAL') {
                $fillHTML = view('adobe-files.license-int-micro', ['data' => $prefillData])->render();
                $fileName = 'IMS-INT-MICRO-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }else if($templateNames[0] == 'IMS-INT-MEGA-FINAL') {
                $fillHTML = view('adobe-files.license-int-mega', ['data' => $prefillData])->render();
                $fileName = 'IMS-INT-MEGA-FINAL-' . $customer->id . '.html';
                $filePath = public_path($fileName);
                file_put_contents($filePath, $fillHTML);
            }
        }else if($templateType == Agreement::PURCHASE_TYPE) {
            $fillHTML = view('adobe-files.purchase', ['data' => $prefillData])->render();
            $fileName = 'IMS-PURCHASE-FINAL-' . $customer->id . '.html';
            $filePath = public_path($fileName);
            file_put_contents($filePath, $fillHTML);
        }

        return $fileName;
    }

    public static function createAgreement(Customer $customer, Model $model, string $templateType, Company $company, string $baseUri, array $templateNames)
    {
        $prefillData = self::getPrefillData(/* $templateFieldsNames,  */$model, $templateType, $company, $templateNames);

        $fillHTML = self::fillHTML($templateType, $templateNames, $prefillData, $customer);

        if ($fillHTML != '') {            

            $service = new self();
            $response = $service->adobeHtmlSend($fillHTML, $customer, $templateType, $company);

            // Extract data from JsonResponse object
            if ($response instanceof \Illuminate\Http\JsonResponse) {
                $responseData = $response->getData(true); // true to get as array
            } else {
                $responseData = $response;
            }

            // echo '<pre>';
            // print_r($responseData);
            // die();
            
            // Log::info('Create agreement response: ' . json_encode($response));

            if ($responseData['success']) {            
                $agreementData = [
                    'adobe_template_id' => '',
                    'customer_id' => $customer->id,
                    'adobe_agreement_id' => $responseData['agreement_id'],
                    'send_date' => now(),
                    'status' => 0,
                    'license_id' => $templateType === 'license' ? $model->id : NULL,
                    'lease_id' => $templateType === 'lease' ? $model->id : NULL,
                    'purchase_id' => $templateType === 'purchase' ? $model->id : NULL,
                    'location' => $model->studio_id,
                    'type' => $templateType
                ];
                AgreementService::store($agreementData);
            } else {
                throw new \Exception($responseData);
            }
        }else{
            throw new \Exception('No HTML file created');
        }
    }

    public static function previewAgreement(Customer $customer, Model $model, string $templateType, Company $company, array $templateNames)
    {
        $prefillData = self::getPrefillData($model, $templateType, $company, $templateNames);
        $fillHTML = self::fillHTML($templateType, $templateNames, $prefillData, $customer);

        if ($fillHTML != '') {
            return view('adobe-files.purchase', compact('fillHTML'));
        }else{
            throw new \Exception('No HTML file created');
        }
    }

    public static function getAgreementInfo(string $agreement_id)
    {
        $baseUri = self::getBaseUri();
        $url = $baseUri . config('adobe-sign.agreements') . '/' . $agreement_id . '/signingUrls';

        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];
        
        $response = Http::withHeaders($headers)->get($url);

        $responseData = $response->json();

        return $responseData;
    }

    public static function getDocumentAndSaveToStorage(Agreement $agreement, string $baseUri): void
    {
        $agreementEndpoint = $baseUri . config('adobe-sign.agreements') . '/' . $agreement->adobe_agreement_id . '/combinedDocument';

        $headers = [
            'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
            'Content-Type' => 'application/json',
        ];

        $response = Http::withHeaders($headers)
            ->get($agreementEndpoint);

        if ($response->ok())
        {
            $fileName = 'agreement_' . $agreement->adobe_agreement_id . '.pdf';
            $filePath = 'customer/' . $agreement->customer_id . '/agreements/' .  $fileName;
            Storage::disk('public')->put($filePath, $response->body());

            $agreement->update([
                'filepath' => $filePath,
                'completed_date' => now(),
                'status' => Agreement::STATUSES['Signed 2/2 (wait4pay)'],
            ]);

        } else {
            throw new \Exception($response->body());
        }
    }

    public function adobeHtmlSend($fileName, Customer $customer, string $type, Company $company)
    {
        try {
            // Validate required configuration
            $accessToken = config('adobe-sign.access_token');
            $appUrl = env('APP_URL');
            // $fileName = 'IMS-LEASE-FINAL.html';

            if (empty($accessToken)) {
                \Log::error('Adobe Sign: Access token not configured');
                return response()->json([
                    'success' => false,
                    'error' => 'Adobe Sign access token not configured'
                ], 500);
            }

            if (empty($appUrl)) {
                \Log::error('Adobe Sign: APP_URL not configured');
                return response()->json([
                    'success' => false,
                    'error' => 'Application URL not configured'
                ], 500);
            }

            // Validate file exists
            $filePath = public_path($fileName);
            if (!file_exists($filePath)) {
                \Log::error('Adobe Sign: HTML file not found', ['path' => $filePath]);
                return response()->json([
                    'success' => false,
                    'error' => 'HTML file not found: ' . $fileName
                ], 404);
            }

            // Check file is readable
            if (!is_readable($filePath)) {
                \Log::error('Adobe Sign: HTML file not readable', ['path' => $filePath]);
                return response()->json([
                    'success' => false,
                    'error' => 'HTML file not readable: ' . $fileName
                ], 403);
            }

            // Initialize cURL with error handling
            $curl = curl_init();
            if ($curl === false) {
                \Log::error('Adobe Sign: Failed to initialize cURL');
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to initialize cURL connection'
                ], 500);
            }

            // Configure cURL options
            $curlOptions = [
                CURLOPT_URL => 'https://api.na1.adobesign.com/api/rest/v6/transientDocuments?File-Name=' . urlencode($fileName) . '&Mime-Type=text%2Fhtml',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30, // Set reasonable timeout
                CURLOPT_CONNECTTIMEOUT => 10, // Connection timeout
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => [
                    'File' => new \CURLFile($filePath),
                    'File-Name' => $fileName,
                    'Mime-Type' => 'text/html'
                ],
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $accessToken
                ],
                CURLOPT_SSL_VERIFYPEER => false, // Enable SSL verification
                CURLOPT_SSL_VERIFYHOST => 2,
                CURLOPT_USERAGENT => 'ERP-System/1.0'
            ];

            curl_setopt_array($curl, $curlOptions);

            // Execute cURL request
            $response = curl_exec($curl);

            // Check for cURL errors
            if ($response === false) {
                $curlError = curl_error($curl);
                $curlErrno = curl_errno($curl);
                curl_close($curl);

                \Log::error('Adobe Sign: cURL execution failed', [
                    'error' => $curlError,
                    'errno' => $curlErrno
                ]);

                return response()->json([
                    'success' => false,
                    'error' => 'Network request failed: ' . $curlError,
                    'error_code' => $curlErrno
                ], 500);
            }

            // Get HTTP status code
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $responseTime = curl_getinfo($curl, CURLINFO_TOTAL_TIME);
            curl_close($curl);

            // Log request details
            \Log::info('Adobe Sign: Transient document upload attempt', [
                'file' => $fileName,
                'http_code' => $httpCode,
                'response_time' => $responseTime . 's'
            ]);

            // Parse response
            $responseData = json_decode($response, true);

            // Handle different HTTP status codes
            switch ($httpCode) {
                case 200:
                case 201:
                    // Success
                    if (isset($responseData['transientDocumentId'])) {
                        \Log::info('Adobe Sign: Transient document uploaded successfully');
                        $docID = $responseData['transientDocumentId'];

                        // echo $docID . '<br>';
                        // return response()->json([
                        //     'success' => true,
                        //     'message' => 'Document uploaded successfully to Adobe Sign',
                        //     'data' => $responseData,
                        //     'document_id' => $responseData['transientDocumentId']
                        // ]);
                    } else {
                        \Log::warning('Adobe Sign: Unexpected success response format', [
                            'response' => $responseData
                        ]);

                        return response()->json([
                            'success' => true,
                            'message' => 'Document uploaded but response format unexpected',
                            'data' => $responseData,
                            'document_id' => ''
                        ]);
                    }
                    break;

                case 400:
                    \Log::error('Adobe Sign: Bad request', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Bad request to Adobe Sign API',
                        'details' => $responseData['message'] ?? 'Invalid request parameters'
                    ], 400);

                case 401:
                    \Log::error('Adobe Sign: Unauthorized access', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Unauthorized access to Adobe Sign API',
                        'details' => 'Please check your access token'
                    ], 401);

                case 403:
                    \Log::error('Adobe Sign: Forbidden access', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Forbidden access to Adobe Sign API',
                        'details' => 'Insufficient permissions'
                    ], 403);

                case 404:
                    \Log::error('Adobe Sign: API endpoint not found', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Adobe Sign API endpoint not found',
                        'details' => 'Please check the API URL'
                    ], 404);

                case 429:
                    \Log::error('Adobe Sign: Rate limit exceeded', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Rate limit exceeded',
                        'details' => 'Too many requests to Adobe Sign API'
                    ], 429);

                case 500:
                case 502:
                case 503:
                case 504:
                    \Log::error('Adobe Sign: Server error', [
                        'http_code' => $httpCode,
                        'response' => $responseData
                    ]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Adobe Sign server error',
                        'details' => 'Please try again later',
                        'http_code' => $httpCode
                    ], 500);

                default:
                    \Log::error('Adobe Sign: Unexpected HTTP status', [
                        'http_code' => $httpCode,
                        'response' => $responseData
                    ]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Unexpected response from Adobe Sign API',
                        'details' => 'HTTP Status: ' . $httpCode,
                        'response' => $responseData
                    ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Adobe Sign: Unexpected error in adobeHtmlSend', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An unexpected error occurred',
                'details' => $e->getMessage()
            ], 500);
        }

        switch ($type) {
            case 'lease':
                $agreement_name = 'Lease Agreement';
                break;
            case 'purchase':
                $agreement_name = 'Purchase Agreement';
                break;
            case 'license':
                $agreement_name = 'License Agreement';
                break;
            default:
                $agreement_name = 'Equipment Agreement';
                break;
        }

        // set doc to adobe
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.na1.adobesign.com/api/rest/v6/agreements',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'{
                "fileInfos":[
                    { "transientDocumentId": "' . $docID . '" }
                ],
                "name":"' . $agreement_name . '",
                "participantSetsInfo":[
                    {
                    "memberInfos":[ { "name":"' . $customer->name . '", "email":"' . $customer->owner->email . '", "securityOption": { "authenticationMethod": "NONE" } } ],
                    "order":1,
                    "role":"SIGNER",
                    "securityOption": [ {
                        "authenticationMethod": "NONE" }
                        ]
                    },
                    {
                    "memberInfos":[ { "name":"' . env('ADOBE_SIGN_NAME') . '", "email":"' . env('ADOBE_SIGN_EMAIL') . '" } ],
                    "order":2,
                    "role":"APPROVER"
                    }
                ],
                "signatureType":"ESIGN",
                "state":"IN_PROCESS"
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken
            ),
            CURLOPT_SSL_VERIFYPEER => false, // Enable SSL verification
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'ERP-System/1.0'
        ));
        $response = curl_exec($curl);
        $responseFinal = json_decode($response, true);

        curl_close($curl);
        
        return response()->json([
            'success' => true,
            'message' => 'Document uploaded successfully to Adobe Sign',
            'first_data' => $responseData,
            'data' => $responseFinal,
            'docID' => $docID,
            'httpCode' => $httpCode,
            'agreement_id' => $responseFinal['id']
        ]);
    }

}

