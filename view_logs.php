<?php
// Simple log viewer - REMOVE THIS FILE AFTER DEBUGGING!
// Access via: https://ims.lagreefitness.com/view_logs.php

// Security check - only allow from specific IPs or remove this file after use
$allowed_ips = ['YOUR_IP_ADDRESS']; // Replace with your IP
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && !empty($allowed_ips[0])) {
    die('Access denied');
}

$logFile = __DIR__ . '/storage/logs/laravel.log';

echo "<h1>Laravel Log Viewer</h1>";
echo "<p><strong>Log file:</strong> $logFile</p>";

if (!file_exists($logFile)) {
    echo "<p style='color: red;'>Log file does not exist!</p>";
    echo "<p>Checking storage directory structure:</p>";
    
    $storageDir = __DIR__ . '/storage';
    if (is_dir($storageDir)) {
        echo "<p>✓ Storage directory exists</p>";
        
        $logsDir = $storageDir . '/logs';
        if (is_dir($logsDir)) {
            echo "<p>✓ Logs directory exists</p>";
            $files = scandir($logsDir);
            echo "<p>Files in logs directory: " . implode(', ', array_diff($files, ['.', '..'])) . "</p>";
        } else {
            echo "<p>❌ Logs directory does not exist</p>";
            echo "<p>Creating logs directory...</p>";
            if (mkdir($logsDir, 0755, true)) {
                echo "<p>✓ Logs directory created</p>";
            } else {
                echo "<p>❌ Failed to create logs directory</p>";
            }
        }
    } else {
        echo "<p>❌ Storage directory does not exist</p>";
    }
    exit;
}

if (!is_readable($logFile)) {
    echo "<p style='color: red;'>Log file is not readable! Check permissions.</p>";
    exit;
}

// Get file size
$fileSize = filesize($logFile);
echo "<p><strong>File size:</strong> " . number_format($fileSize) . " bytes</p>";

// Show last 100 lines
$lines = file($logFile);
$totalLines = count($lines);
$showLines = min(100, $totalLines);
$startLine = max(0, $totalLines - $showLines);

echo "<h2>Last $showLines lines (of $totalLines total):</h2>";

// Filter for Adobe Sign related entries
$adobeLines = [];
$allLines = [];

for ($i = $startLine; $i < $totalLines; $i++) {
    $line = $lines[$i];
    $allLines[] = $line;
    
    if (stripos($line, 'adobe') !== false || stripos($line, 'echosign') !== false) {
        $adobeLines[] = $line;
    }
}

if (!empty($adobeLines)) {
    echo "<h3>Adobe Sign Related Entries:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: scroll;'>";
    foreach ($adobeLines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
}

echo "<h3>All Recent Log Entries:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: scroll;'>";
foreach ($allLines as $line) {
    // Highlight error lines
    if (stripos($line, 'error') !== false) {
        echo "<span style='color: red; font-weight: bold;'>" . htmlspecialchars($line) . "</span>";
    } elseif (stripos($line, 'warning') !== false) {
        echo "<span style='color: orange;'>" . htmlspecialchars($line) . "</span>";
    } elseif (stripos($line, 'adobe') !== false || stripos($line, 'echosign') !== false) {
        echo "<span style='color: blue; font-weight: bold;'>" . htmlspecialchars($line) . "</span>";
    } else {
        echo htmlspecialchars($line);
    }
}
echo "</pre>";

echo "<p><em>Remember to delete this file after debugging!</em></p>";
?>
