@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="page-title">
            <div class="title-left">
                <h3>{{ __('new supplier') }}</h3>
                <a href="{{ route('admin.suppliers.index') }}" class="back-link">← Back</a>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.suppliers.store') }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('status') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'is_active',
                'field_label' => 'Radio group status',
                'values' => ['status_active' => ['text' => 'Active', 'value' => '1'], 'status_inactive' => ['text' => 'Inactive', 'value' => '0']],
                'field' => 'is_active',
                'required' => 'required',
                'checked' => '1',
            ])

            <h5 class="form-section-title">{{ __('Supplier Info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'NAME *',
                'field_type' => 'text',
            ])

            @include('partials.forms.input', [
                'field_name' => 'email',
                'field_label' => 'EMAIL ADDRESS *',
                'field_type' => 'email',
            ])

            @include('partials.forms.checkbox', [
                'field_name' => 'toggle',
                'field_to_toggle' => 'email2_container',
                'field_label' => 'Another email address',
                'wrap_class_name' => ' mb-5 toggle_bottom',
            ])

            @include('partials.forms.input', [
                'field_name' => 'email2',
                'field_label' => 'EMAIL ADDRESS',
                'field_type' => 'email',
                'field_class' => 'd-none email2_container',
            ])

            @include('partials.forms.input', [
                'field_name' => 'phone',
                'field_label' => 'PHONE #',
                'field_type' => 'text',
            ])

            <h5 class="form-section-title">{{ __('address info') }}</h5>

            @include('partials.forms.radio-group', [
               'field_name' => 'location',
               'field_label' => 'Radio group location',
               'field_class' => 'mb-7',
               'values' => [
                    'location_usa' => [
                        'text' => 'USA', 
                        'value' => 'USA'
                    ], 
                    'location_international' => [
                        'text' => 'International', 
                        'value' => 'International'
                    ]
                ],
               'field' => 'status',
               'required' => 'required',
               'checked' => 'USA'
           ])
           
            @include('partials.forms.input', [
                'field_name' => 'address',
                'field_label' => 'ADDRESS',
                'field_type' => 'text',
            ])

            @include('partials.forms.input', [
                'field_name' => 'address2',
                'field_label' => 'ADDRESS 2',
                'field_type' => 'text',
            ])

            @include('partials.forms.input', [
                'field_name' => 'city',
                'field_label' => 'CITY',
                'field_type' => 'text',
            ])

            @include('partials.forms.input', [
                'field_name' => 'zip',
                'field_label' => 'ZIP CODE',
                'field_type' => 'text',
            ])

            <div id="location_usa_container">
                @include('partials.forms.select', [
                    'field_name' => 'state_id',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => 0,
                    'include_empty' => true
                ])                
            </div>

            <div id="location_international_container" style="display: none;">
                @include('partials.forms.select', [
                    'field_name' => 'country_id',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'countries',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => 0,
                    'include_empty' => true
                ])
            </div>

            <h5 class="form-section-title">{{ __('Contact Person') }}</h5>

            @include('partials.forms.input', [
                    'field_name' => 'contact[first_name]',
                    'field_label' => 'FIRST NAME *',
                    'field_type' => 'text',
                ])

            @include('partials.forms.input', [
                    'field_name' => 'contact[last_name]',
                    'field_label' => 'LAST NAME *',
                    'field_type' => 'text',
                ])

            @include('partials.forms.input', [
                   'field_name' => 'contact[position]',
                   'field_label' => 'POSITION',
                   'field_type' => 'text',
               ])

            @include('partials.forms.input', [
                    'field_name' => 'contact[phone]',
                    'field_label' => 'PHONE #',
                    'field_type' => 'text',
                ])

            @include('partials.forms.input', [
                    'field_name' => 'contact[email]',
                    'field_label' => 'EMAIL ADDRESS',
                    'field_type' => 'email',
                ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.suppliers.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
<script>
    document.addEventListener('DOMContentLoaded', function () {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }
        document.getElementById('location_international').addEventListener('click', function () {
            console.log('change');
            document.getElementById('location_usa_container').style.display = 'none';
            document.getElementById('location_international_container').style.display = 'block';
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        });        
        document.getElementById('location_usa').addEventListener('click', function () {
            document.getElementById('location_usa_container').style.display = 'block';
            document.getElementById('location_international_container').style.display = 'none';
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        });

        // Initialize Google Places Autocomplete
        window.initGooglePlaces = function() {
            const autocompleteService = new GooglePlacesAutocomplete();

            const fieldConfigs = [
                {
                    inputId: 'address',
                    fields: {
                        address: 'address',
                        city: 'city',
                        zip: 'zip',
                        state: 'state_id',
                        country: 'country_id'
                    },
                    locationRadios: {
                        usa: 'location_usa',
                        international: 'location_international'
                    }
                }
            ];

            autocompleteService.init(fieldConfigs);
        };

        // Load Google Maps API
        const googleApiKey = '{{ config("services.google_maps.api_key") }}';
        if (googleApiKey && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
            GooglePlacesAutocomplete.loadGoogleMapsAPI(googleApiKey, window.initGooglePlaces);
        }
    });
</script>

<!-- Google Places Autocomplete Script -->
@vite('resources/js/google-places-autocomplete.js')
