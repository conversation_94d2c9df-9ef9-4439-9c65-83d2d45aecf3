<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Customer\StoreCustomerRequest;
use App\Http\Requests\Admin\Customer\UpdateCustomerRequest;
use App\Http\Requests\Admin\Customer\UpdateB2CCustomerRequest;
use App\Http\Requests\Admin\CustomerAgreement\AddCustomerAgreementRequest;
use App\Http\Requests\Admin\LeaseFile\AddLeaseFileRequest;
use App\Mail\ContactCustomer;
use App\Models\AdminNotification;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\Lease;
use App\Models\License;
use App\Models\Studio;
use App\Models\Machine;
use App\Models\Payment;
use App\Models\Purchase;
use App\Models\State;
use App\Models\Countries;
use App\Models\CustomerAgreement;
use App\Services\Admin\Customer\ICustomerService;
use App\Services\Admin\CustomerAgreement\ICustomerAgreementService;
use App\Services\Admin\Dashboard\DashboardService;
use App\Services\Admin\Invoice\IInvoiceService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\View\View;

use Illuminate\Support\Facades\Route;

class CustomerController extends Controller
{
    private ICustomerService $customerService;
    private ICustomerAgreementService $customerAgreementService;

    public function __construct(
        ICustomerService $customerService,
        ICustomerAgreementService $customerAgreementService,
    ) {
        $this->customerService = $customerService;
        $this->customerAgreementService = $customerAgreementService;
    }

    public function index(): View
    {
        return view('admin.customers.index');
    }

    public function studio(): View
    {
        return view('admin.customers.studio-index');
    }

    public function b2c(): View
    {
        return view('admin.customers.b2c-index');
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';
        $status = $request->get('status') ?? '';
        $type = $request->get('type') ?? '';

        $customers = $this->customerService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $type
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }

        foreach ($customers as $customer) {
            $customer['sequential_id'] = $sequential_id++;

            // If calculated_remaining exists (from service sorting), use it; otherwise calculate
            if (isset($customer->calculated_remaining)) {
                $customer['remaining'] = $customer->calculated_remaining;

                // Still need to calculate individual components for display
                $licenseType = $customer->licenses->where('type', Constants::LICENSE_TYPES[0]);
                $allLicenseTypePayments = $licenseType->flatMap(function ($license) {
                    return $license->payments;
                });
                $customer['licenses_remaining'] = $this->customerService->licensesRemainingSum($allLicenseTypePayments, $currentCurrency);

                $exclusivityType = $customer->licenses->where('type', Constants::LICENSE_TYPES[1]);
                $allExclusivityTypePayments = $exclusivityType->flatMap(function ($license) {
                    return $license->payments;
                });
                $customer['exclusivity_remaining'] = $this->customerService->licensesRemainingSum($allExclusivityTypePayments, $currentCurrency);

                $allLeasePayments = $customer->leases->flatMap(function ($lease) {
                    return $lease->payments;
                });
                $leasesById = $customer->leases->keyBy('id');
                $customer['lease_remaining'] = $this->customerService->leasesRemainingSum($allLeasePayments, $currentCurrency, $leasesById);
            } else {
                // Normal calculation for non-remaining sorts
                $licenseType = $customer->licenses->where('type', Constants::LICENSE_TYPES[0]);
                $allLicenseTypePayments = $licenseType->flatMap(function ($license) {
                    return $license->payments;
                });
                $customer['licenses_remaining'] = $this->customerService->licensesRemainingSum($allLicenseTypePayments, $currentCurrency);

                $exclusivityType = $customer->licenses->where('type', Constants::LICENSE_TYPES[1]);
                $allExclusivityTypePayments = $exclusivityType->flatMap(function ($license) {
                    return $license->payments;
                });
                $customer['exclusivity_remaining'] = $this->customerService->licensesRemainingSum($allExclusivityTypePayments, $currentCurrency);

                $allLeasePayments = $customer->leases->flatMap(function ($lease) {
                    return $lease->payments;
                });
                $leasesById = $customer->leases->keyBy('id');
                $customer['lease_remaining'] = $this->customerService->leasesRemainingSum($allLeasePayments, $currentCurrency, $leasesById);

                $customer['remaining'] = $customer['licenses_remaining'] + $customer['exclusivity_remaining'] + $customer['lease_remaining'];
            }

            $customer['licenses_count'] = $customer->licenses->whereIn('type', Constants::LICENSE_TYPES[0])->count();
            $customer['exclusivity_count'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->count();
            $customer['lease_count'] = $customer->leases->count();

            $customer['active_licenses'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[0])->where('is_active', true);
            $customer['inactive_licenses'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[0])->where('is_active', false);

            $customer['active_exclusivity'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->where('is_active', true);
            $customer['inactive_exclusivity'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->where('is_active', false);

            $customer['active_leases'] = $customer->leases->where('is_active', true);
            $customer['inactive_leases'] = $customer->leases->where('is_active', false);

            $customer['orderParam'] = $orderParam;
            $customer['orderType'] = $orderType;
            //            $customer['exclusivity_monthly_average'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->avg('price');
        }

        $viewFile = '';
        $type === 'b2c' ? $viewFile = 'partials.forms.customers-b2c-search' : $viewFile = 'partials.forms.customers-search';
        $viewContent = view($viewFile, compact('customers', 'currentCurrency'))->render();

        return response()->json($viewContent);
    }

    public function b2c_search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        // dd($searchData);
        // $searchData['type'] = 'b2c';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';
        $status = $request->get('status') ?? '';
        $type = $request->get('type') ?? '';

        $customers = $this->customerService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $type
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }

        foreach ($customers as $customer) {
            $customer['sequential_id'] = $sequential_id++;

            $licenseType = $customer->licenses->where('type', Constants::LICENSE_TYPES[0]);
            $allLicenseTypePayments = $licenseType->flatMap(function ($license) {
                return $license->payments;
            });

            $customer['licenses_count'] = $customer->licenses->whereIn('type', Constants::LICENSE_TYPES[0])->count();
            $customer['licenses_remaining'] = $this->customerService->licensesRemainingSum($allLicenseTypePayments, $currentCurrency);

            $exclusivityType = $customer->licenses->where('type', Constants::LICENSE_TYPES[1]);
            $allExclusivityTypePayments = $exclusivityType->flatMap(function ($license) {
                return $license->payments;
            });
            $customer['exclusivity_count'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->count();
            $customer['exclusivity_remaining'] = $this->customerService->licensesRemainingSum($allExclusivityTypePayments, $currentCurrency);


            $allLeasePayments = $customer->leases->flatMap(function ($lease) {
                return $lease->payments;
            });

            $leasesById = $customer->leases->keyBy('id');
            $customer['lease_count'] = $customer->leases->count();
            $customer['lease_remaining'] = $this->customerService->leasesRemainingSum($allLeasePayments, $currentCurrency, $leasesById);

            $customer['remaining'] = $customer['licenses_remaining'] + $customer['exclusivity_remaining'] + $customer['lease_remaining'];

            $customer['active_licenses'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[0])->where('is_active', true);
            $customer['inactive_licenses'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[0])->where('is_active', false);

            $customer['active_exclusivity'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->where('is_active', true);
            $customer['inactive_exclusivity'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->where('is_active', false);

            $customer['active_leases'] = $customer->leases->where('is_active', true);
            $customer['inactive_leases'] = $customer->leases->where('is_active', false);

            $customer['orderParam'] = $orderParam;
            $customer['orderType'] = $orderType;
            //            $customer['exclusivity_monthly_average'] = $customer->licenses->where('type', Constants::LICENSE_TYPES[1])->avg('price');
        }

        $viewContent = view('partials.forms.customers-b2c-search', compact('customers', 'currentCurrency'))->render();

        return response()->json($viewContent);
    }

    public function show(Customer $customer): View
    {
        $customer = $customer->load('owner');
        return view('partials.forms.customer-profile', compact('customer'));
    }

    public function create(): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        return view('admin.customers.create', compact('states_countries'));
    }

    public function b2c_create(): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        $type = 'b2c';
        $title = 'New b2c customer';
        $redirect = "admin.customers.b2c.index";
        $form_address = "admin.customers.b2c.store";
        // return view('admin.customers.b2c-create', compact('states_countries', 'type', 'title', 'redirect', 'form_address'));
        return view('admin.customers.create', compact('states_countries', 'type', 'title', 'redirect', 'form_address'));
    }

    public function studio_create(): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        $type = 'studio';
        $title = 'New studio';
        $redirect = "admin.customers.studio.index";
        $form_address = "admin.customers.studio.store";
        // return view('admin.customers.b2c-create', compact('states_countries', 'type', 'title', 'redirect', 'form_address'));
        return view('admin.customers.create', compact('states_countries', 'type', 'title', 'redirect', 'form_address'));
    }

    public function store(StoreCustomerRequest $request)
    {
        try {
            $this->customerService->store($request->validated());
            toastr()->addSuccess('', 'Customer created successfully.');
            return redirect()->route('admin.customers.index');
        } catch (\Exception $e) {
            // dd($e);
            toastr()->addError('Customer creation failed');
            return redirect()->back();
        }
    }

    public function b2c_store(StoreCustomerRequest $request)
    {
        try {
            $customer_information = $request->validated();
            $customer_information['customer_name'] = $customer_information['owner_first_name'] . ' ' . $customer_information['owner_last_name'];
            $this->customerService->store($customer_information);
            toastr()->addSuccess('', 'B2c Customer created successfully.');
            return redirect()->route('admin.customers.b2c.index');
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Customer creation failed');
            return redirect()->back();
        }
    }

    public function studio_store(StoreCustomerRequest $request)
    {
        try {
            $customer_information = $request->validated();
            // $customer_information['customer_name'] = $customer_information['owner_first_name'] . ' ' . $customer_information['owner_last_name'];
            $this->customerService->store($customer_information);
            toastr()->addSuccess('', 'New studio created successfully.');
            return redirect()->route('admin.customers.studio.index');
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Customer creation failed');
            return redirect()->back();
        }
    }


    public function edit(Customer $customer): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        $customer = $customer->load('owner');

        return view('admin.customers.edit', compact('customer', 'states_countries'));
    }

    public function b2c_edit(Customer $customer): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        $customer = $customer->load('owner');

        return view('admin.customers.b2c-edit', compact('customer', 'states_countries'));
    }

    public function update(Customer $customer, UpdateCustomerRequest $request)
    {
        try {
            $this->customerService->update($request->validated(), $customer);
            toastr()->addSuccess('', 'Customer updated successfully.');
            return redirect()->route('admin.customers.edit', $customer);
        } catch (\Exception $e) {
            // dd($e);
            toastr()->addError('Customer update failed');
            return redirect()->back();
        }
    }

    public function b2c_update(Customer $customer, UpdateCustomerRequest $request)
    {
        $customer_information = $request->validated();
        $customer_information['customer_name'] = $customer_information['owner_first_name'] . ' ' . $customer_information['owner_last_name'];
        $customer_information['shipping_location'] = "";
        $customer_information['shipping_address'] = "";
        $customer_information['shipping_city'] = "";
        $customer_information['shipping_zip'] = "";
        $customer_information['shipping_state_id'] = null;
        $customer_information['shipping_country_id'] = null;
        $customer_information['use_billing_address'] = "";
        // dd($customer_information);
        try {
            $this->customerService->update($customer_information, $customer);
            toastr()->addSuccess('', 'Customer updated successfully.');
            return redirect()->route('admin.customers.edit', $customer);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Customer update failed');
            return redirect()->back();
        }
    }

    public function destroy(Customer $customer)
    {
        try {
            $this->customerService->delete($customer);
            toastr()->addSuccess('', 'Customer deleted successfully.');
            return redirect(route('admin.customers.studio.index'));
        } catch (\Exception $e) {
            toastr()->addError('Customer delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $customerIds = $request->input('selectedItems');
            foreach ($customerIds as $customerId) {
                $this->customerService->delete(Customer::find($customerId));
            }
            toastr()->addSuccess('', 'Selected customers are deleted successfully.');

            return redirect(route('admin.customers.index'));
        } catch (\Exception $e) {
            toastr()->addError('Customer delete failed');
            return redirect()->back();
        }
    }

    public function showDashboard(Customer $customer, Request $request): View
    {
        $licenseCount = $customer->licenses->where('status', '3')->count();
        $leaseCount = $customer->leases->where('status', '3')->count();
        $purchaseCount = $customer->purchases->where('status', '3')->count();
        $notesCount = $customer->notes->count();
        $tab = $request->query('tab', 'dashboard');
        $agreementCount = $customer->agreements()->where('custom', 'custom')->count();
        // $locations = Studios::query()->get();
        // $locations = Customer::distinct()->pluck('city');
        // $locations = collect($locations)->map(function ($location, $index) {
        //     return (object) ['id' => $index + 1, 'name' => $location];
        // });
        // $locations = Studio::all()
        //     ->values()
        //     ->map(function ($studio, $index) {
        //         return (object) ['id' => $index + 1, 'name' => $studio->name];
        //     });

        $licenses = License::select(DB::raw('GROUP_CONCAT(studio_id) as ids'))->where('customer_id', $customer->id)->get();
        if(!empty($licenses[0]['ids'])) {
            $locations = Studio::select('id', 'name')->whereIn('id', explode(',', (string)$licenses[0]['ids']))->get();
        }else{
            $locations = NULL;
        }

        $types = ['license', 'lease'/* , 'exclusivity' */];
        return view('admin.customers.dashboard', compact('customer', 'tab', 'licenseCount', 'leaseCount', 'purchaseCount', 'notesCount', 'agreementCount', 'locations', 'types'));
    }

    public function showB2cDashboard(Customer $customer, Request $request): View
    {
        $licenseCount = $customer->licenses->where('is_active', true)->count();
        $leaseCount = $customer->leases->where('is_active', true)->count();
        $notesCount = $customer->notes->count();
        $tab = $request->query('tab', 'dashboard');
        $agreementCount = $customer->agreements()->count();
        // $locations = Studios::query()->get();
        // $locations = Customer::distinct()->pluck('city');
        // $locations = collect($locations)->map(function ($location, $index) {
        //     return (object) ['id' => $index + 1, 'name' => $location];
        // });
        $licenses = License::select(DB::raw('GROUP_CONCAT(studio_id) as ids'))->where('customer_id', $customer->id)->get();
        if(!empty($licenses[0]['ids'])) {
            $locations = Studio::select('id', 'name')->whereIn('id', explode(',', (string)$licenses[0]['ids']))->get();
        }else{
            $locations = NULL;
        }
        $types = ['license', 'lease', 'exclusivity'];
        return view('admin.customers.b2c-dashboard', compact('customer', 'tab', 'licenseCount', 'leaseCount', 'notesCount', 'agreementCount', 'locations', 'types'));
    }

    public function loadTabContent(Customer $customer, Request $request)
    {
        $tab = $request->query('tab');
        switch ($tab) {
            case 'dashboard':
                $currentCurrency = AdminSettings::first()->currency;
                $customerPayments = Payment::with('license.conversionRate', 'lease.conversionRate')->where('customer_id', $customer->id)->where('license_id', '!=', null)->orWhere('lease_id', '!=', null)->orderBy('updated_at', 'desc')->get();
                $remainingPayments = $customerPayments->where('status', Constants::PAYMENT_STATUS['not_paid']);
                $remainingSubscriptionsSum = CurrencyConversionHelper::calculatePaymentsSum($remainingPayments, $currentCurrency);
                $licenses = License::where('customer_id', $customer->id)->where('is_active', true)->get();
                $activeLicenses = $licenses->where('type', Constants::LICENSE_TYPES[0])->count();
                $activeExclusivities = $licenses->where('type', Constants::LICENSE_TYPES[1])->count();
                $leases = Lease::where('customer_id', $customer->id)->where('is_active', true)->get();
                $activeLeases = $leases->count();
                $machines = $leases->sum('machine_quantity');
                $purchasedMachines = 0;
                $payments = $customerPayments->where('status', Constants::PAYMENT_STATUS['paid'])->sortByDesc('updated_at')->take(10);
                $sequential_id = 1;
                foreach ($payments as $payment) {
                    $payment['sequential_id'] = $sequential_id++;
                    // $payment['invoice'] = Invoice::where('payment_id', $payment->id)->first();
                    $dashService = new DashboardService();
                    $paymentType = $dashService->getPaymentType($payment);
                    $payment['type'] = $paymentType['type'];
                    $payment['name'] = $paymentType['name'];
                    $payment['sequential_id'] = $sequential_id++;
                    if($payment->license_id){
                        $conversionRate = $payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;                
                    }else if($payment->lease_id){
                        $conversionRate = $payment->lease->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;                
                    }else if($payment->purchase_id){
                        $purchase = Purchase::findOrFail($payment->purchase_id);
                        $conversionRate = $purchase->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
                    }

                    // $conversionRate = ($payment->license_id)
                    //     ? $payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate
                    //     : $payment->lease->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
                    $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);
                }

                return view(
                    'partials.forms.customer-dashboard',
                    compact('payments', 'remainingSubscriptionsSum', 'activeLicenses', 'activeExclusivities', 'activeLeases', 'machines', 'purchasedMachines', 'customer')
                )->render();
            case 'profile':
                return view('partials.forms.customer-profile')->render();
            case 'licence':
                return view('admin.licenses.index', compact('customer'))->render();
            case 'lease':
                return view('admin.leases.index', compact('customer'))->render();
            case 'purchases':
                return view('admin.purchases.index', compact('customer'))->render();
            case 'payment-history':
                return view('admin.payment-history.index', compact('customer'))->render();
            case 'invoices':
                return view('admin.invoices.index', compact('customer'))->render();
            case 'notes':
                $notes = $customer->notes()->orderBy('created_at', 'desc')->get();
                return view('admin.notes.index', compact('notes', 'customer'))->render();
            case 'agreements':
                $agreements = $customer->agreements()->where('custom', 'custom')->orderBy('created_at', 'desc')->get();
                // Fetch licenses associated with the customer;
                foreach($agreements as $k => &$agreement){
                    $studio = Studio::where('id', (int)$agreement->location)->first();
                    $agreement->studio_name = $studio?->name;
                    if($agreement->type == 'license'){
                        if(isset($agreement->custom) AND $agreement->license_id != NULL){

                        }
                        $name = License::where('id', $agreement->license_id)->first();
                        if(isset($name->package) AND $name->package != NULL AND (int)$name->package > 0){
                            $agreement->name = Constants::LICENSE_PACKAGES_KEYS[$name->package];
                        }else{
                            $agreement->name = 'vvv';
                        }
                    }else if($agreement->type == 'lease'){
                        $name = Lease::where('id', $agreement->lease_id)->first();
                        if(isset($name->configuration_id) AND $name->configuration_id != NULL){
                            $machine = Machine::where('id', $name->configuration_id)->first();
                            $agreement->name = $machine->name;
                        }else if(isset($name->machine_id) AND $name->machine_id != NULL){
                            $machine = Machine::where('id', $name->machine_id)->first();
                            $agreement->name = $machine->name;
                        }else{
                            $agreement->name = 'aaa';
                        }
                    }else{
                        $agreement->name = 'bbb';
                    }
                }

                // dd($agreements);

                return view('admin.agreements.index', compact('customer', 'agreements'))->render();
            default:
                return response()->json(['error' => 'Invalid tab'], 400);
        }
    }

    public function contactCustomer(Customer $customer, Request $request)
    {
        try {
            $subject = $request->get('subject');
            $message = $request->get('message');
            Mail::to($customer->owner->email)->send(new ContactCustomer($subject, $message));
            toastr()->addSuccess('Email successfully sent.');
            return redirect()->back();
        } catch (\Exception $e) {
            toastr()->addError('Email send failed');
            return redirect()->back();
        }
    }

    public function getCustomer(Request $request): JsonResponse
    {
        $customer = Customer::find($request->get('customer_id'));
        $customer->load('owner');

        return response()->json(['success' => true, 'customer' => $customer->toArray()]);
    }

    public function store_agreements(Customer $customer, AddCustomerAgreementRequest $request)
    {
        try {
            $validated = $request->validated();
            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $uniqueName = pathinfo($originalName, PATHINFO_FILENAME) . '-' . \Illuminate\Support\Str::random(8) . '.' . $extension;
                $uploadPath = public_path('uploads');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }
                $file->move($uploadPath, $uniqueName);
                $customer->files()->create([
                    'file_name' => $request->file_name ?? $originalName,
                    'location' => $request->location ?: 'default_location',
                    'type' => $request->type,
                    'filepath' => 'uploads/' . $uniqueName,
                    'original_name' => $originalName,
                    'custom' => 'custom',
                    'status' => 4
                ]);
            }
            toastr()->addSuccess('', 'Customer agreement files uploaded successfully.');
            return response()->json([
                'success' => true,
                'message' => 'Customer agreement files uploaded successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Customer agreement file upload failed. ' . $e->getMessage(),
            ]);
        }
    }

    public function search_agreements(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';

        $agreements = $this->customerAgreementService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer
        );

        foreach($agreements as $k => &$agreement){
            $studio = Studio::where('id', $agreement->location)->first();
            $agreement->studio_name = $studio?->name;
        //     if($agreement->type == 'license'){
        //         if(isset($agreement->custom) AND $agreement->custom == 'custom'){
        //             $agreement->name = $agreement->file_name;
        //         }else{
        //             $name = License::where('id', $agreement->license_id)->first();
        //             if(isset($name->package) AND $name->package != NULL AND (int)$name->package > 0){
        //                 $agreement->name = Constants::LICENSE_PACKAGES_KEYS[$name->package];
        //             }else{
        //                 $agreement->name = 'No package selected';
        //             }
        //         }
        //     }else if($agreement->type == 'lease'){
        //         if(isset($agreement->custom) AND $agreement->custom == 'custom'){
        //             $agreement->name = $agreement->file_name;
        //         }else{
        //             $name = Lease::where('id', $agreement->lease_id)->first();
        //             if(isset($name->configuration_id) AND $name->configuration_id != NULL){
        //                 $machine = Machine::where('id', $name->configuration_id)->first();
        //                 $agreement->name = $machine->name;
        //             }else if(isset($name->machine_id) AND $name->machine_id != NULL){
        //                 $machine = Machine::where('id', $name->machine_id)->first();
        //                 $agreement->name = $machine->name;
        //             }else{
        //                 $agreement->name = $agreement->file_name;
        //             }
        //         }
        //     }else if($agreement->type == 'purchase'){
        //         $name = Purchase::where('id', $agreement->purchase_id)->first();
        //         if(isset($name->configuration_id) AND $name->configuration_id != NULL){
        //             $machine = Machine::where('id', $name->configuration_id)->first();
        //             $agreement->name = $machine->name;
        //         }else if(isset($name->machine_id) AND $name->machine_id != NULL){
        //             $machine = Machine::where('id', $name->machine_id)->first();
        //             $agreement->name = $machine->name;
        //         }else{
        //             $agreement->name = $agreement->file_name;
        //         }
        //     }else{
        //         $agreement->name = 'No lease, license or purchase';
        //     }
        }

        $viewContent = view('partials.forms.customer-agreements.customer-agreements-search', compact('agreements', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function toggleStatus(Request $request)
    {
        try {

            $agreement = Agreement::findOrFail($request->agreement_id);
            $agreement->status = $agreement->status == '3' ? '0' : '3';
            $agreement->save();

            toastr()->addSuccess('', "Agreement marked as " . ($agreement->status == '3' ? 'active' : 'inactive') . ".");
            return response()->json([
                'success' => true,
                'new_status' => $agreement->status,
                'message' => "Agreement marked as " . ($agreement->status == '3' ? 'active' : 'inactive') . "."
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating agreement status. ' . $e->getMessage()
            ]);
        }
    }

    public function agreement_download(Request $request)
    {
        try {
            $agreement = Agreement::findOrFail($request->agreement_id);
            $filePath = public_path('/storage/' . $agreement->filepath);
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found. PATH: ' . asset('/storage/' . $agreement->filepath)
                ], 404);
            }
            return response()->json([
                'success' => true,
                'file_url' => asset('/storage/' . $agreement->filepath), // Generate full URL
                'file_name' => $agreement->original_name == NULL ? 'agreement_' . $agreement->adobe_agreement_id . '.pdf' : $agreement->original_name
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error downloading file. ' . $e->getMessage()
            ]);
        }
    }

    public function agreement_delete(Request $request)
    {
        try {
            $agreement = Agreement::findOrFail($request->agreement_id);
            $filePath = public_path($agreement->filepath);
            if ($agreement->filepath && file_exists($filePath)) {
                unlink($filePath);
            }
            $agreement->delete();
            toastr()->addSuccess('Customer agreement file deleted successfully.');
            return response()->json([
                'success' => true,
                'message' => 'Customer agreement deleted successfully.'
            ]);
        } catch (\Exception $e) {
            dd($e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting agreement. ' . $e->getMessage()
            ]);
        }
    }
    public function adobeHtmlSend()
    {
        try {
            // Validate required configuration
            $accessToken = config('adobe-sign.access_token');
            $appUrl = env('APP_URL');
            $fileName = 'IMS-LEASE-FINAL-RADI.html';

            if (empty($accessToken)) {
                \Log::error('Adobe Sign: Access token not configured');
                return response()->json([
                    'success' => false,
                    'error' => 'Adobe Sign access token not configured'
                ], 500);
            }

            if (empty($appUrl)) {
                \Log::error('Adobe Sign: APP_URL not configured');
                return response()->json([
                    'success' => false,
                    'error' => 'Application URL not configured'
                ], 500);
            }

            // Validate file exists
            $filePath = public_path($fileName);
            if (!file_exists($filePath)) {
                \Log::error('Adobe Sign: HTML file not found', ['path' => $filePath]);
                return response()->json([
                    'success' => false,
                    'error' => 'HTML file not found: ' . $fileName
                ], 404);
            }

            // Check file is readable
            if (!is_readable($filePath)) {
                \Log::error('Adobe Sign: HTML file not readable', ['path' => $filePath]);
                return response()->json([
                    'success' => false,
                    'error' => 'HTML file not readable: ' . $fileName
                ], 403);
            }

            // Initialize cURL with error handling
            $curl = curl_init();
            if ($curl === false) {
                \Log::error('Adobe Sign: Failed to initialize cURL');
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to initialize cURL connection'
                ], 500);
            }

            // Configure cURL options
            $curlOptions = [
                CURLOPT_URL => 'https://api.na1.adobesign.com/api/rest/v6/transientDocuments?File-Name=' . urlencode($fileName) . '&Mime-Type=text%2Fhtml',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30, // Set reasonable timeout
                CURLOPT_CONNECTTIMEOUT => 10, // Connection timeout
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => [
                    'File' => new \CURLFile($filePath),
                    'File-Name' => $fileName,
                    'Mime-Type' => 'text/html'
                ],
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $accessToken
                ],
                CURLOPT_SSL_VERIFYPEER => false, // Enable SSL verification
                CURLOPT_SSL_VERIFYHOST => 2,
                CURLOPT_USERAGENT => 'ERP-System/1.0'
            ];

            curl_setopt_array($curl, $curlOptions);

            // Execute cURL request
            $response = curl_exec($curl);

            // Check for cURL errors
            if ($response === false) {
                $curlError = curl_error($curl);
                $curlErrno = curl_errno($curl);
                curl_close($curl);

                \Log::error('Adobe Sign: cURL execution failed', [
                    'error' => $curlError,
                    'errno' => $curlErrno
                ]);

                return response()->json([
                    'success' => false,
                    'error' => 'Network request failed: ' . $curlError,
                    'error_code' => $curlErrno
                ], 500);
            }

            // Get HTTP status code
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $responseTime = curl_getinfo($curl, CURLINFO_TOTAL_TIME);
            curl_close($curl);

            // Log request details
            \Log::info('Adobe Sign: Transient document upload attempt', [
                'file' => $fileName,
                'http_code' => $httpCode,
                'response_time' => $responseTime . 's'
            ]);

            // Parse response
            $responseData = json_decode($response, true);
            $docID = '';

            // Handle different HTTP status codes
            switch ($httpCode) {
                case 200:
                case 201:
                    // Success
                    if (isset($responseData['transientDocumentId'])) {
                        \Log::info('Adobe Sign: Transient document uploaded successfully', [
                            'document_id' => $responseData['transientDocumentId']
                        ]);
                        $docID = $responseData['transientDocumentId'];

                        echo $docID . '<br>';
                        // return response()->json([
                        //     'success' => true,
                        //     'message' => 'Document uploaded successfully to Adobe Sign',
                        //     'data' => $responseData,
                        //     'document_id' => $responseData['transientDocumentId']
                        // ]);
                    } else {
                        \Log::warning('Adobe Sign: Unexpected success response format', [
                            'response' => $responseData
                        ]);

                        return response()->json([
                            'success' => true,
                            'message' => 'Document uploaded but response format unexpected',
                            'data' => $responseData
                        ]);
                    }
                    break;

                case 400:
                    \Log::error('Adobe Sign: Bad request', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Bad request to Adobe Sign API',
                        'details' => $responseData['message'] ?? 'Invalid request parameters'
                    ], 400);

                case 401:
                    \Log::error('Adobe Sign: Unauthorized access', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Unauthorized access to Adobe Sign API',
                        'details' => 'Please check your access token'
                    ], 401);

                case 403:
                    \Log::error('Adobe Sign: Forbidden access', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Forbidden access to Adobe Sign API',
                        'details' => 'Insufficient permissions'
                    ], 403);

                case 404:
                    \Log::error('Adobe Sign: API endpoint not found', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Adobe Sign API endpoint not found',
                        'details' => 'Please check the API URL'
                    ], 404);

                case 429:
                    \Log::error('Adobe Sign: Rate limit exceeded', ['response' => $responseData]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Rate limit exceeded',
                        'details' => 'Too many requests to Adobe Sign API'
                    ], 429);

                case 500:
                case 502:
                case 503:
                case 504:
                    \Log::error('Adobe Sign: Server error', [
                        'http_code' => $httpCode,
                        'response' => $responseData
                    ]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Adobe Sign server error',
                        'details' => 'Please try again later',
                        'http_code' => $httpCode
                    ], 500);

                default:
                    \Log::error('Adobe Sign: Unexpected HTTP status', [
                        'http_code' => $httpCode,
                        'response' => $responseData
                    ]);
                    return response()->json([
                        'success' => false,
                        'error' => 'Unexpected response from Adobe Sign API',
                        'details' => 'HTTP Status: ' . $httpCode,
                        'response' => $responseData
                    ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Adobe Sign: Unexpected error in adobeHtmlSend', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An unexpected error occurred',
                'details' => $e->getMessage()
            ], 500);
        }

        // set doc to adobe
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.na1.adobesign.com/api/rest/v6/agreements',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'{
                "fileInfos":[
                    { "transientDocumentId": "' . $docID . '" }
                ],
                "name":"Equipment Lease Agreement",
                "participantSetsInfo":[
                    {
                    "memberInfos":[ { "email":"<EMAIL>" } ],
                    "order":1,
                    "role":"SIGNER",
                    "securityOption": [ {
                        "authenticationMethod": "NONE" }
                        ]
                    },
                    {
                    "memberInfos":[ { "email":"<EMAIL>" } ],
                    "order":2,
                    "role":"APPROVER"
                    }
                ],
                "signatureType":"ESIGN",
                "state":"IN_PROCESS"
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer 3AAABLblqZhCof_uq_fAcLQM984oBc41loLOgEhfL1UgIQn9dyq4cF3pyZl-1lSEByGKJzk9YwDIaKszxL25PsQ9djLIeJInT '
            ),
            CURLOPT_SSL_VERIFYPEER => false, // Enable SSL verification
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'ERP-System/1.0'
        ));
        $response = curl_exec($curl);

        curl_close($curl);
        echo $response;
    }

    public function viewAgreementHtml()
    {
        $data = [
            'agreement_date' => '15th day of January 2025',
            'agreement_lagree_company' => 'Lagree Fitness Inc',
            'agreement_company' => 'TEST COMPANY',

            'agreement_address' => 'ADDRESS',
            'agreement_city' => 'CITY',
            'agreement_zip' => 'ZIP',
            'agreement_coutry_state' => 'COUNTRY/STATE',

            'agreement_annual_fee' => 'One Thousand Nine-Hundred Ninety Dollars ($1,990.00 U.S.)',
            'agreement_deposit' => 'Four Hundred Ninety Dollars ($490.00 U.S.)',
            'agreement_six_months' => 'One Thousand Five Hundred Dollars ($1,500.00 U.S.)',
            'agreement_annual' => 'One Thousand Nine-Hundred Ninety Dollars ($1,990.00 U.S.)',
            'agreement_company_rep' => 'XXXXXXXXX YYYYYYYYYYY',
            'lease_number_model' => 'XXXXXXXXXXXXXX',
            'monthly_per_machine' => 'XXXXXXXXXXXXXX',
            'months_count' => 'XXXXXXXXXXXXXX',
            'installment_cost' => 'XXXXXXXXXXXXXX',
            'deposit_total' => 'XXXXXXXXXXXXXX',
            'deposit_price' => 'XXXXXXXXXXXXXX',
            'buy_out' => 'XXXXXXXXXXXXXX',
            'purchase_number_model' => 'XXXXXXXXXXXXXX',
            'purchase_amount' => 'XXXXXXXXXXXXXX',
            'purchase_per_machine' => 'XXXXXXXXXXXXXX',
        ];    

        return view('adobe-files.license-mega', compact('data'));
    }
}
