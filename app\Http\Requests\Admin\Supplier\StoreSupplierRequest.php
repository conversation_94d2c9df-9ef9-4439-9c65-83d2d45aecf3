<?php

namespace App\Http\Requests\Admin\Supplier;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'is_active'          => 'required|boolean',
            'location'           => 'required|in:USA,International',
            'name'               => 'required|string|max:255',
            'email'              => 'required|string|email|unique:users,email|max:255',
            'email2'             => 'nullable|string|email|max:255',
            'phone'              => 'nullable|string|max:255',
            'address'            => 'nullable|string|max:255',
            'address2'           => 'nullable|string|max:255',
            'city'               => 'nullable|string|max:255',
            'zip'                => 'nullable|string|max:255',
            'state_id'           => 'nullable|exists:states,id',
            'country_id'         => 'nullable|exists:countries,id',
            'contact.first_name' => 'required|string|max:255',
            'contact.last_name'  => 'required|string|max:255',
            'contact.position'   => 'nullable|string|max:255',
            'contact.phone'      => 'nullable|string|max:255',
            'contact.email'      => 'nullable|string|max:255',
        ];
    }

    public function attributes()
    {
        return [
            'contact.first_name' => 'first name',
            'contact.last_name'  => 'last name',
            'contact.position'   => 'position',
            'contact.phone'      => 'phone',
            'contact.email'      => 'email',
        ];
    }
}
