:focus-visible {
	outline: none !important;
}
.items-error {
	transform: translateX(-80px);
}
/* FOR LOADER ON BUTTONS - ajax */
.bg--loading-small,
.bg--loading-black,
.bg--loading {
	position: relative;
}
/* .bg--loading::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.922);
	z-index: 11;
	display: flex;
	align-items: flex-start;
    justify-content: center;
	text-indent: 20px;
} */
.bg--loading::after {
    content: '';
    display: block;
    width: 44px;
    height: 44px;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 12;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    border: 5px solid #fff;
    border-top-color: transparent;
    animation: spin 1s infinite linear;
    margin-left: -12px;
}
.bg--loading-black::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.922);
	z-index: 11;
	display: flex;
	align-items: flex-start;
    justify-content: center;
	text-indent: 20px;
}
.bg--loading-black::after {
	content: '';
	display: block;
	width: 44px;
	height: 44px;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 12;
	transform: translate(-50%, -50%);
	border-radius: 50%;
	border: 2px solid #000;
	border-top-color: transparent;
	animation: spin 1s infinite linear;
	margin-top: -15px;
}
/* -------------- BTN LOADER ------------------*/
body .btn--loading-small-black,
body .btn--loading-small,
body .btn--loading {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
    pointer-events: none;
}
body .btn--loading-small::after,
body .btn--loading::after {
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
    border-radius: 50%;
    border: 3px solid #fff;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}
body .btn--loading-small::after {
	width: 18px;
	height: 18px;
	margin-top: -9px;
	margin-left: -9px;
	border-width: 2px;
    transform-origin: center center;
}
body .btn--loading-small-black {
    color: rgba(0,0,0,0) !important;
    transition: none !important;
    position: relative;
}
body .btn--loading-small-black::after {
    content: '';
    display: block;
    width: 18px;
    height: 18px;
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    border: 2px solid #000;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
    margin-top: -9px;
    margin-left: -9px;
    transform-origin: center center;
}
body .btn--loading-small-black-right {
    transition: none !important;
    position: relative;
    width: 100%;
}
body .btn--loading-small-black-right::after {
    content: '';
    display: block;
    width: 12px;
    height: 12px;
    position: absolute;
    right: 20px;
    top: 17px;
    margin-left: -5px;
    margin-top: -6px;
    border-radius: 50%;
    border: 2px solid #000;
    border-top-color: transparent;
    -webkit-animation: spin 1s infinite linear;
    -moz-animation: spin 1s infinite linear;
    -o-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
}

.entity-table.bg--loading-black{
    min-height: 300px;
}
.fw-medium {
    font-weight: 500 !important;
}
.mr-5 {margin-right:50px;}
.mt-05 {margin-top:5px;}
.mb-05 {margin-bottom: 5px;}
.pt-05 {padding-top:5px;}
.pl-0 {padding-left:0 !important;}
.pl-10 {padding-left:10px !important;}
.pl-2 {padding-left:20px !important;}
.pb-05 {padding-bottom: 5px;}
.mt-20 {margin-top: 20px !important;}
.mb-20 {margin-bottom: 20px !important;}
.mt-50 {margin-top:50px;}
.mt-45 {margin-top:45px;}
.mb-45 {margin-bottom:45px;}
.mb-50 {margin-bottom:50px;}
.pt-45 {padding-top:45px;}
.pb-45 {padding-bottom:45px;}
.pb-50 {padding-bottom:50px;}
.pt-50 {padding-top:50px;}
.max400 {max-width: 400px;}
.pl-05 {padding-left:5px !important;}

.no-border-top {border-top: none !important;}
.no-border-btm {border-bottom: none !important;}
.no-mar-top {margin-top: 0 !important;}


@-webkit-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-moz-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-ms-keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@keyframes spin{
    0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
    100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
.form-group.studio_id {
	display: none;
}
.form-label {font-size: 11px;}
:root {
    --bs-progress-bg: #f0f0f0 !important;
}
*, body {
    letter-spacing: 0.05em !important;
  }
  /* scrollbar */
::-webkit-scrollbar-track {
    background-color: #F0F0F0;
  }
  
  ::-webkit-scrollbar {
    width: 2px;
    background-color: #000;
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: #000;
  }
  
  * {
    scrollbar-color: #000 #F0F0F0;
    scrollbar-width: thin;
  }
.colorGrey {color:#969696 !important;}
body .colorYellow {color:#E8AF44 !important;}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
	background:#fff;
    border-color:#000;
    color:#000;
}
.progress {
    background-color: #f0f0f0 !important;
}
.bg-fcfcfc {
    background-color: #FCFCFC !important;
}
.bg-1866 {
	background: #4791D1;
}
.bg-blue {
    background: #4791D1;
}
input[type=number],input[type=number],input[type=number],input[type=day],input[type=month],input[type=year],input[type=time],input[type=date],input[type=number] {
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}
.full-schedule-link {
    color: #969696;
    text-decoration: underline;
}
.full-schedule-link:hover {
    color: #000;
    text-decoration: underline;
}
a,
.btn.btn-link {
	text-decoration-color: #969696;
}
.btn.btn-small {
	padding: 10px 15px;
	font-size: 10px !important;
}
input[type="text"]::placeholder, input[type="email"]::placeholder, input[type="url"]::placeholder, input[type="password"]::placeholder, input[type="search"]::placeholder, input[type="number"]::placeholder, input[type="tel"]::placeholder, input[type="range"]::placeholder, input[type="date"]::placeholder, input[type="month"]::placeholder, input[type="week"]::placeholder, input[type="time"]::placeholder, input[type="datetime"]::placeholder, input[type="datetime-local"]::placeholder, input[type="color"]::placeholder, textarea::placeholder {
	font-weight: 400;
    text-transform: none;
}
input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea {
    border-color: #DDD !important;
    border-radius: 8px !important;
    background: #FDFDFD !important;
}
[aria-labelledby="dropdownMenuAccount"] {
    padding: 20px;
}
[aria-labelledby="dropdownMenuAccount"] h6 {
    line-height: 1;
    padding: 0 0 18px 0 !important;
    margin: 0 0 20px 0;
}
[aria-labelledby="dropdownMenuAccount"] a:hover {
    color: #333;
}
[aria-labelledby="dropdownMenuAccount"] a {
    padding: 0 !important;
}
.client-main [aria-labelledby="dropdownMenuAccount"] a {
	padding: 7px 10px !important;
}
.client-main [aria-labelledby="dropdownMenuAccount"] {
	padding: 15px 20px;
}
[aria-labelledby="dropdownMenuAccount"] .dropdown-item:hover, [aria-labelledby="dropdownMenuAccount"] .dropdown-item:focus {
    background: none !important;
}
body #toast-container, body .fl-main-container {
	background-image: none !important;
	padding: 25px;
	width: 100%;
	max-width: 500px;
	bottom: 0vh !important;
	right: 0 !important;
	transform: none;
	z-index: ************;
	top: auto !important;
}
.fl-main-container .fl-container.fl-flasher .fl-message {
	font-size: .875em;
	margin-top: 0;
}
#toast-container .toast + .toast {
	margin-top: 10px !important;
}
body #toast-container > .toast, body .fl-main-container > .fl-container {
	opacity: 1 !important;
	padding: 15px 35px 15px 15px !important;
	background: #000 !important;
	border: none !important;
	border-radius: 10px !important;
	width: auto;
	margin: 0 !important;
	transform: translateY(-25px) !important;
	box-shadow: 0px 0px 50px 0 rgba(51, 51, 51, 0.15) !important;
	/* animation: notification 0.25s forwards !important; */
}
body .fl-main-container > .fl-flasher.fl-error .fl-title, 
body .fl-main-container > .fl-flasher.fl-error .fl-message {
    color: #fff !important;
}
body #toast-container > .toast::before, .fl-main-container > .fl-container::before {
	content: "×";
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 15px;
	color: #F0F0F0;
	font-size: 28px;
	font-weight: 100;
}
.form-group.lh-1.discount_type {
	max-width: 80px;
	margin-left: 20px;
}
.spots .spot:hover {
	border-color: #777 !important;
}
.go-to-date {
    transition: all 0.25s ease-in-out 0s;
    font-weight: 500;
}
.go-to-date:hover {
	color: #000 !important;
}
.lg-search {padding-left:20px;}
.lg-search .form-control {
	border-radius:50px !important;
    min-height:40px !important;
    border-color: #ddd !important;
}
.lg-search:hover .form-control:not(.lg-search-expanded) {
    border-color:#000 !important;
}
.lg-search .form-control:hover {
	border-color: #000;
}
.was-validated .form-control:invalid,
.form-control.is-invalid {
	background-image: none !important;
}
@keyframes notification {
    0% {transform: translateY(-25px) !important;opacity: 0 !important;}
    100% {transform: translateY(0px) !important;opacity: 1 !important;}
}
body .fl-main-container > .fl-show {
    transform: translate(0) !important;
}
body #toast-container .toast-title, body .fl-main-container .fl-title {
	color: #000 !important;
}
body #toast-container > .toast-success .toast-title, body #toast-container > .toast-success .toast-message, body .fl-main-container > .fl-success .fl-title, body .fl-main-container > .fl-success .fl-message {
	color: #fff !important;
	padding: 0px;
}
.bulk_qty_label {
	position: absolute;
	top: 50%;
	left: 100%;
	margin-left: 10px;
	white-space: nowrap;
	transform: translateY(-50%);
}
body #toast-container > .toast-warning .toast-title, body .fl-main-container > .fl-warning .fl-title {
	color: #E8AF44 !important;
}
body #toast-container > .toast-error .toast-title, body #toast-container > .toast-error .toast-message, body .fl-main-container > .fl-error .fl-title, body .fl-main-container > .fl-error .fl-message {
	color: #fff !important;
}
body #toast-container > .toast-info .toast-title, body #toast-container > .toast-info .toast-message, body .fl-main-container > .fl-info .fl-title, body .fl-main-container > .fl-info .fl-message {
	color: #969696 !important;
}
body #toast-container .toast-progress, body .fl-main-container .fl-progress-bar, body .fl-main-container .fl-icon {
	display: none !important;
}
body #toast-container .toast-title, body .fl-main-container .fl-title {
	font-weight: 400 !important;
	font-size: 14px !important;
    margin: 0 !important;
    /* display: none; */
}
.toast-error .toast-title{
    display: none;
}
body.side-menu #toast-container, body.side-menu .fl-main-container {
	left: calc(50vw + 150px);
}
body.mini-menu #toast-container, body.mini-menu .fl-main-container {
	left: calc(50vw + 30px);
}
body.only-modal #toast-container, body.only-modal .fl-main-container {
	left: 50vw;
}
.toastr-popup {
	position: fixed;
    z-index: ************;
	width: 100%;
	max-width: 600px;
	top: 13vh !important;
	left: 50vw;
	transform: translateX(-50%);
	box-shadow: 0px 0px 50px 0 rgba(51, 51, 51, 0.15) !important;
    min-height: 80px;
    height: auto;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-weight: 400 !important;
	font-size: 14px !important;
	margin: 0 !important;
	padding: 30px !important;
    cursor: pointer;
    color: #000;
}
body .toastr-popup::before {
	content: "×";
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	right: 30px;
	color: #F0F0F0;
	font-size: 40px;
	font-weight: 100;
}
.main-content {
	padding: 0 60px;
}
/* scrollbar */
::-webkit-scrollbar-track {
    background-color: #fff;
}
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
/*    !*background-color: #000;*!*/
}
::-webkit-scrollbar-thumb {
    background-color: #EAEAEA;
    border-radius: 3px;
/*    !*background-color: #000;*!*/
}
/** {*/
/*    scrollbar-color: #EAEAEA !*#000*! #fff;*/
/*    scrollbar-width: thin;*/
/*}*/
#dashboard-side::-webkit-scrollbar-track {
    background-color: #000;
}
#dashboard-side::-webkit-scrollbar {
    width: 6px;
    background-color: #222;
}
#dashboard-side::-webkit-scrollbar-thumb {
    background-color: #222;
}
#dashboard-side {
    scrollbar-color: #222 #000;
    scrollbar-width: thin;
}
/* NOVO */
.main-content > .navbar {
    margin-right: -60px;
    margin-left: -60px;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 999;
    padding-right: 30px !important;
}
.main-content .logo {
	display: none;
}
.table-filter-select {
    padding-right: 40px;
    min-width: auto;
    border: none;
    right: 0px;
    position: relative;
    padding-left: 0;
    font-size: 12px;
}
/*.select2-hidden-accessible { position: fixed !important; }*/
.select2-results__option--highlighted:hover div span:first-child{
    cursor: pointer;
    text-decoration: underline;
}
.py-50 {
    padding-bottom: 50px !important;
    padding-top: 50px !important;
}
.my-50 {
    margin-bottom: 50px !important;
    margin-top: 50px !important;
}
.gap-25 {
    gap: 25px !important;
}
.lh-small {
    line-height: 1 !important;
}
.lh-25 {
    line-height: 25px !important;
}
.lh-33 {
    line-height: 33px !important;
}
.min-h-0 {min-height: 0 !important;}
.modal-footer.justify-content-between {
    flex-wrap: nowrap;
}
.dashboard-side .logo {
    width: 240px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.modal-body .form-control {
    max-width: none;
}
input.form-control {
    min-height:40px !important;
    max-height: 40px;
    height: 40px;
    padding: 5px 15px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: 14px !important;
	padding-top: 0 !important;
	padding-bottom: 0 !important;
    color:#969696;
}
.form-group .select2-container--default .select2-selection--single .select2-selection__rendered {
    min-height:38px !important;
    max-height: 38px;
    height: 38px;
    padding-left:15px !important;
}
.table-filter-select .select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: 14px !important;
	padding-top: 0 !important;
	padding-bottom: 0 !important;
}
.account-ctrl span {
	width: 100%;
	height: 100%;
	display: flex;
	background: #000;
	align-items: center;
	justify-content: center;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 50%;
}
.account-ctrl {
    font-weight: 600;
    text-transform: uppercase;
    overflow: hidden;
    background-color: rgba(0,0,0,0);
}

.w-100px {
    width: 100px;
}
.w-100-full {
    width: 100%;
}
.jcsb {justify-content: space-between;}
.fdc {flex-direction: column;}

.custom-modal-size {
    /*max-width: 400px; !* Adjust width as needed *!*/
    height: 300px; /* Adjust height as needed */
}
.form-check-input,
.form-check {
    position: relative;
    z-index: 11;
}
.no-uppercase-no-bold {
    text-transform: none;
    font-weight: normal;
}
.list-group-item .remove-item {
    text-decoration: none;
}
.buttons-wrapper {
    display:flex;
    border-top:1px solid #f0f0f0; 
    padding-top: 50px;
    margin-top: 50px;
    width: 100% !important;
}
.delete-btn {
    border: 1px solid #DB1818;
    padding: 10px 20px;
    margin-left: auto !important;
    color: #DB1818;
}
.delete-btn:hover {
    background: #DB1818;
    color: white !important;
}
/*Radio Checkbox*/
input[type="radio"] {
    display: none;
}
.radio-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 10px 10px 10px 0;
    font-size: 14px;
    color: #888888;
}
.radio-container .checkmark {
    height: 20px;
    width: 20px;
    background: #ffffff;
    border-color: #ddd;
    border-width: 1px;
    border-style: solid;
    display: inline-block;
    position: relative;
    margin-right: 10px;
    border-radius: 4px;
}
.radio-container .checkmark::after {
    content: "";
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: #000;
}
.radio-container input:checked ~ .radio-text {
    color: #000; /* Lime green color for active text */
}
.radio-container input:checked ~ .checkmark::after {
    display: block;
}
/*Cancel button*/
#cancel-button {
    background-color: #fff;
    border: 1px solid #ccc;
    padding: 0.5rem 1rem;
}
.cancel-btn, .saveastpl-btn {
    background: #fff !important;
    border: 1px solid #ddd !important;
    color: #969696 !important;
}
.cancel-btn:hover, .saveastpl-btn:hover {
    background: #000 !important;
  border: 1px solid #000 !important;
  color: #fff !important;
}
.cancel-btn {margin-left: 20px;}
.saveastpl-btn {margin-left: auto;}

/*Round Button dropdown*/
.round-button-dropdown {
    position: relative;
    display: inline-block;
}
.dropbtn, .dropbtn-mobile {
    background-color: #fff;
    border: 1px solid #ddd;
    cursor: pointer;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    /*box-shadow: 0 2px 4px rgba(0,0,0,0.1);*/
}
.dropbtn:hover,
.dropbtn:active {
    border: 1px solid black;
}
.dropbtn i {
    font-size: 16px;
}
.dropdown-content {
    display: none;
    position: absolute;
    top: 130%;
    right: 20%;
    background-color: #fff;
    min-width: 80px;
    z-index: 999;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
    padding: 20px 20px;
}
.dropdown-content a {
    color: black;
    padding: 0;
    margin-bottom: 20px;
    line-height: 1;
    text-decoration: none;
    display: block;   
    white-space: nowrap;
}
.dropdown-content a:last-child {
    margin-bottom: 0;
}
.dropdown-content .terminate {
    color: red;
}
.dropdown:hover .dropdown-content {
    display: block;
}
.custom-round-button-dropdown {
    position: relative;
    display: inline-block;
}
.custom-dropbtn {
    background-color: #000; /* Black button background */
    color: white; /* White text */
    font-size: 30px; /* Larger font size */
    border: none;
    cursor: pointer;
    padding: 10px; /* Padding around the button */
    border-radius: 50%; /* Make the button round */
    width: 45px; /* Adjusted button width */
    height: 45px; /* Adjusted button height */
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1; /* Line height */
    text-align: center; /* Center text */
    font-weight: 400; /* Normal font weight */
}
.custom-dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
    z-index: 1;
    right: 0; /* Align dropdown to the right */
    border-radius: 5px;
}
.custom-dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}
.custom-dropdown-content a:hover {
    background-color: #f1f1f1;
}
/* Show dropdown content on hover */
.custom-round-button-dropdown:hover .custom-dropdown-content {
    display: block;
}
/* Alternatively, show dropdown content on button click */
.custom-round-button-dropdown:active .custom-dropdown-content {
    display: block;
}

.currency-symbol {
    position: absolute !important;
    left: 10px;
    top:0 !important;
    margin-top: 20px;
    transform: translateY(-50%);
    font-size: 14px;
    pointer-events: none; /* Prevent clicking the symbol */
    margin-left:15px;
}
.show {
    display: block;
}
.customer-dashboard-card {
    width: 285px;
    height: 130px;
    border: 1px solid #F0F0F0;
    border-left: 5px solid #F0F0F0;
    align-content: center;
    border-radius: 8px;
}
.customer-dashboard-card-title {
    font-size: 16px;
    margin-bottom: 0;
    font-weight: 500 !important;
}
.customer-dashboard-card-text {
    font-size: 12px;
    margin-bottom: 0;
}
/*Add License Button Dropdown*/
#add-license-button {
    position: relative;
    display: inline-block;
}
/*#add-license-button button {margin-bottom:10px; margin-top: 10px;}*/
.add-license-button-dropdown {
    display: none;
}
.add-license-button-dropdown.show {
    display: block;
}
#add-license-button-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
    z-index: 1;
    width: 170px;
    padding: 5px 0;
    border-radius: 6px;
    display: none;
    border: 1px solid #ddd;
    max-width: 135px;
}
#add-license-button-dropdown a {
    color: black;
    padding: 10px 20px;
    text-decoration: none;
    display: block;
}
#add-license-button-dropdown a:hover {
    color: #969696;
    text-decoration: none;
}
#add-license-button:hover #add-license-button-dropdown {
    display: block;
}
/* #add-license-button::after {
    content: ' \25BC';
    margin-left: 10px;
    opacity: 0;
    transition: opacity 0.3s;
} */
/* #add-license-button:hover #add-license-button::after {
    opacity: 1;
} */
/* Modal body */
.modal-body.empty-licenses {
    height: 300px; /* Set the fixed height */
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-dialog-scrollable {
    max-height: 60vh; /* You can adjust this value as needed */
}
.customer-modal-body {
    max-height: calc(60vh - 150px);
    overflow-y: auto;
}
.box60 {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-radius: 50%;
    border: 1px solid #F0F0F0;
    font-size: 14px;
    font-weight: 500;
}
.profile-thumbnail-60 img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.profile-thumbnail-60 {
	object-fit: cover;
    width: 60px;
    height: 60px;
    min-width: 60px;
    min-height: 60px;
    overflow: hidden;
    border-radius: 50px;
}
[data-tooltip] {
	position: relative;
}
[data-tooltip]:hover::before {
	left: calc(100% + 22px);
    opacity: 1;
    visibility: visible;
}
[data-tooltip]:hover::after {
	left: calc(100% + 2px);
    opacity: 1;
    visibility: visible;
}
[data-tooltip]::before {
	content: attr(data-tooltip);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s ease-in-out 0s;
	background: #000;
	position: absolute;
	top: 50%;
	left: calc(100% + 12px);
	z-index: 1111;
	font-size: 10px;
	padding: 12px 15px;
	transform: translate(0, -50%);
	border-radius: 3px;
}
[data-tooltip]::after {
	content: "";
	position: absolute;
	left: calc(100% - 8px);
	top: 50%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s ease-in-out 0s;
	border: 8px solid rgba(0,0,0,0);
	border-right: 12px solid #000;
	transform: translateY(-50%);
}
#dashboard-icons svg {
    display: block;
    margin: auto;
}
#dashboard-icons .menu-item {
	overflow: visible;
}
.menu-item.mini-logo {
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
}
body.side-menu #dashboard-icons {
    width: 0;
}
body.mini-menu #dashboard-icons {
	width: 60px;
}
#dashboard-side, #dashboard-icons {
    transition: all 0.25s cubic-bezier(.13,.56,.38,.89) 0s;
}
body.side-menu #dashboard-side {
    width: 300px;
}
body.mini-menu #dashboard-side {
	width: 0;
}
.input-placeholder {
	position: relative;
}
.h-40px {
    height: 40px;
}
.lg-search .lg-search-ico {
	position: absolute;
	top: 0;
	right: 0;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 11112;
}
.lg-search .lg-search-ico img {height: 15px;}

.datepicker.datepicker-dropdown {z-index: 1000 !important;}
.date-selector-wrap .datepicker .datepicker-days table tr td,
.date-selector-wrap .datepicker .datepicker-days table tr td,
.date-selector-wrap .datepicker .datepicker-days table tr td,
.date-selector-wrap .datepicker .datepicker-days table tr td {
	width: 38px;
	height: 38px;
}
.datepicker .datepicker-years table tr td span,
.datepicker .datepicker-months table tr td span {
    height: 56px;
    line-height: 56px;
}
#locationModalToggle .modal-body {
	max-height: 70vh;
	overflow: auto;
}
.right-recycle-position {
	right: 4vw;
}
.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within) {
	position: absolute !important;
	width: 1px !important;
	height: 1px !important;
	padding: 0 !important;
	margin: -1px !important;
	overflow: hidden !important;
	clip: rect(0,0,0,0) !important;
	white-space: nowrap !important;
	border: 0 !important;
}
textarea.form-control {
	max-height: 130px;
	background: #FDFDFD !important;
	border-color: #DDD !important;
	border-radius: 8px !important;
    padding: 13px 20px !important;
}
.list-group-item .list-thumbnail {
	width: 40px;
	height: 40px;
    min-width: 40px;
    min-height: 40px;
    max-width: 40px;
    max-height: 40px;
	object-fit: cover;
}
.welcome-badge {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 11;
}
.trial-upgrade {
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 10px;
	gap: 3px;
	white-space: nowrap;
	min-width: 240px;
}
@media(min-width: 576px){
    .fw-sm-normal {
        font-weight: 400 !important;
    }
    .fw-sm-medium {
        font-weight: 500 !important;
    }
    .fw-sm-semibold {
        font-weight: 600 !important;
    }
    .modal-dialog.w-500 {
        max-width: 500px;
    }
    .modal-dialog.w-40 {
        max-width: 40%;
    }
    .modal {
        z-index: ************111;
    }
    /*.modal-backdrop {*/
    /*    z-index: 1111111;*/
    /*}*/

}
@media (max-width: 576px) {
    .modal-footer.justify-content-between {
        flex-wrap: wrap;
    }
}
/* #dashboard-dropdown-content {
    position: fixed;
    top: auto;
    right: auto;
    margin-top: 10px;
} */
.menu-item:not(:first-child) svg {
    width: 15px;
}
.alert p {
	margin: 0;
}
#dashboard-icons .menu-item {
	padding: 20px 0;
}
#dashboard-icons .menu-item:last-of-type {
  border-bottom: none;
}
#dashboard-icons .menu-item {
  border-top: 1px solid rgba(255,255,255,0.1);
}
.alert {
	position: fixed;
	bottom: 0;
	left: 0;
	z-index: 111;
	width: 100%;
	text-align: center;
	border: none;
    margin: 0;
}
.btn-ghost-light {
	background: #fff;
}
.btn-ghost-light:hover, .btn-ghost-light:focus{
	background: #F0F0F0;
}
.btn-ghost-light-bigger {
    background: #fff !important;
    border: 1px solid #ddd !important;
    color: #969696 !important;
}
.btn-ghost-light-bigger:hover, .btn-ghost-light:focus{
    background: #000 !important;
    color: #fff !important;
    border-color:#000 !important;
}
#customers-table .list-group-item a {text-decoration: none;}
#customers-table .entity-table #searchForm .list-group .list-group-item {
    padding-top: 40px;
    padding-bottom: 30px;
}
#machines-table .entity-table #searchForm .list-group .list-group-item {
    padding-top: 40px;
    padding-bottom: 30px;
}
#products-table .entity-table #searchForm .list-group .list-group-item {
    padding-top: 40px;
    padding-bottom: 30px;
}
#companies-table .entity-table #searchForm .list-group .list-group-item {
    padding-top: 40px;
    padding-bottom: 30px;
}
#upcoming-payments-table .entity-table .list-group .list-group-item {
    padding-top: 40px;
    padding-bottom: 30px;
}
.entity-table .list-group-item a:hover {color:#969696;}
input[type="text"].line-style, input[type="email"].line-style, input[type="url"].line-style, input[type="password"].line-style, input[type="search"].line-style, input[type="number"].line-style, input[type="tel"].line-style, input[type="range"].line-style, input[type="date"].line-style, input[type="month"].line-style, input[type="week"].line-style, input[type="time"].line-style, input[type="datetime"].line-style, input[type="datetime-local"].line-style, input[type="color"].line-style, textarea.line-style {
  padding: 1rem 0;
}
#clientInstructorModal.modal.fade .modal-dialog,
#locationClientModalToggle.modal.fade .modal-dialog,
#locationModalToggle.modal.fade .modal-dialog,
#clientCourseModal.modal.fade .modal-dialog {
	max-width: 500px;
}
.py-25 {
	padding-top: 25px;
	padding-bottom: 25px;
}
.minH-42px {
    min-height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.minH-42px-normal {
    min-height: 42px;
    /*display: flex;*/
    align-items: center;
    justify-content: center;
}
/* main.client-main .main-content {
	height: calc(100vh - 99px);
} */
.admin-avatar {
	width: 70px;
	height: 70px;
}
.profile-info-div {
    border: 1px solid #f0f0f0; 
    border-radius: 10px;
}
.profile-info-div .instructor-basic-info:last-of-type {
    border-bottom: none;
}
.instructor-basic-info {
	display: grid;
	border-bottom: 1px solid #f0f0f0;
	grid-template-columns: 150px 1fr;
    font-size: 14px;
    height: 54px;
    padding-left: 20px;
}
.instructor-basic-info > div {
	display: flex;
	align-items: center;
	justify-content: flex-start;
}
.rate-grade {
	display: flex;
    border: 1px solid #F0F0F0;
}
.rate-grade > label:hover {
	background: #F8F8F8;
	cursor: pointer;
}
.rate-grade > label:last-of-type {
    border-right: none !important;
}
.client-classes-table .class-history.list-group-item {
	grid-template-columns: 60px 2fr 1fr;
}
#clientPolicyModal.modal .modal-dialog {
	max-width: 500px;
}
.rate-grade > label {
	width: 20%;
	height: 55px;
	background: #FCFCFC;
	border-right: 1px solid #F0F0F0;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
}
.rate-grade > label > input {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	visibility: hidden;
	width: 1px;
	height: 1px;
}
.rate-grade > label > input:checked + .rate-value {
	width: 35px;
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #52C15A;
	border-radius: 50%;
	color: #fff;
}
.form-group.w-100 textarea.form-control {
	max-width: 100% !important;
}
.dashboard-side .menu, .dashboard-side .location-wrap {
	border-color: rgba(255,255,255, 0.1) !important;
}
.dashboard-side .menu-item.text-opacity-50 {
	color: #fff, var(--bs-text-opacity)\) !important;
}
.dashboard-side .menu {padding-bottom: 30px;}
.rating-stars {
	position: relative;
	top: -1px;
}
.rating-stars.full svg path {
	fill: #e8af44;
}
#invitation_code.is-invalid {
	border-color: red;
}
.invalid-feedback.invitation_code_error {
	position: absolute;
	bottom: -20px;
	left: 0;
	width: 100%;
}
.invalid-feedback, .error-msg.text-danger {color:#DB1818 !important; margin-top: 10px; font-size: 10px !important;}

.dashboard-side .menu-item {
	line-height: 1;
    font-size: 11px;
    border-top: 1px solid rgba(255,255,255,0.15);
    padding-top: 30px;
}
.dashboard-side #sidebar-submenu .menu-item,
.dashboard-side #sidebar-inventory .menu-item,
.dashboard-side #sidebar-invoices .menu-item,
.dashboard-side #sidebar-customers .menu-item {
    border:none; padding:0;text-transform: none;font-weight: 400; overflow:visible;
}
.select2-container--default .select2-results__group {
	padding: 6px 6px 22px 6px;
}
.notes_badge {
	position: absolute;
	top: 0;
	left: calc(100% - 20px);
	width: auto;
	height: 20px;
	min-width: 18px;
	background: #fff;
	border-radius: 30px;
	border: 1px solid #F0F0F0;
	display: flex;
	white-space: nowrap;
	font-size: 10px;
	color: #969696;
}
.number-of-notes {
	width: 18px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.notes_badge:hover .number-of-notes-text {
    width: 78px;
}
body .select2-container {
    width: auto !important;
    max-width: 500px !important;
}
.number-of-notes-text {
    padding: 0;
    display: block;
    line-height: 20px;
    width: 0;
    overflow: hidden;
    transition: all 0.25s ease;
}
.password-eye {
    top: 15px;
}
.mini-dollar {
    font-size: 20px;
}
svg {
	display: block;
}
.dashboard-side .menu-item-has-children[data-tooltip="Management"] svg{
    transform: rotate(0deg);
}
#dashboard-icons #sidebar-submenu-mini {
	z-index: 11122;
}
.zIndex-10000 .select2-container.select2-container--default.select2-container--open:not(.select2-container--below) {
	right: 0 !important;
	top: 100% !important;
}
.zIndex-10000 {
    z-index: 1113;
}
.select2-dropdown {
    min-width: 140px !important;
    width: auto;
	padding: 20px 15px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.zIndex-10000 .select2-container--open .select2-dropdown--below {
	transform: translateX(-17px) !important;
	border-radius: 6px;
	border: 1px solid #ddd !important;
    margin-top: 8px;
    z-index: ************11111111;
}
.select2-results__options {
	white-space: normal;
}
.max-w-600 {
    max-width: 600px;
}
.max500 {
    max-width: 500px;
}
.max330 {
    width: 100%;
    max-width: 330px;
}
div#extraInfo {
    margin-top: -25px;
    color: #969696;
    margin-bottom: 40px;
}
[for="repeat_every_period"] {
    height: 10px;
}
#repeat-every-weekday {
    margin-bottom: 40px;
}
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Semi-transparent black background */
    z-index: *********00; /* Ensure the overlay appears above other elements */
}
#addCard .modal-dialog {
	max-width: 500px;
}
.break-text {
    white-space: pre-line;
}
.modal-header .modal-title {
    margin-bottom: 0;
    padding-right: 35px;
    line-height: 1.6 !important;
    font-weight: 600
}
.datepicker-days table thead tr:nth-child(2) {
    position: relative;
}
.datepicker-days table thead tr:nth-child(2) th.prev {
    position: absolute;
    right: 25px;
    top: 0;
}
.datepicker-days table thead tr:nth-child(2) th.next {
    position: absolute;
    right: 0;
    top: 0;
}
.datepicker-days table thead tr:nth-child(2) .datepicker-switch {
	order: -1;
	text-align: left;
	font-size: 14px;
	height: 35px;
    margin-left: 0;
  display: block;
}
.datepicker-years table thead tr:nth-child(2) .prev,
.datepicker-months table thead tr:nth-child(2) .prev,
.datepicker-days table thead tr:nth-child(2) .prev {
	height: 35px;
	width: 30px;
	margin-left: auto;
	font-size: 0;
	background: url(../img/arrow-left.svg) no-repeat center center;
	opacity: 0.5;
    transition: all 0.25s ease-in-out 0s;
}
.datepicker-years table thead tr:nth-child(2) .next,
.datepicker-months table thead tr:nth-child(2) .next,
.datepicker-days table thead tr:nth-child(2) .next {
	height: 35px;
	width: 30px;
	margin-left: auto;
	font-size: 0;
	background: url(../img/arrow-right.svg) no-repeat center center;
	opacity: 0.5;
    margin-left: 0px;
    transition: all 0.25s ease-in-out 0s;
}
.datepicker-years table thead tr:nth-child(2) .next:hover,
.datepicker-years table thead tr:nth-child(2) .prev:hover,
.datepicker-months table thead tr:nth-child(2) .next:hover,
.datepicker-months table thead tr:nth-child(2) .prev:hover,
.datepicker-days table thead tr:nth-child(2) .next:hover,
.datepicker-days table thead tr:nth-child(2) .prev:hover {
    opacity: 1;
}
.mx-2.date-selector.text-center.fs-14px.ls-0\.7px {
	position: relative;
	top: 3px;
}
.list-group-item-action:hover, .list-group-item-action:focus {
	background-color: #FCFCFC !important;
}
.nav.nav-tabs {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    width: 100%;
    display: block;
    padding: 0 30px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
} 
.nav.nav-tabs .nav-item {
    display: inline-block !important; 
    position: relative;
    margin-right: 20px !important;
}
.entity-table .list-group-item > p, .entity-table .list-group-item > a, .entity-table .table-div {
    word-break: break-word;
    padding-right:20px;
}
.entity-table .list-group-item {
	padding: 18px 20px !important;
}
.right_mark {
	position: absolute;
	top: 12px;
	right: 20px;
	font-size: 14px;
	color: #969696;
	display: flex;
	justify-content: flex-end;
    pointer-events: none;
	width: 60px;
}
.left_mark {
	position: absolute;
	top: 20px;
	left: 26px;
	font-size: 14px;
    pointer-events: none;
	font-weight: 400;
	color: #969696;
	line-height: 1;
}
.datepickerr.date-selector-wrap .datepicker {
	top: calc(100% - 1px) !important;
    border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  width: 100%;
}
body .flatpickr-calendar {
	border-radius: 0;
	box-shadow: none;
	border: 1px solid #f0f0f0 !important;
    transform: translateY(-3px);
}
body .flatpickr-calendar.hasTime .flatpickr-time {
	border-top: none !important;
}
.flatpickr-time::after,
.flatpickr-calendar.arrowTop::after,
.flatpickr-calendar.arrowTop::before {
	display: none !important;
}
.dash-stats-container .dash-stats-header {
    background-color: #fcfcfc !important;
}
.dashboard-side .menu-item.active {
	opacity: 1 !important;
    color:#969696;
}
#internal-note {
	height: 180px;
}
.schedule-wrap .list-group-item.list-group-item-action + .list-group-item.list-group-item-action {
	border-top: none !important;
}
.error-msg.text-danger {
	position: absolute;
	top: 120%;
	width: 50vw;
	left: 0;
	z-index: 11;
}
.multi-select-actions > div {
	align-items: center;
	background: #fcfcfc;
	z-index: 11;
	position: relative;
	margin-top: 0px;
	padding-top: 15px !important;
	padding-bottom: 15px !important;
}
.multi-select-actions {
    position: absolute;
    top: 0;
    background: #fff;
    width: 100%;
    height: 50px;
}
.entity-table {
    position: relative;
}
.schedule-wrap.entity-table:last-of-type .list-group-item.list-group-item-action {
	border-bottom: 1px solid #f0f0f0 !important;
}
.schedule-wrap.entity-table + .schedule-wrap.entity-table .border-top {
	border-top: none !important;
}
#clientCourseModal .list-thumbnail, #clientInstructorModal .list-thumbnail {
    width: 70px;
    height: 70px;
    min-width: 70px;
    max-width: 70px;
    min-height: 70px;
    max-height: 70px;
    object-fit: cover;
}
button, .btn {
    white-space: nowrap;
    letter-spacing: 0.1em !important;
}
.modal-content * {
    word-break: break-word;
}
.modal-content {
    border-radius: 10px;
}
.modal-content .modal-header {padding:26px 30px 23px 30px !important;}
.modal-content .modal-header h1 {
    font-weight: 600 !important;
  text-transform: uppercase;
} 
.modal-content .license-left span:not(.text-secondary), .modal-content .lease-left span:not(.text-secondary),
.modal-content .license-right span.text-success, .modal-content .lease-right span.text-success {font-weight: 500; }

.modal.show .modal-dialog {    
    max-width: 500px;
}
.real_right .right_mark {
    left: auto !important;
    right: 20px;
}
#addCardModalToggle .modal-dialog {
    max-width: 500px !important;
}
.cart-icon-hover {
    transition: all 0.2s ease-in-out 0s;
}
.cart-icon-hover:hover{
    border-color: #000 !important;
}
#classes-table .multi-select-actions {
    position: relative!important;
    top: auto!important;
    left: auto!important;
    height: auto!important;
}
#classes-table .multi-select-actions>div {
    align-items: center!important;
    height: auto!important;
    border-bottom: 1px solid #F0F0F0 !important;
}
.text-black-hover:hover {
    color: #000 !important;
}
.spots .spot:not(.checked):hover {
	background: #f0f0f0 !important;
}
.color-palette {
	position: absolute;
	top: 50%;
	left: calc(100% + 20px);
	background: #fff;
	padding: 30px;
	box-shadow: 0 0 30px #0000000d;
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
	transition: all .25s ease-in-out 0s;
	display: flex;
	max-width: 260px;
	flex-wrap: wrap;
	width: 1000%;
	z-index: 1111111;
	transform: translateY(-50%);
}
.color-palette.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}
.color-item.selected {
	box-shadow: 0 0 0 2px #fff, 0 0 0 3px #000;
}
.color-item {
	display: block;
	width: 30px;
	height: 30px;
	margin: 5px;
	border-radius: 50px;
}
.color-item:hover {
    box-shadow: 0 0 0 3px #dcdcdc;
}
.small-dropdown-row .select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: 12px !important;
}
.py-25px {
    padding-top: 25px !important;
    padding-bottom: 25px !important;
}
form {
    margin-bottom: 0;
}
.custom-fw-100 {
    font-weight: 100;
}
.custom-fw-200 {
    font-weight: 200;
}
.custom-fw-300 {
    font-weight: 300;
}
.custom-fs-400 {
    font-weight: 400;
}
.custom-fw-500 {
    font-weight: 500;
}
.custom-fw-600 {
    font-weight: 600;
}
.custom-fw-700 {
    font-weight: 700;
}
.custom-fw-800 {
    font-weight: 800;
}
.custom-fw-900 {
    font-weight: 900;
}
.custom-fs-10px {
    font-size: 10px;
}
.custom-fs-12px {
    font-size: 12px;
}
.custom-fs-14px {
    font-size: 14px;
}
.custom-fs-16 {
    font-size: 16px;
}
.custom-fs-18 {
    font-size: 18px;
}
.custom-fs-20 {
    font-size: 20px;
}
.border-radius-50 {
    border-radius: 50px;
}
.dashboard-wrap .logo {
    padding-bottom: 50px; 
    padding-top: 10px;
}
.dashboard-wrap .logo svg {width:100%;}
.table-filter-select + .select2-container--default .select2-selection__rendered {
    padding-right:20px !important;
    font-size: 12px !important;
}
.input-wrap {
    width:100%; 
    max-width: 500px;
}
input#shipping_fee, input#handling_fee {
    max-width: 90px; 
    text-align: left !important; 
    max-height:35px; 
    min-height: 35px !important;
    font-size: 12px;
}
.invoice-items input {
    text-align: left !important;
}
.rounded-pill {
    padding: 5px 10px !important;
    color:#fff !important;
    line-height: 1;
}
.rounded-pill.bg-success {background:rgba(82, 193, 90, 1) !important;}
.rounded-pill.bg-secondary {background:rgba(112, 112, 112, 1) !important;}
.rounded-pill.draft-pill {background-color: rgba(232, 175, 68, 1);}


.form-check .form-check-input {
	border-color: #ddd !important;
	border-radius: 4px !important;
}
.right-align {
    text-align: right !important;
}
.round-button-dropdown {
    display: flex; 
    justify-content: flex-end;
}
.border-div {
    border:1px solid #f0f0f0; 
    border-radius:10px; 
    padding:15px 0 20px 0;
}
.border-div  > div:not(:first-child) {
    padding-left:30px;
    padding-right:30px;
}
.border-div  > div:first-child .items-header, .border-div  > div:first-child .items-group  {
    padding-left:30px;
    padding-right:30px;
}
.colorRed,
.input-red {
    color:#DB1818 !important;
}
.input-h40 input, .input-h40 select, .input-h40 .form-group .select2-container .select2-selection--single .select2-selection__rendered {
    min-height:35px !important; 
    height:35px;
    font-size: 12px !important;
}
.remove-div {margin-right: 10px;}

 .invoice-items .list-group-item, .order-items .list-group-item {padding-top:20px; padding-bottom: 20px; border-bottom: 1px solid #f0f0f0;}
 .invoice-items .list-group-item label, .order-items .list-group-item label {display:none;}


.dashboard-widgets {
    justify-content: space-between;
}
.dashboard-widgets .col-8 {
    padding:0 !important; 
    width: 22.5%;
}
.dashboard-widgets .col-8 .customer-dashboard-card {
    width:100%;
}
form .form-group {
    margin-bottom:30px;
}
.form-group.license_studio label:first-of-type {margin-top: 0;}
.form-mb-0.form-group {margin-bottom: 0;}
form .border-div .form-group  {
    margin-bottom:0;
}
.auth-page-content-wrap input {
    border-radius: 0 !important;
    border-color:#f0f0f0 !important;
}
.lg-search .form-control.lg-search-expanded {
    border-radius: 50px !important;
}
.modal-dialog button {
    margin: 0 7px;
}
.title-left .back-link {
	margin-top: 1rem !important;
    font-weight: 400;
    display: block;
    line-height:1;
    font-size: 12px;
    text-decoration: none;
    color: #969696;
}
.page-title {
    border-bottom: 1px solid #f0f0f0;
    align-items: center !important;
    display: flex !important;
    justify-content: space-between !important;
}
.page-title .title-left {
    padding-top: 57px !important;
    padding-bottom: 57px !important;
}
.page-title .title-left h3 {
    line-height:1;
    margin-bottom: 0;
    text-transform: uppercase;
    font-size: 20px !important;
}
.ttl-dropdown button {
    margin-top: 10px; 
    margin-bottom: 10px;
} 
.order-border input {
    max-width: 400px;
}
.newitem-btn {
    font-size: 10px !important;
    padding: 10px 15px;
}
#charCount {display: none;}
.grandtotal-wrap {
    width:380px;
    border:1px solid #ddd;
    background-color: #FCFCFC;
    padding:30px 40px 30px;
    border-radius: 8px;
}
.grandtotal-wrap table {width:100%;}
.add-item {
    margin-left: 43px;
    margin-top: 20px;
    align-items: center;
}
.new-items-box a {
    text-decoration: none;
    display: inline-block;
}
.new-items-box a span {
    font-size: 10px !important;
    text-decoration: none;
    text-transform: uppercase;
    background: #000;
    color: #fff !important;
    padding: 10px 15px;
    border-radius: 50px;
    font-weight: 600;
    display:inline-block;
    line-height: 1;
    border: 1px solid #000;
}
.add-item a:hover span {
    background: #fff;
    color: #000 !important;
    }
.new-items-box a.text-secondary {margin-left:15px;}    
.three-fields {display:flex; justify-content: space-between;}    
.twofields {width:65.5%; display: flex; justify-content: space-between;}
.datepickerr {width: 100%; max-width: 500px;}
.invoice-fields .datepickerr, .order-info .datepickerr {width: 32%;;}

.twofields .input-wrap {width:48.3%;position: relative;}
.items-group .total {
  border: 1px solid #ddd;
  height: 35px;
  line-height: 35px;
  padding-left: 15px;
  border-radius: 8px;
  background: #f8f8f8;
} 
.fees-div { border-bottom: 1px solid #f0f0f0; padding-bottom: 30px; margin-bottom: 30px;}
.fees-div label {display: none;}
.fees-div .form-group {margin-bottom: 0 !important;}
.invoice-items .list-header {display: flex !important;}
.invoice-items-table {display:flex !important;}
.invoice-items-table .form-group, .items-header .inv-product, .orders-small-fields  .form-group {flex:1; max-width: 400px;} 
.invoice-items-table .item-number, .items-header .inv-id {flex:0 0 13px; text-align: center; margin-right: 30px;}
.invoice-items-table .unitprice-field, .items-header .inv-unitprice  {flex:0 0 100px; margin-left: 10px; margin-left: auto;}
.invoice-items-table .unitqty-field, .items-header .inv-qty {flex:0 0 50px; margin-left: 10px;}
.invoice-items-table .unitdiscount-field, .items-header .inv-discount {flex: 0 0 100px; margin-left: 10px;} 
.invoice-items-table .unitdeposit-field, .items-header .inv-deposit {flex: 0 0 100px; margin-left: 10px;} 
.invoice-items-table .discount_type-field, .items-header .inv-discount_type {flex: 0 0 70px; margin-left: 10px;} 
.invoice-items-table .total, .items-header .inv-total  {flex: 0 0 auto; margin-left: 10px; min-width:100px; padding-right:10px;}
.unitqty-field .form-control {padding-left: 10px !important;padding-right: 10px !important;}

.invoice-items-table .form-group .select2.select2-container.select2-container--default {max-width: 400px !important;} 
.cust-pass-text {margin-top:-4px;}
.cust-pass-text p {font-size: 10px; color:#DB1818;}
#id.sortable-list-header, .hide-transp {opacity:0; }
.index-addnew-mob {display: none; width: 45px;padding: 10px;font-size: 30px !important; align-items: center !important;justify-content: center !important;line-height: 1 !important;text-align: center;font-weight: 400 !important;}

/* Tables Columns width */
#suppliers-table .list-group-item, #suppliers-table .list-header {
	grid-template-columns: 0 0 200px auto 60px 30px;
}
#companies-table .list-header {
	grid-template-columns: 0 0px 260px auto 60px 30px;
}
#companies-table .list-group-item {
	grid-template-columns: 0 0 40px 140px auto 60px 30px;
}
#machines-table .list-group-item, #machines-table .list-header {
	grid-template-columns: 20px 200px auto 1fr 1fr 40px;
}
#orders-table .list-group-item, #orders-table .list-header {
	grid-template-columns: 0 0 140px 140px 120px 120px auto 55px 30px;
}
#customers-table .list-header, #customers-table .list-group-item {
    grid-template-columns: 0 5px 140px 140px 120px 120px 120px auto 60px 30px;  
}
#invoice-products-table .list-group-item, #invoice-products-table .list-header {
	grid-template-columns: 0 0 140px 140px 120px 120px auto 50px 30px;
}
#product-fees-table .list-group-item, #product-fees-table .list-header {
	grid-template-columns: 20px 200px auto 40px;
}
#bundles-table .list-group-item, #bundles-table .list-header {
	grid-template-columns: 20px 200px auto 40px;
}
#licences-table .list-group-item, #licences-table .list-header {
	grid-template-columns: 0px 0px 260px 140px 120px 0px auto 170px 40px;
}
#purchases-table .list-group-item, #purchases-table .list-header{
    grid-template-columns: 0% 0% 13% 5% 10% 10.6% 1fr 170px 3%;
}
#payment-history-table .list-group-item, #payment-history-table .list-header {
	grid-template-columns: 0px 2fr 1fr 1fr 1fr 1fr 100px 140px 30px;
    padding-left: 0 !important;
}
#upcoming-payments-table .list-group-item, #upcoming-payments-table .list-header {
	grid-template-columns: 0px 140px 150px 120px 120px auto 60px;
    padding-left: 0 !important;
}
#leases-table .list-group-item, #leases-table .list-header {
	grid-template-columns: 0px 0px 140px 110px 60px 100px 120px 120px auto 170px 30px;
}
#invoice-templates-table .list-group-item, #invoice-templates-table .list-header {
	grid-template-columns: 20px auto 40px;
}
#admin-notifications-table .list-group-item, #admin-notifications-table .list-header {
    grid-template-columns: 20px 200px;
}
#payments-history-table .list-group-item, #payments-history-table .list-header {
    grid-template-columns: 0px 140px 150px 100px 120px 120px auto 75px 30px;
    padding-left: 0 !important;
}

#agreements-table .list-group-item, #agreements-table .list-header, #all-agreements-table .list-group-item, #all-agreements-table .list-header {
	grid-template-columns: 0px 0px 140px 140px 120px auto 170px 30px !important;
}
#all-agreements-table .list-group-item, #all-agreements-table .list-header {
	grid-template-columns: 0px 0px 220px 120px 80px 135px auto 170px 30px !important;
}
/*************/
#users-table .list-group-item, #users-table .list-header {
    grid-template-columns: 20px 60px 3fr 12% 12% 16% 80px;
    column-gap: 20px;
}
#classes-table .list-group-item, #classes-table .list-header {
    grid-template-columns: 20px 90px 60px 2fr 16%;
    column-gap: 20px;
}
#instructors-table .list-group-item, #instructors-table .list-header {
    grid-template-columns: 20px 60px 2fr 12% 12% 16% 80px;
    column-gap: 20px;
}
#payment-table .list-group-item, #payment-table .list-header {
	grid-template-columns: 3fr 1fr 1fr 1fr 80px;
}
#packages-table .list-group-item, #packages-table .list-header {
	grid-template-columns: 20px 3fr 12% 12% 12% 8%;
}
#invoices-table .list-group-item, #invoices-table .list-header {
	grid-template-columns: 0px 0px 140px 140px 120px 120px auto 40px 40px;
}

#products-table .list-group-item, #products-table .list-header{
	grid-template-columns: 20px auto 120px 120px 120px 120px 60px;
}
.order-items .items-header {grid-template-columns: 35px auto 90px;}
.order-items .items-group {grid-template-columns:35px 33.5% auto 90px;} 

 
/*#customers-table .list-header, #customers-table .list-group-item {display:flex !important; justify-content: space-between;}
#customers-table .list-header .hide-transp, #customers-table .list-group-item .hide-transp {display:none !important;}
#customers-table .form-check {width: 20px; flex: 0 0 20px; min-width:20px;}
#customers-table .cust-name {width: auto; flex: 0 0 auto; min-width:200px;}
.licenses-preview  {width: auto; flex: 0 0 auto; min-width:115px;}
.exclusivity-preview {width: auto; flex: 0 0 auto; min-width:145px;} 
.leases-preview {width: auto; flex: 0 0 auto; min-width:100px;}
.remaining-preview {width: auto; flex: 0 0 auto; min-width:100px;margin-right: auto;}
.cust-statusbadge {width: auto; flex: 0 0 auto; min-width:80px;}
.cust-status {width: auto; flex: 0 0 auto; min-width:80px;}
#customers-table .round-button-dropdown {width: 33px; flex: 0 0 33px;}*/

/* .entity-table {
	overflow-x: auto !important;
} */
input.form-control:disabled {
	background-color: #eee !important;
	opacity: 1;
}
.currency-symbol + input {
	padding-left: 25px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	color: #000;
}
.select2-container--default .select2-results__group {
	padding: 6px 6px 22px 0px;
	font-size: 12px;
	font-weight: 500;
	text-transform: uppercase;
}
.select2-container--default .select2-results__option .select2-results__option {
	padding-left: 0;
	font-size: 12px;
}
.max-title-width > span:first-child {
	max-width: 280px;
	white-space: normal;
	line-height: 1.2;
}
.input-h40 .form-group .select2-container .select2-selection--single .select2-selection__rendered {
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: calc(100% - 30px);
	display: inline-block;
	line-height: 35px !important;
    color: #000 !important;
}
.input-error {font-size: 10px !important; margin-top: 7px; display: block;}
.showpass {cursor: pointer; z-index: 99;position: relative; width: 100%; max-width: 500px;}
.showpass svg {position: absolute; right: 15px; margin-top: -55px;}
.internal-notes .delete-note {font-size: 10px !important; padding: 10px 15px;}
.login-wrap {padding: 60px 25px 35px; border-radius: 10px; max-width: 500px;}
.login-above {padding-left: 25px; padding-right: 25px;}
.login-title {margin-bottom: 60px;}
.login-above a.text-secondary:hover {text-decoration: underline !important;}
.login-bottom {border-top:1px solid #f0f0f0; padding-top:34px; margin-top: 40px; text-align: center;}
.login-bottom a {margin-bottom: 0; text-decoration: underline;}
.login-bottom a:hover {color:#969696;}
.login-wrap input.line-style  {border:1px solid #ddd !important; background:#fdfdfd !important; border-radius: 8px !important; padding-left:15px !important; height:40px !important;}
.login-wrap input#email {margin-bottom: 15px;}
.login-submit {margin-top:20px;}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {box-shadow: none !important;}
.btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible {
box-shadow: none !important;}
#statusSelect {font-size:12px; color:#969696; padding-right: 0;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5'%3E%3Cpath id='Path_7017' data-name='Path 7017' d='M0,0,4,5,8,0Z' transform='translate(0 0)' fill='%23969696'/%3E%3C/svg%3E%0A");
    background-position-x: 0%;
    background-position-y: 0%;
    background-repeat: repeat;
  background-repeat: no-repeat;
  background-position: right;
  padding-right: 18px;
  width: auto;
  padding-top: 0;
  padding-bottom: 0;
}

.order-items .items-group {
	grid-template-columns: 35px 33.5% auto 90px;
	display: flex !important;
}
.select2-tooltip {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    font-size: 10px;
    font-style: normal;
    border-radius: 20px;
    border: 1px solid red;
    color: red !important;
}
/*.filter-dropdown:before {content:"Sort by:"; margin-right: 5px; font-size: 12px; color:#969696;}*/
.filter-dropdown {display: flex; border: 1px solid #ddd; padding: 6px 10px 6px 15px; border-radius: 10px; align-items: center;}
.filter-dropdown:hover {box-shadow: 0 0 50px rgba(51, 51, 51, 0.1);}
.entity-table {border: 1px solid #f0f0f0; border-radius: 10px;}
.entity-table .list-header {background-color: #fcfcfc; border-top-left-radius: 10px; border-top-right-radius: 10px; font-weight: 500 !important; padding: 15px 20px !important;}
.entity-table:has(.no-results-txt) {border-left: none; border-right:none; border-bottom:none; border-radius: 0;}
.nr-items {padding-top: 1.2499788rem; padding-bottom: 1.2499788rem; align-items: center; display: flex; justify-content: space-between;}
.nr-items h5 {font-size: 12px; font-weight: 400 !important; margin:0;}
.sortbysearch {display: flex; align-items: center; justify-content: flex-end; z-index: 100;}
.studiospopup .studios-popup-data {display: flex; justify-content: space-between; padding-top: 15px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;}
.active-licenses-data.studios-popup-data:last-of-type {border-bottom: none;}
.studios-popup-data .license-left, .studios-popup-data .license-right, .studios-popup-data .lease-left, .studios-popup-data .lease-right {padding-left: 15px; padding-right: 15px;}
.has-danger input, .has-danger .select2-container--default .select2-selection--single {border-color: #db1818 !important;}

/*inner pages subtitles*/
.form-section-title {font-size: 14px; text-transform: uppercase; margin-top: 48px; margin-bottom: 48px; padding-top:48px; border-top:1px solid #f0f0f0; line-height: 1;}
.form-section-title.first-title {border-top:none; padding-top:0;}
.form-section-title.below-form {margin-top: 20px;}
.main-subtitle {padding-top:48px; padding-bottom: 48px; border-bottom: 1px solid #f0f0f0;}
.main-subtitle h5 {font-size: 14px; text-transform: uppercase; margin:0; line-height: 1;}
/**/
.no-results-txt {font-size: 14px; color:#969696; padding-top: 42px;}
.table-name-col {font-weight: 500 !important; margin-top: 0 !important; margin-bottom: 0 !important;}
.int-note-box {border:1px solid #f0f0f0; border-radius: 10px; padding: 30px; margin-top: 20px;}
.int-note-box:first-of-type {margin-top: 30px;}
.agree-files-btn {margin-right: 0 !important;}
.invoice-items-table.add-item .new-items-box {min-width:225px;}
.deposit-wrap.disabled {
    pointer-events: none;
}
.deposit-wrap.disabled p {
    display: block !important;
}
.deposit-wrap.disabled input {
    background: #f4f4f4 !important;
}
.bababa {display: none;}
.info-statuses {
	background: #fdfdfd;
	color: #000;
	border: 1px solid #ddd;
	border-radius: 50px;
	cursor: pointer;
	font-size: 10px;
	font-weight: 600;
	text-transform: uppercase;
	width: 52px;
	height: 52px;
	border-radius: 50px;
	padding: 0;
	z-index: 10;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
    text-decoration: none;
}
.info-statuses:hover {
	color: #fff;
	background: #000;
	border-color: #000;
}
.main-subtitle .info-statuses {
    float: right;
  margin-top: -32px;
}

#popup-agr-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease;
    z-index: 9999;
}
 
#popup-agr-overlay.show {
    opacity: 1;
    visibility: visible;
}

#popup-agr-overlay #popup-agr-body {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    transition: opacity 0.5s ease;
    z-index: 1000;
    width: 90%;
    max-width: 500px;
    border-radius: 10px;
    }

 
.popup-tabs .popup-tab-content {
  display:none;
 
}
.popup-tabs .popup-tab-content.active {
  display:block;
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}


.popup-tabs .popup-tab-content p {
    margin: 0;
    font-weight: 500;
}
.popup-tabs .popup-tab-content span {
    color: #969696;
}
.popup-tabs .popup-tab-content:not(.active) p, .popup-tabs .popup-tab-content:not(.active) span {
    font-size: 0;
}

.popup-tabs .tab-button.active {
  font-weight: bold;
  background-color: #ddd;
}
.popup-tabs .tab-buttons .tab-button {
    font-size: 12px;
  color: #969696;
  background: none !important;
  border: none;
  font-weight: 400;
  text-transform: none !important;
  padding: 0;
  margin-right: 15px;
  letter-spacing: 0.05em !important;
}
.popup-tabs .tab-buttons .tab-button.active {
  color: #000;
}
#popup-agr-body .modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #f0f0f0;
}
#popup-agr-body .modal-header h1 {
    font-weight: 600;
    font-size: 14px;
}
#popup-agr-body .modal-header .btn-close {
    position: absolute;
    right: 30px;
}
#popup-agr-body .tab-buttons {
    padding: 10px 30px;
    border-bottom: 1px solid #f0f0f0;
}
.popup-tabs .status-box {
    border-bottom: 1px solid #f0f0f0;
    padding:10px 55px 10px 30px;
    position: relative;
}
.popup-tabs .status-box:last-of-type {
    border-bottom: none;
}
.text-blue {color:#4791D1;}
.circle-box {width:15px; height:15px; position:absolute; right:30px; border-radius: 50%;top: 50%; margin-top: -7.5px;}
.circle-box.orange {background: #E8AF44;}
.circle-box.black {background: #000;}
.circle-box.blue {background: #4791D1;}
.circle-box.green {background: #52C15A;}
.circle-box.grey {background: #969696;}
.circle-box.red {background: #DB1818;}















/*  RESPONSIVE  */
@media(min-width: 1400px){
.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
	max-width: 1280px !important;
}
}

@media(max-width: 1600px){
    #customers-table.entity-table, #leases-table.entity-table {overflow-x: auto !important;}
    #customers-table.entity-table .list-group, #customers-table.entity-table .list-group-item, #customers-table.entity-table .list-header {width:1200px !important;}
    #leases-table.entity-table .list-group, #leases-table.entity-table .list-group-item, #leases-table.entity-table .list-header {width:1150px !important;}
}

 @media(max-width: 1450px){
   #licences-table.entity-table, #payment-history {overflow-x: auto !important;}
   #licences-table.entity-table .list-group, #licences-table.entity-table .list-group-item, #licences-table.entity-table .list-header {width:1050px !important;}
   #payments-history-table.entity-table {overflow-x: auto !important;}
   #payments-history-table.entity-table .list-group, #payments-history-table.entity-table .list-group-item, #payments-history-table.entity-table .list-header {width:1050px !important;}
   #payment-history .list-group, #payment-history .list-group-item, #payment-history .list-header {width:1050px !important;}
   .table-invoice-main {overflow-x: auto;}
   .table-invoice-main .invoice-items {width: 1200px;}
   .table-invoice-main .invoice-items-table.add-item {width: 1157px;}
    
 }

@media(max-width: 1366px){
    body.side-menu #dashboard-side {
        width: 220px;
    }
    .dashboard-side .logo {
        width: 155px;
    }
    .main-content {
        padding: 0 40px;
    }
    .main-content>.navbar {
        margin-right: -40px;
        margin-left: -40px;
    }
    .dash-stats .stat .stat-content {
        padding-left: 20px;
        padding-right: 20px;
    }
    .spots .spot {
        width: 60px;
        height: 60px;
        line-height: 60px;
    }
    .py-3.py-sm-7 {
        padding-bottom: 30px !important;
        padding-top: 30px !important;
    }
    .py-4.py-sm-6 {
        padding-bottom: 30px !important;
        padding-top: 30px !important;
    }
    .py-5.py-md-10 {
        padding-bottom: 50px !important;
        padding-top: 50px !important;
    }
    .py-5.py-sm-7 {
        padding-bottom: 35px !important;
        padding-top: 35px !important;
    }    
    .py-6.py-md-11 {
        padding-bottom: 60px !important;
        padding-top: 60px !important;
    }
    .my-6.my-sm-8 {
        margin-bottom: 40px !important;
        margin-top: 40px !important;
    }
    .py-6.py-sm-8 {
        padding-bottom: 40px !important;
        padding-top: 40px !important;
    }
    .py-7.py-sm-11 {
        padding-bottom: 60px !important;
        padding-top: 60px !important;
    }
    .py-7.py-md-11 {
        padding-bottom: 60px !important;
        padding-top: 60px !important;
    }
    .fs-18px {
        font-size: 16px!important;
    }
    .trial-upgrade {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        gap: 3px;
        white-space: normal;
        min-width: 40px;
        padding: 11px 10px;
        flex-direction: column;
        line-height: 1.2;
    }
}
.overflow-x-1280 {
    overflow-x: auto;   
}
@media(min-width: 1200px){
    .overflow-x-1280 {
        overflow-x: visible;   
    }
}
@media(max-width: 1199px){
    .entity-table {overflow-x: auto !important;}
    .entity-table .list-group, .entity-table .list-header  {width:900px !important;}
    .table-invoice-tpl {overflow-x: auto;}
    .table-invoice-tpl .invoice-items {width: 1200px;}
    .table-invoice-tpl .invoice-items-table.add-item {width: 1157px;}

}
@media(max-width: 1024px){
    .dash-stats {
        gap: 20px;
    }
    .dash-stats .stat {
        padding-bottom: 90%;
    }
    .spots .spot {
        width: 30px;
        height: 30px;
        line-height: 30px;
        min-width: 30px;
        min-height: 30px;
        max-width: 30px;
        max-height: 30px;
        font-size: 12px;
    }
    .spots > div.d-flex {
        gap: 25px !important;
    }
    .stat-content .description {
        font-size: 14px !important;
    }
    #instructors-table .list-group-item, #instructors-table .list-header {
        grid-template-columns: 20px 60px 2fr 12% 10% 18% 80px;
        column-gap: 15px;
    }
    .dash-stats .stat .stat-status {
        bottom: 14px;
    }
}

@media(max-width: 768px){
.login-bottom {padding-top:25px; margin-top: 25px;}
.login-submit {margin-top:10px;}
.page-title .title-left h3 {font-size: 16px !important;}
.title-left .back-link {margin-top: 0.7rem !important;}

}


@media(max-width: 576px){
    #internal-note {
        height: 120px;
    }
    .datepickerr.date-selector-wrap .datepicker {
        top: auto !important;
    }
    .right-recycle-position.rounded-pill {
        transform: translateX(20px);
    }
    .pb-120 {
        padding-bottom: 120px !important;
    }
    body {
        overflow: hidden !important;
    }
    .flex-md-nowrap {
        flex-wrap: nowrap !important;
    }
    .error-msg.text-danger {
        width: 90vw;
        font-size: 10px;
    }
    main.client-main .main-content{
        height: 100vh !important;
    }
    body {
        overflow: visible;
        overflow-x: hidden;
    }
    body.side-menu #toast-container, body.side-menu .fl-main-container {
        left: 50%;
        top: auto !important;
        bottom: 0vh !important;
    }
    .placeholder-shown {
        display: none !important;
    }
    .with-center-align .stat-content {
        margin-top: auto;
    }
    .modal {
        z-index: 12000;
    }
    [id*=addCard] .modal-dialog,
    [id*=deleteModal] .modal-dialog {
        margin: 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        z-index: 11111;
    }
    .form-control {
        min-height: 50px;
    }
    body [id*=addCard]  .modal-dialog form .modal-header .btn-close,
    body [id*=deleteModal] .modal-dialog form .modal-header .btn-close {
        display: none !important;
    }
    body .modal-dialog form .border-bottom {
        border-bottom: none !important;
    }
    body .modal-dialog form {
        margin: 0;
    }
    .modal-header.py-4.border-bottom.justify-content-center h1 {
        transform: translateY(45px);
        font-size: 16px !important;
        font-weight: 600 !important;
    }
    .modal-header.py-4.border-bottom.justify-content-center h1 {
        transform: translateY(45px);
        font-size: 16px !important;
        font-weight: 600 !important;
    }
    .auth-card {
        height: 100vh;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        padding: 27px 0 0;
    }
    main.sidebar {
        overflow: hidden;
    }
    .date-selector-wrap .datepicker::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.3);
        z-index: -1;
    }
    .datepicker td, .datepicker th {
        height: 12vw !important;
    }
    .date-selector-wrap .datepicker {
        border: none !important;
        box-shadow: 0 0 0 500px rgba(0,0,0,0.3) !important;
        top: auto !important;
        position: fixed !important;
        bottom: 0 !important;
        width: 100% !important;
        left: 0 !important;
        margin: 0 !important;
        z-index: 11111 !important;
    }
    .main-content .logo {
        display: block;
        margin-right: auto;
        margin-left: 20px;
    }
    #register1ModalToggle input,
    #register2ModalToggle input {
        margin-bottom: 10px;
    }
    .main-content {
        padding: 0;
        transition: margin 0.35s ease !important;
        min-width: 300px !important;
        overflow: auto;
    }
    .main-content.opened > .navbar {
        left: 300px !important;
    }
    .main-content > .navbar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        z-index: ********* !important;
        width: 100% !important;
        transition: left .35s ease !important;
    }
    .main-content > .navbar > .logo > svg {
        width: 140px !important;
    }
    .main-content > .navbar > .hamburger, .main-content > .navbar > .logo, .main-content > .navbar .acc-notification > svg {
        filter: invert(1);
    }
    .acc-notification {
        background-color: rgba(0,0,0,0);
    }
    #dropdownMenuAccount {
        box-shadow: 0 0 0 1px #444;
    }
    .main-content.opened {
        margin-left: 300px !important;
    }
    .profile-thumbnail {
        width: 60px;
        height: 60px;
    }
    .fs-sm-14px{
        font-size: 14px !important;
    }
    .main-content > .navbar {
        margin-right: 0;
        margin-left: 0;
        padding-right: 20px !important;
        padding-left: 20px !important;
        background-color: #000;
        z-index: 111111;
    }
    .dash-stats-container {
        margin: 0 15px 0 0;
        overflow-x: auto;
        overflow-y: hidden;
    }
    .dash-stats {
        display: flex;
        gap: 20px;
        grid-template-columns: 1fr 1fr;
        width: 240vw;
        flex-wrap: nowrap;
        padding: 0 2px 20px 0;
    }
    .dash-stats .stat {
        height: 150px;
        padding-bottom: 0;
        width: 57vw;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .dash-stats .stat .stat-content .description {
        line-height: 1.2;
    }
    .dash-stats .stat .stat-content {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        text-align: center;
        width: 100%;
        margin-bottom: 0;
        row-gap: 10px;
    }
    .dash-stats .stat .stat-status {
        bottom: auto;
        position: relative;
        left: auto;
        right: auto;
        text-align: center;
        margin-top: auto;
        margin-bottom: 10px !important;
    }

    #dashboard-side .logo {
        display: none;
    }
    .dashboard-side .dashboard-wrap {
        width: 300px;
    }
    .dashboard-side {
        z-index: 1000000;
        left: 0;
        transition: width 0.35s ease !important;
    }
    .dashboard-side .hamburger {
        display: none !important;
    }
    .dashboard-side .logo {
        display: none !important;
    }
    .dashboard-side .dashboard-wrap {
        width: 300px;
    }
    .dashboard-side .menu-item {
        text-transform: none;
        line-height: 1;
    }
    body #dashboard-side {
        width: 0;
    }
    main.sidebar #dashboard-side {
        width: 300px;
    }
    body.side-menu #dashboard-side {
        width: 0;
    }
    body main.sidebar #dashboard-side {
        width: 300px;
    }
    .btn-border {
        gap: 10px;
        padding: 8px 10px;
        border: 1px solid rgba(255,255,255,0.1);
        width: 100px;
        text-transform: uppercase;
    }
    .btn-border img {
        width: 10px;
    }
    .location-modal {
        bottom: 0;
        height: auto;
        top: auto;
    }
    .location-modal.modal.fade .modal-dialog {
      transform: translate3d(0, 25%, 0);
    }
    .location-modal.modal.show .modal-dialog {
      transform: translate3d(0, 0, 0);
    }
    .location-modal .modal-dialog {
        top: auto;
        right: auto;
        left: auto;
        bottom: 0;
        width: 100%;
        margin: 0;
        height: auto !important;
    }
    .btn {
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .schedule-wrap.entity-table {
        margin: 0px -12px 0;
    }
    .profile-thumbnail {
        width: 60px;
        height: 60px;
    }
    #locationModalToggle .modal-header {
        justify-content: space-between !important;
    }
    #dropdownMenuAccount + .dropdown-menu {
        top: 38px !important;
        left: 0;
        position: fixed;
    }
    .admin-avatar {
        width: 60px;
        height: 60px;
    }
    .mx-n12 {
        margin-left: -12px !important;
        margin-right: -12px !important;
    }
    .mx-n15 {
        margin-left: -15px;
        margin-right: -15px;
        padding-right: 15px;
        padding-left: 15px;
    }
    .fs-18px{
        font-size: 14px !important;
    }
    .date-selector-wrap {
        min-width: 160px;
    }
    .date-selector-wrap > input {
        width: 157px;
    }
    .spots .spot {
        width: 25px;
        height: 25px;
        font-size: 11px;
        line-height: 25px;
        min-width: 25px;
        max-width: 25px;
        min-height: 25px;
        max-height: 25px;
    }
    .go-to-date img {
        height: 12px;
    }
    .entity-grade {
        width: 25px;
        height: 25px;
    }
    textarea.form-control {
        max-height: none;
    }
    body.side-menu #dashboard-icons,
    body.mini-menu #dashboard-icons {
        display: none !important;
    }
    .profile-thumbnail-60 {
        width: 50px;
        height: 50px;
        min-width: 50px;
        min-height: 50px;
        overflow: hidden;
        border-radius: 50px;
    }
    .fw-sm-normal {
        font-weight: 500 !important;
        text-transform: uppercase !important;
    }
    .fw-sm-semibold {
        font-weight: 600 !important;
        text-transform: uppercase !important;
    }
    .right-recycle-position {
        right: 10vw;
    }
    .card-wrap {
        position: relative;
    }
    .btn-border-locations {
        width: 125px;
        font-size: 10px;
    }
    .lg-search .form-control.lg-search-expanded {
        width: calc(100vw - 29px);
        padding-left: 20px;
        position: absolute;
        right: 0;
        top: 0px;
        z-index: 11111;
    }
    .entity-table .list-header {
        padding: 15px;
    }
    .table-filter-select {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .profile-thumbnail + .entity-grade {
        font-size: 10px;
    }
    .fs-mob-14px {
        font-size: 14px !important;
    }
    .fs-mob-12px {
        font-size: 12px !important;
    }
    .fs-mob-10px {
        font-size: 10px !important;
    }
    [id*=deleteModal] .modal-footer {
        flex-direction: column;
        align-items: unset;
        gap: 10px;
        padding-right: 15px!important;
        padding-left: 15px!important;
        padding-top: 5px!important;
    }
    [id*=deleteModal] .modal-footer .btn {
        width: 100%; /* Full width button on small screens */
        padding: 10px 15px; /* Adjust padding */
        font-size: 14px; /* Reduce font size */
        margin-bottom: 10px; /* Add some spacing between buttons */
    }
    .login-modal-content input {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    .toastr-popup {
        width: 93%;
        top: auto !important;
        left: 50%;
        min-height: 80px;
        height: auto;
        bottom: 60px;
    }
    .list-group-item .btn {
        min-width: 86px;
    }
    .rating-stars svg {
        width: 14px;
        height: 14px;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        font-size: 12px!important;
    }
    .btn.btn-edit {
        height: 32px;
        padding: 5px 15px;
        font-size: 10px !important;
        line-height: 1;
    }
    .table-filter-select+.select2-container--default .select2-selection__rendered {
        padding: 0px 26px 0px 0!important;
    }
    .modal-header .modal-title {
        margin-bottom: 0;
        line-height: var(--bs-modal-title-line-height);
        padding-right: 0;
        text-align: center !important;
        line-height: 1.6 !important;
    }
    .flat-info-table tr:first-of-type th,
    .flat-info-table tr:first-of-type td {
        padding-left: 15px;
    }
    .w-mob-100 {
        width: 100%;
    }
    .absolute-mob {
        position: absolute;
        right: 20px;
    }
    .mob-right-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 20px;
        z-index: 11;
    }
    .box-stats-front-container {
        margin: 0;
        overflow-x: auto;
        overflow-y: hidden;
    }
    .box-stats-front-wrap {
        width: 200vw;
        gap: 20px;
    }
    .box-stats-front-wrap .box-stats-front {
        border: 1px solid rgba(var(--bs-light-rgb),var(--bs-border-opacity))!important;
    }
    .calendar-wrap .arrow.prev-arrow {
        left: -20px;
    }
    .calendar-wrap .arrow.next-arrow {
        right: -20px;
    }
    .active_date {
        background: #000;
        border-radius: 50px;
        color: #fff !important;
    }
    img.rounded-circle.list-thumbnail.me-4 {
        width: 50px;
        height: 50px;
        max-width: 50px;
        max-height: 50px;
        min-width: 50px;
        min-height: 50px;
    }
    #accordionPackages .list-group-item-action:hover {
        background: none !important;
    }
    #addCardModal .modal-content {
        padding-top: 100px !important;
    }
    #addCardModal .modal {
        z-index: 111111111 !important;
    }
    body.mini-menu #toast-container, body.mini-menu .fl-main-container {
        left: 50%;
    }
    body #toast-container, body .fl-main-container {
        top: auto!important;
        z-index: ************;
        bottom: 3vh;
    }
    .lh-mob-base {
        line-height: 1.5!important;
    }
    /*NAVTAB RESPONSIVE*/
    .tab-container {
        overflow-x: auto;
        white-space: nowrap;
    }
    .nav-tabs {
        display: flex;
        flex-wrap: nowrap;
        width: 100%; /* Ensure the nav-tabs takes the full width of the screen */
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; /* Enable smooth scrolling on touch devices */
    }
    .nav-item {
        flex: 1 0 auto; /* Prevent items from shrinking and wrapping */
        text-align: center; /* Center-align the tab text */
    }
    .nav-link {
        white-space: nowrap;
        width: 100%; /* Let the nav-link take full width inside each nav-item */
    }
    /* Pagination Scroll */
    .pagination-scroll {
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
    }
    .pagination-scroll .page-item {
        flex-shrink: 0;
    }
    .pagination-scroll::-webkit-scrollbar {
        width: 6px; /* Adjust the scrollbar width if needed */
        height: 6px; /* Adjust the scrollbar height if needed */
    }
    .pagination-scroll::-webkit-scrollbar-thumb {
        background-color: #ccc; /* Customize scrollbar color */
        border-radius: 10px;
    }
    .mr-mob-0 {margin-right: 0;}
    .input-wrap {width:100%; max-width: 500px;}
    .fdx-mob {flex-direction: column;}
    .page-title .title-left {padding-top: 47px !important; padding-bottom: 47px !important;}
    .mt-50, .mt-45 {margin-top:30px;}
.mb-45 {margin-bottom:25px;}
.pt-45 {padding-top:25px;}
.pb-45, .pb-50 {padding-bottom:25px;}
.twofields, .twofields .input-wrap, .datepickerr {
	width: 100%;
}
.three-fields {
	flex-direction: column;
}
.login-above {
	padding-left: 0;
	padding-right: 0;
}
.index-addnew-desk {
    display: none !important;
}
.index-addnew-mob {
    display: flex;
}
.delete-btn {
    width: 100%;
    margin-top: 15px;
}
.buttons-wrapper {
	flex-direction: column;
}
.back-btn, .cancel-btn, .saveastpl-btn {
 display: none;   
}
.login-title {
    padding-left: 20px !important;
    padding-right: 20px !important;
    padding-bottom: 23px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 30px !important;
}
.login-above form {padding-left: 50px; padding-right: 50px;}
.login-above form .btn {height:46px;}


#popup-agr-overlay #popup-agr-body {
	top: 0;
	left: 0;
	transform: translate(0, 0);
	width: 100%;
	max-width: 100%;
    border-radius: 0;
    height: 100%;
    padding-top: 67px;
    overflow-y: auto;
}
}

@media only screen and (max-height: 700px) and (min-width: 577px) {
    #popup-agr-overlay #popup-agr-body {
	top: 0;
	transform: translate(-50%, 100px);
	height: 70vh;
    overflow-y: auto;
}
}

.btn {
	border-radius: 40px;
	font-size: 12px;
	font-weight: 600 !important;
	text-transform: uppercase !important;
}
#dashboard-dropdown-content {
	min-width: 130px;
    padding: 20px 25px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
}
.customer-dashboard-card-body {
	display: flex;
	flex-direction: column;
	gap: 5px;
}
#dashboard-dropdown-content a {
	padding: 0;
}
#dashboard-dropdown-content a:hover {
	background: none !important;
    text-decoration: underline !important;
}
.customer-dashboard-card-text {
	font-size: 12px;
	margin-bottom: 0;
	color: #969696 !important;
}
.nav-tabs .nav-item .nav-link {
	font-size: 11px;
	font-weight: 600 !important;
    padding: 16px 0 !important;
}
.nav-tabs .nav-item .nav-link.active:after {
	content:"";
    width: 100%;
    height: 1px;
    background-color: #000;
    position: absolute;
    left: 0;
    bottom: 0;
}

.entity-table .sort-icon {
	position: relative;
	top: -4px;
}
.entity-table .sort-icon::before {
	content: '';
	border: 4px solid transparent;
	border-top: 6px solid #E6E6E6;
	position: absolute;
	top: 8px;
	right: -4px;
	width: 8px;
	height: 6px;
	z-index: 0;
}
.entity-table .desc {
	border-width: 0 4px 6px 4px;
	border-color: transparent transparent rgb(227, 227, 227) transparent;
}
.sortable-list-header:not(.active) .sort-icon.asc {
	border-color: transparent transparent rgb(227, 227, 227) transparent;
}
.sortable-list-header:not(.active) .sort-icon.desc::before {
	border-top: 6px solid rgb(227, 227, 227);
}
.sortable-list-header.active .sort-icon.desc::before {
	border-top: 6px solid #969696;
}
.dropdown-menu[data-bs-popper] {
	border: none;
	border-radius: 8px;
}
.select2-container--default .select2-selection--single {
	height: auto;
	border: 1px solid #ddd;
	border-radius: 8px;
	outline: none;
    background: #FDFDFD;
}
.page-link {
	color: #969696;
    fill: #969696;
	border: 1px solid #DDDDDD;
	border-radius: 4px;
	width: 30px;
	padding: 0;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 5px;
}
.page-link.active, .active > .page-link {
	z-index: 3;
	color: #fff;
	background-color: var(--bs-pagination-active-border-color);
	border-color: var(--bs-pagination-active-border-color);
}
.page-link:hover {
	color: #969696;
	background-color: var(--bs-pagination-hover-border-color);
	border-color: #DDDDDD;
}

.form-check-input {
	border-color: #DDDDDD;
	border-radius: 4px;
}
.list-header {
	align-items: center;
}
.text-dark {
    color: #000 !important;
}
#iframe-popup {
	position: fixed;
	top: 30px;
	left: 30px;
	width: calc(100% - 60px);
	height: calc(100% - 60px);
	z-index: 1111111111;
	background: #fff;
	box-shadow: 0 0 30px rgba(0,0,0,0.3);
	border-radius: 20px;
}
#iframe-popup .close_iframe{
	position: absolute;
	top: -15px;
	right: -15px;
	width: 30px;
	height: 30px;
	z-index: 1111111111;
    background: #000;
    color: #fff;
    border-radius: 50%;
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}   
#iframe-popup iframe{
	width: calc(100% - 40px);
	height: calc(100% - 40px);
    border: none;
    margin: 20px;
}
.btn-close.mark-as-read-btn {
	transform: scale(0.8);
}
.read-notification {
    cursor: pointer;
}
.small-badge {
	width: 16px;
	height: 16px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}
.agreement_popup_overlay {
	position: fixed;
	top: 0%;
	left: 0%;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.5);
	z-index: 1111111110;
}
.agreement_popup {
	position: fixed;
	top: 50%;
	left: 50%;
	width: 880px;
	height: 90vh;
	overflow-y: auto;
    overflow-x: hidden;
    font-family: Roboto, sans-serif;
    line-height: 1.65;
	padding: 30px 40px;
	background: #fff;
	z-index: 1111111111;
	transform: translate(-50%, -50%);
}
.agreement_popup .close-popup {
	position: absolute;
	top: 10px;
	right: 15px;
	font-size: 30px;
    line-height: 1;
	cursor: pointer;
}
.agreements-err {
	display: inline-block;
	color: red;
}
.agreement-html .page-break {
	height: 10px;
	background: rgba(0,0,0,0.4);
	margin-left: -36pt;
	width: calc(100% + 72pt);
	margin-bottom: 30px;
}
.agreement-html {
	font-size: 14px;
}
