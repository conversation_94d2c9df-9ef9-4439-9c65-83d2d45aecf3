<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Purchase\ChangePurchaseStatusRequest;
use App\Http\Requests\Admin\Purchase\StorePurchaseRequest;
use App\Http\Requests\Admin\Purchase\UpdatePurchaseRequest;
// use App\Http\Requests\Admin\License\ChangeLicenseStatusRequest;
// use App\Http\Requests\Admin\PurchaseNote\AddPurchaseNoteRequest;
// use App\Http\Requests\Admin\PurchaseFile\AddPurchaseFileRequest;
use App\Mail\PaymentLinkEmail;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Purchase;
use App\Models\License;
use App\Models\Machine;
use App\Models\Payment;
use App\Models\State;
use App\Models\Studio;
use App\Services\Admin\AdobeSign\AdobeSignService;
use App\Services\Admin\Purchase\IPurchaseService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    private IPurchaseService $purchaseService;

    public function __construct(
        IPurchaseService $purchaseService,
    )
    {
        $this->purchaseService = $purchaseService;
    }

    public function index(Customer $customer): View
    {
        $customerPurchases = Customer::with('purchases');

        return view('admin.purchases.index', compact('customer', 'customerPurchases'));
    }

    public function show(Customer $customer, Purchase $purchase, Request $request): View
    {
        $tab             = $request->query('tab', 'profile');
        $currentCurrency = AdminSettings::first()->currency;

        return view('admin.purchases.show', compact('customer', 'purchase', 'tab'));
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';

        $purchases = $this->purchaseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($purchases as $purchase) {
            $purchase['sequential_id'] = $sequential_id++;
            // $purchase['machine'] = Machine::find($purchase->machine_id);
            $machine = Machine::where(['id' => $purchase->machine_id])->first();
            $purchase['machine'] = $machine->name;
            // $purchase['payments'] = Payment::where('purchase_id', $purchase->id)->first();
            if(!empty($purchase['payments'])){
                if($purchase['payments']->count() > 0){
                    $purchase['payments'] = $purchase['payments'][0];
                }
                $purchase['invoice'] = Invoice::where('payment_id', $purchase['payments']->id)->first();
            }else{
                $purchase['invoice'] = null;
            }
            $agreement = Agreement::where('purchase_id', $purchase->id)->first();
            $purchase['agreement'] = $agreement;
            $sign = NULL;
            if($purchase->status == Constants::PURCHASE_STATUSES['Signed 1/2']){
                $sign = AdobeSignService::getAgreementInfo($agreement->adobe_agreement_id);
                if(isset($sign['message'])){
                    $purchase['sign_url'] = 'javascript:;';
                }else{
                    $purchase['sign_url'] = $sign['signingUrlSetInfos'][0]['signingUrls'][0]['esignUrl'];
                }                
            }
        }

        // $payments = collect();
        // $viewContent = view('partials.forms.purchases.purchases-search', compact('purchases', 'payments', 'customer'))->render();
        $viewContent = view('partials.forms.purchases.purchases-search', compact('purchases', 'customer'))->render();

        return response()->json($viewContent);
    }

    public function create(Customer $customer): View
    {
        $companies = Company::select('id', 'name')->get();
        $locations = License::select(DB::raw('GROUP_CONCAT(studio_id) as ids'))->where('customer_id', $customer->id)->get();
        if(!empty($locations[0]['ids'])) {
            $studios = Studio::select('id', 'name')->whereIn('id', explode(',', (string)$locations[0]['ids']))->get();            
        }else{
            $studios = NULL;
        }
        $machines  = Machine::select('id', 'name')->whereNull('parent_machine_id')->orderBy('name', 'desc')->get();
        
        return view('admin.purchases.create', compact('customer', 'studios', 'machines', 'companies'));
    }

    public function store(Customer $customer, StorePurchaseRequest $request)
    {
        try {
            $response = $this->purchaseService->store($request->validated(), $customer);
            toastr()->addSuccess('', 'Purchase created successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'purchases']);
        } catch (\Exception $e) {
            toastr()->addError($e->getMessage(),'Purchase creation failed');
            return redirect()->back();
        }
    }

    public function edit(Customer $customer, Purchase $purchase): View
    {
        $companies                              = Company::select('id', 'name')->get();
        $studios                                = Studio::select('id', 'name')->get();
        $states                                 = State::select('id', 'name')->get();
        $machines                               = Machine::select('id', 'name')->whereNull('parent_machine_id')->get();
        $conf_machines                          = Machine::where('parent_machine_id', $purchase->machine_id)->get();
        $currentCurrency                        = AdminSettings::first()->currency;
        $notes                                  = $purchase->notes()->orderBy('created_at', 'desc')->get();
        $conversionRate                         = $purchase->conversionRate()->where('currency_id', $currentCurrency->id)->first();
        $purchase->converted_price                 = $purchase->machine_price / 100 / $conversionRate->rate;
        $purchase->converted_deposit               = ($purchase->deposit_amount) ? $purchase->deposit_amount / 100 / $conversionRate->rate : null;
        $purchase->converted_monthly_installment   = ($purchase->monthly_installment) ? $purchase->monthly_installment / 100 / $conversionRate->rate : null;
        $purchase->converted_buy_out               = ($purchase->buy_out) ? $purchase->buy_out / $conversionRate->rate : null;
        
        return view('admin.purchases.edit', compact('purchase', 'customer', 'studios', 'states', 'machines', 'companies', 'conf_machines'));
    }

    public function paymentLink(Customer $customer, Purchase $purchase)
    {
        $customer = Customer::where('id', $purchase->customer_id)->first();
        $purchase_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $purchase->id))));
        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'purchase', 'id' => $purchase_id])));

        toastr()->addSuccess('', 'Payment Link sent successfully.');
        return redirect()->back();
    }

    public function update(Customer $customer, Purchase $purchase, UpdatePurchaseRequest $request)
    {
        try {
            $this->purchaseService->update($request->validated(), $purchase, $customer);
            toastr()->addSuccess('', 'Purchase updated successfully.');
            return redirect()->route('admin.purchases.edit', ['customer' => $customer, 'purchase' => $purchase]);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Purchase update failed');
            return redirect()->back();
        }
    }

    public function destroy(Customer $customer, Purchase $purchase)
    {
        try {
            $this->purchaseService->delete($purchase);
            toastr()->addSuccess('', 'Purchase deleted successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            toastr()->addError('Purchase delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Customer $customer, Request $request)
    {
        try {
            $purchaseIds = $request->input('selectedItems');
            foreach ($purchaseIds as $purchaseId) {
                $this->purchaseService->delete(Purchase::find($purchaseId));
            }
            toastr()->addSuccess('', 'Selected purchases deleted successfully.');

            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Purchase delete failed');
            return redirect()->back();
        }
    }
    public function changeStatus(Customer $customer, Purchase $purchase, ChangePurchaseStatusRequest $request)
    {
        try {
            if ($request->get('status')) {                
                $purchase->update(['status' => $request->get('status')]);
                $purchase->payments()
                    ->where('purchase_id', $purchase->id)
                    ->update(['status' => Constants::PAYMENT_STATUS['declined']]);

                $purchase->update(['is_active' => $request->get('is_active')]);
                
                toastr()->addSuccess('', 'Purchase cancelled successfully.');
                return redirect()->back();
            }
            
            return redirect()->back();
        } catch (\Exception $e) {
            toastr()->addError('Purchase cancellation failed');
            return redirect()->back();
        }
    }
    public function markAsDelivered(Customer $customer, Purchase $purchase, ChangePurchaseStatusRequest $request)
    {
        try {
            if ($request->get('status')) {                
                $purchase->update(['status' => $request->get('status')]);
                $agreement = Agreement::where('purchase_id', $purchase->id)->first();
                $agreement->update(['status' => $request->get('status')]);
                
                toastr()->addSuccess('', 'Purchase marked as delivered');
                return redirect()->back();
            }
            
            return redirect()->back();
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Purchase marked as delivered failed');
            return redirect()->back();
        }
    }

}
