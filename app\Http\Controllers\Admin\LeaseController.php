<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Lease\StoreLeaseRequest;
use App\Http\Requests\Admin\Lease\UpdateLeaseRequest;
use App\Http\Requests\Admin\Lease\ChangeLeaseStatusRequest;
use App\Http\Requests\Admin\LeaseNote\AddLeaseNoteRequest;
use App\Http\Requests\Admin\LeaseFile\AddLeaseFileRequest;
use App\Http\Requests\Admin\Purchase\ChangePurchaseStatusRequest;
use App\Mail\PaymentLinkEmail;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Lease;
use App\Models\License;
use App\Models\Machine;
use App\Models\Payment;
use App\Models\State;
use App\Models\Studio;
use App\Models\LeaseNotes;
use App\Models\LeaseFiles;
use App\Services\Admin\AdobeSign\AdobeSignService;
use App\Services\Admin\Lease\ILeaseService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class LeaseController extends Controller
{
    private ILeaseService $leaseService;

    public function __construct(
        ILeaseService $leaseService,
    )
    {
        $this->leaseService = $leaseService;
    }

    public function index(Customer $customer): View
    {
        return view('admin.leases.index', compact('customer'));
    }

    public function show(Customer $customer, Lease $lease, Request $request): View
    {
        $tab             = $request->query('tab', 'profile');
        $currentCurrency = AdminSettings::first()->currency;
        $conversionRate  = $lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
        $leaseRemaining  = CurrencyConversionHelper::calculateLeasePaymentsSum($lease, $conversionRate);
        $leaseTotal      = CurrencyConversionHelper::calculateLeaseTotalPrice($lease, $conversionRate);
        $leaseMonthly    = CurrencyConversionHelper::calculateLeaseMonthlyPrice($lease, $conversionRate);
        $leaseDeposit    = CurrencyConversionHelper::calculateLeaseDeposit($lease, $conversionRate);

        $lease_notes = $lease->notes()->get();
        $lease_files_count = count($lease->files()->get());
        return view('admin.leases.show', compact('customer', 'lease', 'tab', 'leaseRemaining', 'leaseTotal', 'leaseMonthly', 'leaseDeposit', 'lease_notes', 'lease_files_count'));
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';

        $leases = $this->leaseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($leases as $lease) {
            $lease['sequential_id'] = $sequential_id++;
            $conversionRate           = $lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            $lease['remaining']       = CurrencyConversionHelper::calculateLeasePaymentsSum($lease, $conversionRate);
            $lease['conversion_rate'] = $conversionRate;
            $lease['expires']         = Carbon::parse($lease->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y');
            $lease['total']           = CurrencyConversionHelper::calculateLeaseTotalPrice($lease, $conversionRate);
            $lease['monthly']         = CurrencyConversionHelper::calculateLeaseMonthlyPrice($lease, $conversionRate);
            $lease['note_count'] = $lease->lease_notes_count;
            $agreement = Agreement::where('lease_id', $lease->id)->first();
            $lease['agreement'] = $agreement;
            $sign = NULL;
            if($lease->status == Constants::LEASE_STATUSES['Signed 1/2']){
                $sign = AdobeSignService::getAgreementInfo($agreement->adobe_agreement_id);
                if(isset($sign['message'])){
                    $lease['sign_url'] = 'javascript:;';
                }else{
                    $lease['sign_url'] = $sign['signingUrlSetInfos'][0]['signingUrls'][0]['esignUrl'];
                }                
            }
        }

        $viewContent = view('partials.forms.leases.leases-search', compact('leases', 'customer'))->render();

        return response()->json($viewContent);
    }

    public function create(Customer $customer): View
    {
        $companies = Company::select('id', 'name')->get();
        $locations = License::select(DB::raw('GROUP_CONCAT(studio_id) as ids'))->where('customer_id', $customer->id)->get();
        if(!empty($locations[0]['ids'])) {
            $studios = Studio::select('id', 'name')->whereIn('id', explode(',', (string)$locations[0]['ids']))->get();            
        }else{
            $studios = NULL;
        }
        $machines  = Machine::select('id', 'name')->where('id', '!=', '2')->whereNull('parent_machine_id')->orderBy('name', 'desc')->get();
        
        return view('admin.leases.create', compact('customer', 'studios', 'machines', 'companies'));
    }

    public function store(Customer $customer, StoreLeaseRequest $request)
    {
        try {
            $this->leaseService->store($request->validated(), $customer);
            toastr()->addSuccess('', 'Lease created successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'lease']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError($e->getMessage(),'Lease creation failed');
            return redirect()->back();
        }
    }

    public function edit(Customer $customer, Lease $lease): View
    {
        $companies                              = Company::select('id', 'name')->get();
        $studios                                = Studio::select('id', 'name')->get();
        $states                                 = State::select('id', 'name')->get();
        $machines                               = Machine::select('id', 'name')->where('id', '!=', '2')->whereNull('parent_machine_id')->get();
        $conf_machines                          = Machine::where('parent_machine_id', $lease->machine_id)->get();
        $currentCurrency                        = AdminSettings::first()->currency;
        $notes                                  = $lease->notes()->orderBy('created_at', 'desc')->get();
        $conversionRate                         = $lease->conversionRate()->where('currency_id', $currentCurrency->id)->first();
        $lease->converted_price                 = $lease->machine_price / 100 / $conversionRate->rate;
        $lease->converted_deposit               = ($lease->deposit_amount) ? $lease->deposit_amount / 100 / $conversionRate->rate : null;
        $lease->converted_monthly_installment   = ($lease->monthly_installment) ? $lease->monthly_installment / 100 / $conversionRate->rate : null;
        $lease->converted_buy_out               = ($lease->buy_out) ? $lease->buy_out / $conversionRate->rate : null;
        
        return view('admin.leases.edit', compact('lease', 'customer', 'studios', 'states', 'machines', 'companies', 'conf_machines'));
    }

    public function paymentLink(Customer $customer, Lease $lease)
    {
        $customer = Customer::where('id', $lease->customer_id)->first();
        $lease_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $lease->id))));
        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'lease', 'id' => $lease_id])));

        toastr()->addSuccess('', 'Payment Link sent successfully.');
        return redirect()->back();
    }

    public function update(Customer $customer, Lease $lease, UpdateLeaseRequest $request)
    {
        try {
            $this->leaseService->update($request->validated(), $lease, $customer);
            toastr()->addSuccess('', 'Lease updated successfully.');
            return redirect()->route('admin.leases.edit', ['customer' => $customer, 'lease' => $lease]);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Lease update failed');
            return redirect()->back();
        }
    }

    public function destroy(Customer $customer, Lease $lease)
    {
        try {
            $this->leaseService->delete($lease);
            toastr()->addSuccess('', 'Lease deleted successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            toastr()->addError('Lease delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Customer $customer, Request $request)
    {
        try {
            $leaseIds = $request->input('selectedItems');
            foreach ($leaseIds as $leaseId) {
                $this->leaseService->delete(Lease::find($leaseId));
            }
            toastr()->addSuccess('', 'Selected leases deleted successfully.');

            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            toastr()->addError('Lease delete failed');
            return redirect()->back();
        }
    }


    public function loadTabContent(Customer $customer, Lease $lease, Request $request)
    {
        $tab = $request->query('tab');

        switch ($tab) {
            case 'payment-history':
                return view('admin.leases.payment-history-index', compact('customer', 'lease'))->render();
            case 'upcoming-payments':
                return view('admin.leases.upcoming-payments-index', compact('customer', 'lease'))->render();
            case 'notes':
                $notes = $lease->notes()->orderBy('created_at', 'desc')->get();
                return view('admin.lease-notes.index', compact('notes', 'customer', 'lease'))->render();
            case 'files':
                $files = $lease->files()->orderBy('created_at', 'desc')->get(); // Fetch files
                return view('admin.lease-files.index', compact('files', 'customer', 'lease'))->render();
            default:
                return response()->json(['error' => 'Invalid tab'], 400);
        }
    }

    public function changeStatus(Customer $customer, Lease $lease, ChangeLeaseStatusRequest $request)
    {
        try {
            if ($request->get('status')) {                
                $lease->update(['status' => $request->get('status')]);
            }
            if (!$request->get('is_active')) {
                $lease->payments()
                    ->where('payment_date', '>', Carbon::now())
                    ->update(['status' => Constants::PAYMENT_STATUS['declined']]);

                $lease->update(['is_active' => $request->get('is_active')]);

                toastr()->addSuccess('', 'Lease cancelled successfully.');
                return redirect()->back();
            }

            return redirect()->back();
        } catch (\Exception $e) {
            toastr()->addError('Lease cancellation failed');
            return redirect()->back();
        }
    }

    // Notes 
    public function store_lease_notes(Lease $lease, Customer $customer, AddLeaseNoteRequest $request){
        try {
            // dd($Lease);
            $lease->notes()->create($request->validated());
            toastr()->addSuccess('', 'Lease note created successfully.');
            return redirect()->route('admin.leases.show', ['customer'=> $customer, 'lease' => $lease, 'tab' => 'notes']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Lease note creation failed');
            return redirect()->back();
        }
    }
    public function destroy_note(Lease $lease, Customer $customer, LeaseNotes $notes)
    {
        try {
            $notes->delete();
            toastr()->addSuccess('', 'Lease Note deleted successfully.');
            return redirect()->route('admin.leases.show', ['customer' => $customer, 'lease' => $lease, 'tab' => 'notes']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Lease Note delete failed');
            return redirect()->back();
        }
    }

    // Files
    public function store_lease_files(Lease $lease, Customer $customer, AddLeaseFileRequest $request)
    {
        try {
            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $uniqueName = pathinfo($originalName, PATHINFO_FILENAME) . '-' . \Illuminate\Support\Str::random(8) . '.' . $extension;
                $uploadPath = public_path('uploads');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }
                $file->move($uploadPath, $uniqueName);
                $lease->files()->create([
                    'file_path' => 'uploads/' . $uniqueName,
                    'original_name' => $originalName,
                ]);
            }

            toastr()->addSuccess('', 'Lease files uploaded successfully.');
            return redirect()->route('admin.leases.show', [
                'customer' => $customer,
                'lease' => $lease,
                'tab' => 'files'
            ]);
        } catch (\Exception $e) {
            \Log::error('File Upload Error: ' . $e->getMessage());
            dd($e->getMessage());
            toastr()->addError('Lease file upload failed');
            return redirect()->back();
        }
    }

    public function destroy_lease_file(Lease $lease, LeaseFiles $file)
    {
        try {
            $filePath = public_path($file->file_path);
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Remove from database
            $file->delete();
            toastr()->addSuccess('', 'File deleted successfully.');
        } catch (\Exception $e) {
            \Log::error('File Deletion Error: ' . $e->getMessage());
            toastr()->addError('File deletion failed.');
        }
        return redirect()->back();
    }
    public function markAsDelivered(Customer $customer, Lease $lease, ChangePurchaseStatusRequest $request)
    {
        try {
            if ($request->get('status')) {                
                $lease->update(['status' => $request->get('status')]);
                $lease->update(['starting_date' => Carbon::now()]);
                $lease->payments()
                    ->where('payment_date', '>', Carbon::now())
                    ->update(['status' => Constants::PAYMENT_STATUS['declined']]);
                $agreement = Agreement::where('lease_id', $lease->id)->first();
                $agreement->update(['status' => $request->get('status')]);
                
                toastr()->addSuccess('', 'Lease marked as delivered');
                return redirect()->back();
            }
            
            return redirect()->back();
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Lease marked as delivered failed');
            return redirect()->back();
        }
    }
}
