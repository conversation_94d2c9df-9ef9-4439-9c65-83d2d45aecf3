@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('New Location') }}</h3>
            <a href="{{ route('admin.customers.dashboard', $customer) }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.licenses.store', $customer) }}" id="studio-location-form">
            @csrf
            <h5 class="form-section-title first-title">{{ __('lagree company') }}</h5>
            @include('partials.forms.select', [
                'field_name' => 'company_id',
                'field_label' => null,
                'values' => $companies,
                'field' => 'company',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('company_id', null),
                'include_empty' => true,
            ])
            <div class="studio_info_wrap">
                <h5 class="form-section-title">{{ __('studio info') }}</h5>
                @include('partials.forms.checkbox', [
                    'field_name' => 'same_as_account',
                    'field_label' => 'Copy from account',
                    'wrap_class_name' => ' mb-5 same_as_account_toggle',
                ])

                @include('partials.forms.input', [
                    'field_name' => 'studio[name]',
                    'field_label' => 'LOCATION NAME *',
                    'field_type' => 'text',
                    'field_value' => old('studio[name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_first_name]',
                    'field_label' => 'FIRST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_first_name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_last_name]',
                    'field_label' => 'LAST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_last_name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[phone]',
                    'field_label' => 'PHONE # *',
                    'field_type' => 'text',
                    'field_value' => old('studio[phone]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[email]',
                    'field_label' => 'EMAIL ADDRESS *',
                    'field_type' => 'email',
                    'field_value' => old('studio[email]', null),
                    'field_class' => 'studio-input',
                ])

                <h5 class="form-section-title">{{ __('address info') }}</h5>
                @include('partials.forms.radio-group', [
                    'field_name' => 'location',
                    'field_class' => 'mb-7',
                    'field_label' => 'Radio group location',
                    'values' => [
                        'location_usa' => ['text' => 'USA', 'value' => 'USA'],
                        'location_international' => ['text' => 'International', 'value' => 'International'],
                    ],
                    'field' => 'status',
                    'field_value' => old('location', null),
                    'required' => 'required',
                    'checked' => 'USA',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[address]',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => old('studio[address]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[address2]',
                    'field_label' => 'ADDRESS 2',
                    'field_type' => 'text',
                    'field_value' => old('studio[address2]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[city]',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => old('studio[city]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[zip]',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => old('studio[zip]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.select', [
                    'field_name' => 'studio[state_id]',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[state_id]', null),
                    'include_empty' => true,
                ])
                @include('partials.forms.select', [
                    'field_name' => 'studio[country_id]',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'countries',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[country_id]', null),
                    'include_empty' => true,
                ])
            </div>

            <h5 class="fs-14px mt-50 mb-45 pt-45 form-section-title custom-fw-500 text-uppercase border-top">
                {{ __('license Info') }}</h5>
            @include('partials.forms.input', [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', null),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field',
            ])
            @include('partials.forms.select', [
                'field_name' => 'package',
                'field_label' => 'PACKAGE TYPE *',
                'placeholder' => 'Select',
                'values' => $packages,
                'field' => 'package',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('package', null),
                'field_class' => 'packages',
                'include_empty' => true,
            ])
            @include('partials.forms.input', [
                'field_name' => 'price',
                'field_type' => 'hidden',
                'field_value' => 0,
            ])
            @include('partials.forms.input', [
                'field_name' => 'duration',
                'field_type' => 'hidden',
                'field_value' => 1,
            ])
            @include('partials.forms.input', [
                'field_name' => 'type',
                'field_type' => 'hidden',
                'field_value' => 'license',
            ])
            @include('partials.forms.input', [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d'),
            ])
            <div class="deposit-wrap">
                @include('partials.forms.input', [
                    'field_name' => 'deposit_amount',
                    'field_label' => 'DEPOSIT AMOUNT *',
                    'field_type' => 'text',
                    'currency' => true,
                    'field_value' => old('deposit_amount', 0),
                    'input_field_class' => 'decimal-field',
                ])
                <p class="f-10" style="display: none; color: red; position: relative; top: -20px">Deposit is not available
                    for international locations</p>
            </div>
            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', null),
            ])
            <div class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{ route('admin.licenses.create', $customer) }}" class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <div class="ms-auto d-flex align-items-center gap-3">
                    <span class="agreements-err"></span>
                    <a type="button" href="javascript:;" class="btn cancel-btn preview_agreement" style="margin-left: auto !important">{{ __('PREVIEW AGREEMENT') }}</a>
                </div>
            </div>
        </form>
    </div>
    <div class="agreement_popup_overlay" style="display: none;" onclick="$('.agreement_popup_overlay').fadeOut();">
        <div class="agreement_popup">
            <span class="close-popup" onclick="$('.agreement_popup_overlay').fadeOut();">×</span>
            <div class="agreement-html"></div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="module">
        const package_prices = @json(\App\Helpers\Constants::LICENSE_PACKAGES_PRICES);
        document.addEventListener('DOMContentLoaded', function() {
            function toggleLocationInputs() {
                const isUSA = document.getElementById('location_usa').checked;
                const stateSelect = document.querySelector("[name='studio[state_id]']");
                const countrySelect = document.querySelector("[name='studio[country_id]']");
                const price = package_prices[$('#package-package').val()];

                if (isUSA) {
                    stateSelect.closest('.form-group').style.display = 'block';
                    countrySelect.closest('.form-group').style.display = 'none';
                    if ($('#package-package').val() != 0) {
                        $('#deposit_amount').val(490);
                    } else {
                        $('#deposit_amount').val(0);
                    }
                } else {
                    stateSelect.closest('.form-group').style.display = 'none';
                    countrySelect.closest('.form-group').style.display = 'block';
                    if ($('#package-package').val() != 0) {
                        $('#deposit_amount').val(price);
                    } else {
                        $('#deposit_amount').val(0);
                    }
                }
            }
            document.getElementById('location_usa').addEventListener('change', toggleLocationInputs);
            document.getElementById('location_international').addEventListener('change', toggleLocationInputs);
            let inputField = document.querySelector('.is-invalid');
            if (inputField) {
                inputField.focus();
            }

            $('form').on('submit', function() {
                $('input[name="location"]').prop('disabled', false);
            });

            $('#package-package').change(function() {
                var val = $(this).val();
                console.log('val: ', val);
                $('#price').val(package_prices[val] || 0);
                toggleLocationInputs();
            });

            $('.same_as_account_toggle input').on('change', function() {
                if ($(this).is(':checked')) {
                    console.log('Checked');
                    setTimeout(function() {
                        $.ajaxSetup({
                            headers: {
                                'X-CSRF-TOKEN': "{{ csrf_token() }}"
                            }
                        });
                        $.ajax({
                            type: 'POST',
                            url: '{{ route('admin.customers.getCustomer') }}',
                            data: {
                                customer_id: {{ $customer->id }}
                            },
                            dataType: 'json',
                            success: function(data) {
                                console.log('customer:', data.customer);
                                console.log('customer owner name:', data.customer.owner
                                    .first_name);
                                if (data.success) {
                                    console.log('Success');
                                    $('[id="studio[name]"]').val(data.customer.name);
                                    $('[id="studio[owner_first_name]"]').val(data
                                        .customer.owner.first_name);
                                    $('[id="studio[owner_last_name]"]').val(data
                                        .customer.owner.last_name);
                                    $('[id="studio[phone]"]').val(data.customer.owner
                                        .phone);
                                    $('[id="studio[email]"]').val(data.customer.owner
                                        .email);
                                    if (data.customer.location == 'USA') {
                                        $('#location_usa').prop('checked', true)
                                            .trigger('change');
                                    } else {
                                        $('#location_international').prop('checked',
                                            true).trigger('change');
                                    }
                                    $('[id="studio[address]"]').val(data.customer
                                        .address);
                                    $('[id="studio[city]"]').val(data.customer.city);
                                    $('[id="studio[zip]"]').val(data.customer.zip);
                                    $('[id="studio[state_id]-state"]').val(data.customer.state_id).change();
                                    $('[id="studio[country_id]-countries"]').val(data.customer.country_id).change();
                                    toggleLocationInputs();
                                }
                            },
                            error: function(request, status, error) {
                                console.log('PHP Error');
                            }
                        });
                    }, 100);
                } else {
                    $('[id="studio[name]"]').val("");
                    $('[id="studio[owner_first_name]"]').val("");
                    $('[id="studio[owner_last_name]"]').val("");
                    $('[id="studio[phone]"]').val("");
                    $('[id="studio[email]"]').val("");
                    $('#location_usa').prop('checked', true).trigger('change');
                    $('[id="studio[address]"]').val("");
                    $('[id="studio[city]"]').val("");
                    $('[id="studio[zip]"]').val("");
                    $('[id="studio[state_id]-state"]').val("").change();
                    $('[id="studio[country_id]-countries"]').val("").change();
                    toggleLocationInputs();
                }
            });
            $('.preview_agreement').on('click', function() {
                $('.agreements-err').text('').hide();
                var tempName = "";
                var file = "";
                if($('[name=location]:checked').val() == 'USA'){
                    if($('#package-package').val() > 0){
                        if($('#package-package').val() == 1){
                            tempName = 'IMS-MICRO-FINAL';
                            file = 'license-micro';
                        }else if($('#package-package').val() == 2){
                            tempName = 'IMS-MINI-FINAL';
                            file = 'license-mini';
                        }else if($('#package-package').val() == 3 || $('#package-package').val() == 4){
                            tempName = 'IMS-MEGA-FINAL';
                            file = 'license-mega';
                        }
                    }else{
                        $('.agreements-err').text('Please select a package').show();
                        return false;
                    }
                }else{
                    if($('#package-package').val() > 0){
                        if($('#package-package').val() == 1){
                            tempName = 'IMS-INT-MICRO-FINAL';
                            file = 'license-int-micro';
                        }else if($('#package-package').val() == 2){
                            tempName = 'IMS-INT-MINI-FINAL';
                            file = 'license-int-mini';
                        }else if($('#package-package').val() == 3 || $('#package-package').val() == 4){
                            tempName = 'IMS-INT-MEGA-FINAL';
                            file = 'license-int-mega';
                        }
                    }else{
                        $('.agreements-err').text('Please select a package').show();
                        return false;
                    }
                }

                var company_name = $('#company_id-company option:selected').text().trim();
                var address = $('[id="studio[address]"]').val();
                var city = $('[id="studio[city]"]').val();
                var zip = $('[id="studio[zip]"]').val();
                var country = $('[id="studio[country_id]-countries"] option:selected').text().trim();
                var state = $('[id="studio[state_id]-state"] option:selected').text().trim();
                var licenseFee = $('#price').val();
                var deposit = $('#deposit_amount').val() != 0 ? $('#deposit_amount').val() : 0;
                var left_to_pay = $('#price').val() - $('#deposit_amount').val();

                // Enhanced debugging with length and type info
                console.log('company_name: "' + company_name + '" (length: ' + company_name.length + ', trimmed: "' + company_name.trim() + '")');
                console.log('address: "' + address + '" (length: ' + address.length + ', trimmed: "' + address.trim() + '")');
                console.log('city: "' + city + '" (length: ' + city.length + ', trimmed: "' + city.trim() + '")');
                console.log('zip: "' + zip + '" (length: ' + zip.length + ', type: ' + typeof zip + ')');
                console.log('country: "' + country + '" (length: ' + country.length + ', trimmed: "' + country.trim() + '")');
                console.log('state: "' + state + '" (length: ' + state.length + ', trimmed: "' + state.trim() + '")');
                console.log('licenseFee: "' + licenseFee + '" (type: ' + typeof licenseFee + ')');
                console.log('deposit: "' + deposit + '" (type: ' + typeof deposit + ')');
                console.log('left_to_pay: "' + left_to_pay + '" (type: ' + typeof left_to_pay + ')');
                console.log('tempName: ', tempName);
                console.log('file: ', file);

                // Helper function to check if value is valid
                function isValidValue(value) {
                    return value !== null && value !== undefined && value !== '' &&
                           String(value).trim() !== '' && !String(value).includes('Select');
                }

                // Individual condition checks
                var conditions = {
                    company_name: isValidValue(company_name),
                    address: isValidValue(address),
                    city: isValidValue(city),
                    zip: isValidValue(zip),
                    country_or_state: (isValidValue(country) || isValidValue(state)),
                    licenseFee: isValidValue(licenseFee) && parseFloat(licenseFee) > 0,
                    left_to_pay: isValidValue(left_to_pay) && parseFloat(left_to_pay) >= 0
                };

                console.log('Individual conditions:', conditions);

                var result = conditions.company_name && conditions.address && conditions.city &&
                           conditions.zip && conditions.country_or_state && conditions.licenseFee &&
                           conditions.left_to_pay;

                console.log('FINAL RESULT: ', result);

                // Additional debugging for common issues
                if (!result) {
                    console.log('=== DEBUGGING FALSE RESULT ===');
                    if (!conditions.company_name) {
                        $('.agreements-err').text('Company name is empty or invalid').show();
                        return false;
                    }
                    if (!conditions.address) {
                        $('.agreements-err').text('Address is empty or invalid').show();
                        return false;
                    }
                    if (!conditions.city) {
                        $('.agreements-err').text('City is empty or invalid').show();
                        return false;
                    }
                    if (!conditions.zip) {
                        $('.agreements-err').text('ZIP is empty or invalid').show();
                        return false;
                    }
                    if (!conditions.country_or_state) {
                        $('.agreements-err').text('Both country and state are empty').show();
                        return false;
                    }
                    if (!conditions.licenseFee) {
                        $('.agreements-err').text('License fee is empty or invalid. Please select package.').show();
                        return false;
                    }
                    if (!conditions.left_to_pay) {
                        $('.agreements-err').text('Please select package').show();
                        return false;
                    }

                    // if (!conditions.company_name) console.log('❌ Company name is empty or invalid');
                    // if (!conditions.address) console.log('❌ Address is empty or invalid');
                    // if (!conditions.city) console.log('❌ City is empty or invalid');
                    // if (!conditions.zip) console.log('❌ ZIP is empty or invalid');
                    // if (!conditions.country_or_state) console.log('❌ Both country and state are empty');
                    // if (!conditions.licenseFee) console.log('❌ License fee is empty or invalid');
                    // if (!conditions.left_to_pay) console.log('❌ Left to pay is empty or invalid');

                    // Check for specific common issues
                    if (licenseFee === '0' || licenseFee === 0) console.log('⚠️ License fee is zero');
                    if (left_to_pay === '0' || left_to_pay === 0) console.log('⚠️ Left to pay is zero');
                    if (country.includes('Select') || state.includes('Select')) console.log('⚠️ Default select option still selected');
                }

                if(result){
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': "{{ csrf_token() }}"
                        }
                    });
                    $.ajax({
                        type: 'POST',
                        url: '{{ route('admin.agreements.preview-agreement') }}',
                        data: {
                            customer_id: {{ $customer->id }},
                            company_name,
                            address,
                            city,
                            zip,
                            country,
                            state,
                            licenseFee,
                            deposit,
                            left_to_pay,
                            templateNames: tempName,
                            type: 'license',
                            file: file,
                        },
                        dataType: 'json',
                        success: function(data) {
                            console.log(data);
                            console.log('SUCCESS');
                            if (data.success) {
                                $('.agreement_popup_overlay').fadeIn();
                                var rep1 = (data.html).replace("@{{Sig_es_:signer1:signature}}", "  ").replace("@{{Dte_es_:signer1:date}}", "  ").replace("@{{Sig_es_:signer2:signature}}", "  ").replace("@{{Dte_es_:signer2:date}}", "  ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ").replace("@{{Init_es_:signer1:initials}}", " INITIALS ");
                                $('.agreement-html').html(rep1);
                            }
                        },
                        error: function(request, status, error) {
                            console.log('PHP Error');
                        }
                    });
                }else{
                    $('.agreements-err').text('Preview can not generate unless all fields are valid.').show();
                }
            });
            toggleLocationInputs();

            // Initialize Google Places Autocomplete
            window.initGooglePlaces = function() {
                const autocompleteService = new GooglePlacesAutocomplete();

                const fieldConfigs = [
                    {
                        inputId: 'studio[address]',
                        fields: {
                            address: 'studio[address]',
                            city: 'studio[city]',
                            zip: 'studio[zip]',
                            state: 'studio[state_id]',
                            country: 'studio[country_id]'
                        },
                        locationRadios: {
                            usa: 'location_usa',
                            international: 'location_international'
                        }
                    }
                ];

                autocompleteService.init(fieldConfigs);
            };

            // Load Google Maps API
            const googleApiKey = '{{ config("services.google_maps.api_key") }}';
            if (googleApiKey && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
                GooglePlacesAutocomplete.loadGoogleMapsAPI(googleApiKey, window.initGooglePlaces);
            }
        });
        $('.agreement_popup').on('click', function(e){
            e.stopPropagation();
        });
    </script>

    <!-- Google Places Autocomplete Script -->
    @vite('resources/js/google-places-autocomplete.js')
@endpush
