<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\LicenseNotes;
use App\Models\LicenseFiles;

class License extends Model
{
    use HasFactory, SoftDeletes;

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    protected $fillable = [
        'company_id',
        'type',
        'location',
        'studio_id',
        'customer_id',
        'price',
        'duration',
        'starting_date',
        'deposit_amount',
        'deposit_date',
        'is_active',
        'status',
        'package',
        'initial_currency_id',
        'stripe_session_id'
    ];

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'studio'   => ['id', 'licenses.studio_id'],
    ];

    public function getPrimaryAndForeignKeys(string $relation): array
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function sortableFields(): array
    {
        return [
            'id',
            'type',
            'price',
            'package',
            'is_active',
            'status',
            'studio.name',
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }
    public function notes(): HasMany
    {
        return $this->hasMany(LicenseNotes::class);
    }

    public function files()
    {
        return $this->hasMany(LicenseFiles::class);
    }

    public function licenseNotes(): HasMany
    {
        return $this->hasMany(LicenseNotes::class, 'license_id', 'id');
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function studio(): BelongsTo
    {
        return $this->belongsTo(Studio::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function conversionRate(): MorphMany
    {
        return $this->morphMany(ConversionRate::class, 'rateable');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function initialCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    public function agreement(): HasMany
    {
        return $this->hasMany(Agreement::class);
    }
}
