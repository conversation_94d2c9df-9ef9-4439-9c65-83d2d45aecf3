<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\Notifiable;

class Agreement extends Model
{
    use Notifiable;

    const LICENSE_TYPE = 'license';
    const LEASE_TYPE = 'lease';
    const PURCHASE_TYPE = 'purchase';

    const LEASE_TEMPLATE_NAMES = ['IMS-LEASE-FINAL'];
    
    const LICENSE_TEMPLATE_MINI = ['IMS-MINI-FINAL'];
    const LICENSE_TEMPLATE_MICRO = ['IMS-MICRO-FINAL'];
    const LICENSE_TEMPLATE_MEGA = ['IMS-MEGA-FINAL'];

    const LICENSE_TEMPLATE_MINI_INT = ['IMS-INT-MINI-FINAL'];
    const LICENSE_TEMPLATE_MICRO_INT = ['IMS-INT-MICRO-FINAL'];
    const LICENSE_TEMPLATE_MEGA_INT = ['IMS-INT-MEGA-FINAL'];
    
    const PURCHASE_TEMPLATE = ['IMS-PURCHASE-FINAL'];

    const STATUSES = [
        'Sent (0/2)' => 0,
        'Signed 2/2 (wait4pay)' => 1,
        'Awaiting delivery (paid)' => 2,
        'Active' => 3,
        'Uploaded' => 4,
        'Inactive' => 5,
        'Signed 1/2' => 6,
        'Canceled' => 7,
        'Deposit Paid' => 8,
        'Processing' => 9,
        'Payment failed' => 10,
        'Payment requires action' => 11,
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    // protected $fillable = [
    //     'adobe_template_id',
    //     'customer_id',
    //     'adobe_agreement_id',
    //     'send_date',
    //     'singing_date',
    //     'completed_date',
    //     'filepath',
    //     'status'
    // ];

    protected $fillable = [
        'adobe_template_id',
        'customer_id',
        'lease_id',
        'license_id',
        'purchase_id',
        'adobe_agreement_id',
        'send_date',
        'singing_date',
        'completed_date',
        'filepath',
        'status',
        'type',
        'file_name',
        'original_name',
        'location',
        'custom',
    ];

    /**
     * Get the customer associated with the agreement.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the agreement template associated with the company.
     */
    public function agreementTemplate(): BelongsTo
    {
        return $this->belongsTo(AgreementTemplate::class);
    }

    public function getFormattedNumberAttribute(): string
    {
        return str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }

    public function license(): BelongsTo
    {
        return $this->belongsTo(License::class);
    }

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class);
    }

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * Get the agreement type based on which foreign key is set
     */
    public function getAgreementTypeAttribute(): string
    {
        if ($this->license_id) {
            return 'license';
        } elseif ($this->lease_id) {
            return 'lease';
        } elseif ($this->purchase_id) {
            return 'purchase';
        } else {
            return $this->type ?? 'custom';
        }
    }

    /**
     * Get the agreement name based on the type
     */
    public function getAgreementNameAttribute(): string
    {
        if ($this->license_id && $this->license) {
            if (isset($this->license->package) && $this->license->package != null && (int)$this->license->package > 0) {
                return \App\Helpers\Constants::LICENSE_PACKAGES_KEYS[$this->license->package] ?? 'License';
            } else {
                return $this->license->type == \App\Helpers\Constants::LICENSE_TYPES[0] ? 'License' : 'Exclusivity';
            }
        } elseif ($this->lease_id && $this->lease) {
            if ($this->lease->configuration_id && $this->lease->configuration) {
                return $this->lease->configuration->name ?? 'Lease';
            } elseif ($this->lease->machine_id && $this->lease->machine) {
                return $this->lease->machine->name ?? 'Lease';
            }
            return 'Lease';
        } elseif ($this->purchase_id && $this->purchase) {
            if ($this->purchase->configuration_id && $this->purchase->configuration) {
                return $this->purchase->configuration->name ?? 'Purchase';
            } elseif ($this->purchase->machine_id && $this->purchase->machine) {
                return $this->purchase->machine->name ?? 'Purchase';
            }
            return 'Purchase';
        } else {
            return 'Custom Agreement';
        }
    }

    /**
     * Get the studio/location name based on the agreement type
     */
    public function getLocationNameAttribute(): ?string
    {
        if ($this->license_id && $this->license && $this->license->studio) {
            return $this->license->studio->name;
        } elseif ($this->lease_id && $this->lease && $this->lease->studio) {
            return $this->lease->studio->name;
        } elseif ($this->purchase_id && $this->purchase && $this->purchase->studio) {
            return $this->purchase->studio->name;
        }
        return null;
    }

    /**
     * Get the customer email through the owner relationship
     */
    public function getCustomerEmailAttribute(): ?string
    {
        return $this->customer && $this->customer->owner ? $this->customer->owner->email : null;
    }

}
