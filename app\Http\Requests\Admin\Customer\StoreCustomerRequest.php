<?php

namespace App\Http\Requests\Admin\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use App\Rules\UniqueCustomerEmailPerType;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.'max:255'
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'is_active'             => ['required', 'boolean'],
            'location'              => ['required', 'string', Rule::in(['USA', 'International'])],
            'customer_name'         => ['nullable', 'string', 'max:255'],
            'licensee2_first_name'  => ['nullable', 'string', 'max:255'],
            'licensee2_last_name'   => ['nullable', 'string', 'max:255'],
            'address'               => ['nullable', 'string', 'max:255'],
            'address2'              => ['nullable', 'string', 'max:255'],
            'city'                  => ['nullable', 'string', 'max:255'],
            'zip'                   => ['nullable', 'string', 'max:255'],
            'state_id'              => ['nullable', 'integer', 'exists:states,id'],
            'country_id'            => ['nullable', 'integer', 'exists:countries,id'],
            'use_billing_address'   => ['nullable', 'string', 'max:255'],
            'shipping_address'      => ['nullable', 'string', 'max:255'],
            'shipping_city'         => ['nullable', 'string', 'max:255'],
            'shipping_zip'          => ['nullable', 'string', 'max:255'],
            'shipping_state_id'     => ['nullable', 'integer', 'exists:states,id'],
            'shipping_country_id'   => ['nullable', 'integer', 'exists:countries,id'],
            'shipping_location'     => ['nullable', 'string', 'max:255'],
            'owner_first_name'      => ['required', 'string', 'max:255'],
            'owner_last_name'       => ['required', 'string', 'max:255'],
            'phone'                 => ['nullable', 'string', 'max:255'],
            'type'                  => ['required', 'string', 'max:255'],
            'email'                 => [
                                        'required',
                                        'string',
                                        'email',
                                        'max:255',
                                        new UniqueCustomerEmailPerType($this->input('owner_id'), $this->input('type')),
                                        ],
            'email2'                => ['nullable', 'string', 'email', 'max:255', new UniqueCustomerEmailPerType($this->input('owner_id'), $this->input('type')),],
            'password'              => ['nullable', Password::min(8)->letters()->mixedCase()->numbers(), 'confirmed'],
        ];
    }

    // protected function failedValidation(Validator $validator)
    // {
    //     throw new HttpResponseException(response()->json($validator->errors(), 422));
    // }    
}
