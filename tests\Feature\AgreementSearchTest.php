<?php

namespace Tests\Feature;

use App\Models\Agreement;
use App\Models\Countries;
use App\Models\Customer;
use App\Models\License;
use App\Models\Lease;
use App\Models\Purchase;
use App\Models\Studio;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AgreementSearchTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user with admin role
        $user = User::factory()->create();
        $this->actingAs($user);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_name()
    {
        // Create test data
        $customer1 = Customer::factory()->create(['name' => '<PERSON>']);
        $customer2 = Customer::factory()->create(['name' => '<PERSON>']);
        
        $agreement1 = Agreement::factory()->create(['customer_id' => $customer1->id]);
        $agreement2 = Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "John"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'John',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should contain John Doe but not Jane Smith
        $this->assertStringContainsString('John Doe', $content);
        $this->assertStringNotContainsString('Jane Smith', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_email()
    {
        // Create test data
        $owner1 = User::factory()->create(['email' => '<EMAIL>']);
        $owner2 = User::factory()->create(['email' => '<EMAIL>']);
        
        $customer1 = Customer::factory()->create(['owner_id' => $owner1->id]);
        $customer2 = Customer::factory()->create(['owner_id' => $owner2->id]);
        
        $agreement1 = Agreement::factory()->create(['customer_id' => $customer1->id]);
        $agreement2 = Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "<EMAIL>"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => '<EMAIL>',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // <NAME_EMAIL> <NAME_EMAIL>
        $this->assertStringContainsString('<EMAIL>', $content);
        $this->assertStringNotContainsString('<EMAIL>', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_agreement_type()
    {
        // Create test data
        $customer = Customer::factory()->create();
        $studio = Studio::factory()->create();
        
        $license = License::factory()->create(['studio_id' => $studio->id]);
        $lease = Lease::factory()->create(['studio_id' => $studio->id]);
        
        $licenseAgreement = Agreement::factory()->create([
            'customer_id' => $customer->id,
            'license_id' => $license->id,
            'type' => 'license'
        ]);
        
        $leaseAgreement = Agreement::factory()->create([
            'customer_id' => $customer->id,
            'lease_id' => $lease->id,
            'type' => 'lease'
        ]);

        // Search for "license"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'license',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should find the license agreement
        $this->assertStringContainsString('License', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_studio_location()
    {
        // Create test data
        $customer = Customer::factory()->create();
        $studio1 = Studio::factory()->create(['name' => 'Downtown Studio']);
        $studio2 = Studio::factory()->create(['name' => 'Uptown Studio']);
        
        $license1 = License::factory()->create(['studio_id' => $studio1->id]);
        $license2 = License::factory()->create(['studio_id' => $studio2->id]);
        
        $agreement1 = Agreement::factory()->create([
            'customer_id' => $customer->id,
            'license_id' => $license1->id,
            'type' => 'license'
        ]);
        
        $agreement2 = Agreement::factory()->create([
            'customer_id' => $customer->id,
            'license_id' => $license2->id,
            'type' => 'license'
        ]);

        // Search for "Downtown"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'Downtown',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();
        
        // Should contain Downtown Studio but not Uptown Studio
        $this->assertStringContainsString('Downtown Studio', $content);
        $this->assertStringNotContainsString('Uptown Studio', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_owner_first_name()
    {
        // Create test data
        $owner1 = User::factory()->create(['first_name' => 'John', 'last_name' => 'Doe']);
        $owner2 = User::factory()->create(['first_name' => 'Jane', 'last_name' => 'Smith']);

        $customer1 = Customer::factory()->create(['owner_id' => $owner1->id]);
        $customer2 = Customer::factory()->create(['owner_id' => $owner2->id]);

        Agreement::factory()->create(['customer_id' => $customer1->id]);
        Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "John"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'John',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should contain John but not Jane
        $this->assertStringContainsString('John', $content);
        $this->assertStringNotContainsString('Jane', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_owner_last_name()
    {
        // Create test data
        $owner1 = User::factory()->create(['first_name' => 'John', 'last_name' => 'Doe']);
        $owner2 = User::factory()->create(['first_name' => 'Jane', 'last_name' => 'Smith']);

        $customer1 = Customer::factory()->create(['owner_id' => $owner1->id]);
        $customer2 = Customer::factory()->create(['owner_id' => $owner2->id]);

        Agreement::factory()->create(['customer_id' => $customer1->id]);
        Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "Doe"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'Doe',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should contain Doe but not Smith
        $this->assertStringContainsString('Doe', $content);
        $this->assertStringNotContainsString('Smith', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_address()
    {
        // Create test data
        $customer1 = Customer::factory()->create(['address' => '123 Main Street']);
        $customer2 = Customer::factory()->create(['address' => '456 Oak Avenue']);

        Agreement::factory()->create(['customer_id' => $customer1->id]);
        Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "Main"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'Main',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should contain Main Street but not Oak Avenue
        $this->assertStringContainsString('Main Street', $content);
        $this->assertStringNotContainsString('Oak Avenue', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_city()
    {
        // Create test data
        $customer1 = Customer::factory()->create(['city' => 'New York']);
        $customer2 = Customer::factory()->create(['city' => 'Los Angeles']);

        Agreement::factory()->create(['customer_id' => $customer1->id]);
        Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "York"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'York',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should contain New York but not Los Angeles
        $this->assertStringContainsString('New York', $content);
        $this->assertStringNotContainsString('Los Angeles', $content);
    }

    /** @test */
    public function it_can_search_agreements_by_customer_country()
    {
        // Create test data
        $country1 = Countries::factory()->create(['name' => 'United States']);
        $country2 = Countries::factory()->create(['name' => 'Canada']);

        $customer1 = Customer::factory()->create(['country_id' => $country1->id]);
        $customer2 = Customer::factory()->create(['country_id' => $country2->id]);

        Agreement::factory()->create(['customer_id' => $customer1->id]);
        Agreement::factory()->create(['customer_id' => $customer2->id]);

        // Search for "United"
        $response = $this->get(route('admin.agreements.searchAll', [
            'search_data' => 'United',
            'order_param' => 'id',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        $content = $response->getContent();

        // Should contain United States but not Canada
        $this->assertStringContainsString('United States', $content);
        $this->assertStringNotContainsString('Canada', $content);
    }
}
