// Bootstrap
import 'bootstrap';

// Bootstrap datepicker
import 'bootstrap-datepicker';
import 'bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css';

// Axios
import './bootstrap';

import $ from 'jquery';
window.$ = $;
window.jQuery = $;

import flatpickr from "flatpickr";
import "flatpickr/dist/flatpickr.min.css";
flatpickr(".timepicker", {
    enableTime: true,
    noCalendar: true,
    dateFormat: "H:i",
});

// Select2
import select2 from 'select2';
select2($);

// DataTables
import DataTable from 'datatables.net-bs5';

window.DataTable = DataTable;

import 'datatables.net-bs5/css/dataTables.bootstrap5.min.css';

import 'datatables.net-rowreorder-bs5';
import 'datatables.net-rowreorder-bs5/css/rowReorder.bootstrap5.min.css';

// Font Awesome
import '@fortawesome/fontawesome-free/scss/fontawesome.scss';
import '@fortawesome/fontawesome-free/scss/brands.scss';
import '@fortawesome/fontawesome-free/scss/regular.scss';
import '@fortawesome/fontawesome-free/scss/solid.scss';
import '@fortawesome/fontawesome-free/scss/v4-shims.scss';

// Fonts
import.meta.glob([
    '../css/fonts/**',
]);
document.addEventListener("DOMContentLoaded", async () => {
    const { default: Inputmask } = await import("inputmask");

    // Select all input fields inside elements with the class 'currency-input'
    const inputElements = document.querySelectorAll('.currency-input input');
    inputElements.forEach(inputElement => {
        if (inputElement) {
            Inputmask({
                alias: "numeric",
                groupSeparator: ",",
                autoGroup: true,
                digits: 2,
                digitsOptional: false,
                prefix: "$", // Adding prefix to display in the input field
                placeholder: "0",
                showMaskOnHover: false
            }).mask(inputElement);
        }
    });
});

$('document').ready(function () {
    sidebarStateHandler()
});

$('.date-selector').each(function(){
    $(this).datepicker({
        format: 'DD, M d yyyy',
        container: $(this).parent(),
        orientation: 'bottom left'
    }).on('changeDate', function(e){
        let selectedDate = new Date(e.date);

        let year = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(selectedDate);
        let month = new Intl.DateTimeFormat('en', { month: '2-digit' }).format(selectedDate);
        let day = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(selectedDate);

        var queryParams = new URLSearchParams(window.location.search);
        queryParams.set("date", `${year}-${month}-${day}`);

        location.href = location.protocol + '//' + location.host + location.pathname + "?" + queryParams.toString();
    });
});


$('.date-picker').each(function(){
    console.log(weekStart)
    $(this).datepicker({
        format: 'yyyy-mm-dd',
        container: $(this).parent(),
        orientation: 'bottom left',
        startDate: new Date(),
        weekStart: weekStart
    }).on('changeDate', function(e){
        $('.datepicker-dropdown').remove();
    });
});

$('.date-picker-history').each(function(){
    console.log(weekStart)
    $(this).datepicker({
        format: 'yyyy-mm-dd',
        container: $(this).parent(),
        orientation: 'bottom left',
        startDate: null,
        weekStart: weekStart
    }).on('changeDate', function(e){
        $('.datepicker-dropdown').remove();
    });
});

//Store sidebar state in local storage, check on load if it should be open/closed.
function sidebarStateHandler() {
    if (!localStorage.getItem('sidebar-open')) {
        localStorage.setItem('sidebar-open', true);
    }

    var state = JSON.parse(localStorage.getItem('sidebar-open'));

    if (state == true) {
        $('#dashboard-side').addClass('show');
        $('#dashboard-icons').removeClass('show');

    } else if (state == false) {
        $('#dashboard-side').removeClass('show');
        $('#dashboard-icons').addClass('show');
    }

    $('.hamburger').on('click', function (e) {
        var state = JSON.parse(localStorage.getItem('sidebar-open'));

        localStorage.setItem('sidebar-open', !state);
    });
}

// Toggle password eye
// TODO refactor
function togglePasswordEye() {
    let passwordEye = document.getElementById("password");
    if (passwordEye.type === "password") {
        passwordEye.type = "text";
    } else {
        passwordEye.type = "password";
    }
}

// Function for mark as read all notifications for agreements
function sendMarkRequest(id = null) {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    return $.ajax({
        url: 'mark-as-read',
        method: 'POST',
        data: {id}
    })
}
$(function () {
    $('.mark-as-read').click(function () {
        let request = sendMarkRequest($(this).data('id'));
        request.done(() => {
            $(this).parents('li.alert').remove();
            location.reload();
        });
    });
});
window.togglePasswordEye = togglePasswordEye;

import flasher from "@flasher/flasher";
window.flasherJS = flasher;


// Select2 init
if ($.isFunction($.fn.select2)) {
    $('select').each(function(i, el){
        if($(el).parent().hasClass('zIndex-10000')){
            $(el).parent().addClass('position-relative');

            $(el).select2({
                minimumResultsForSearch: 8,
                placeholder: "Select",
                dropdownParent: $(el).parent(),  
                closeOnSelect: true, // Ensure the dropdown closes after selection
            });
            setTimeout(function(){
                $('.select2-selection__rendered').removeAttr('title');
            }, 200);
        }else{
            $(el).select2({
                minimumResultsForSearch: 8,
                placeholder: "Select",
                closeOnSelect: true, // Ensure the dropdown closes after selection
                dropdownParent: $('body'),  
                templateResult: function (data) {
                    // Retrieve custom attributes
                    const name = $(data.element).data('name');
                    const price = $(data.element).data('price');

                    if (!data.id || name == undefined || price == undefined) {
                        return data.text;
                    }
                    
                    // Return a custom template
                    return $(`
                        <div class="max-title-width" style="display: flex; justify-content: space-between; align-items: flex-start;">
                            <span>${name}</span>
                            <span style="color: #969696 !important;">${price}</span>
                        </div>
                    `);
                },
                templateSelection: function (data) {
                    // Customize how the selected item appears
                    const name = $(data.element).data('name');
                    const price = $(data.element).data('price');
        
                    if (!data.id || name == undefined || price == undefined) {
                        return data.text;
                    }

                    return `${name} - ${price}`;
                }        
            });
            setTimeout(function(){
                $('.select2-selection__rendered').removeAttr('title');
            }, 200);
        }
        $(el).on('select2:select', function (e) {
            const selectedData = e.params.data;
            const originalOption = $(el).find(`option[value="${selectedData.id}"]`);
            if (originalOption.data('type') === 'bundle') {
                // Call your function
                if(selectedData.element.text == 'Bundle #1 (minimum)'){
                    console.log('Bundle #1 (minimum) selected');
                    $(el).closest('.invoice-items-table').remove();
                    generateBundle1();
                    $(el).select2("close");
                    $(el).select2().trigger("select2:close");
                }else if(selectedData.element.text == 'Bundle #2 (recommended)'){
                    console.log('Bundle #2 (recommended) selected');
                    $(el).closest('.invoice-items-table').remove();
                    generateBundle2();
                    $(el).select2("close");
                    $(el).select2().trigger("select2:close");
                }
            }
        });    
    });
}

// Open modals on page load
$(document).ready(function() {
    $('.modal-open-on-load').each(function(){
        let openedModal = new bootstrap.Modal($(this));
        openedModal.show();
    });
});

// Open modals on page load
$('.main-content .hamburger').on('click', function() {
    if($(window).width() < 768) {
        if($('.main-content').hasClass('opened')){
            $('.main-content').removeClass('opened');
            $('.dashboard-side').removeClass('show');
            $('main').removeClass('sidebar');
        }else{
            $('.main-content').addClass('opened');
            $('.dashboard-side').addClass('show'); // Ensure 'show' actually shows the sidebar
            $('main').addClass('sidebar');
        }
    }
});
$('.dashboard-side [href]:not(.menu-item-has-children)').on('click', function() {
    if($(window).width() < 768) {
        $('.main-content').removeClass('opened');
        $('.dashboard-side').removeClass('show');
        $('main').removeClass('sidebar');
    }
});

$('.close-notification').on('click', function() {
    $('#offcanvasExample').removeClass('show');
    $('.offcanvas-backdrop').fadeOut();
    setTimeout(function(){
        $('.offcanvas-backdrop').remove();
    }, 300);
    $('.offcanvas-backdrop').fadeOut();
});

// Toggle password eye
$(document).ready(function() {
    $('.password-eye').on('click', function(){
        let passwordEye = $(this).closest('.input-placeholder').find('input');

        console.log(passwordEye);
        
        if (passwordEye.attr('type') == "password") {
            passwordEye.attr('type', "text");
        } else {
            passwordEye.attr('type', "password");
        }
    });
});

// Cookie Functions
function createCookie(name, value, days) {
    var expires;

    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toGMTString();
    } else {
        expires = "";
    }
    document.cookie =
        encodeURIComponent(name) +
        "=" +
        encodeURIComponent(value) +
        expires +
        "; path=/";
}

function readCookie(name) {
    var nameEQ = encodeURIComponent(name) + "=";
    var ca = document.cookie.split(";");
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === " ") c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0)
            return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
}

function eraseCookie(name) {
    createCookie(name, "", -1);
}
// END Cookie Functions

// Location selector cookie
$('#location-selector-wrap .location').click(function(){
    createCookie("studio-location", $(this).attr('data-location-id'), 99999);
});

// 'Select all' action
$('.form-select-all').each(function(){
    let tableWrapper = $(this).closest('.entity-table');
    let selectAllElement = $(this);
    selectAllElement.change(function() {
        if (this.checked) {
            tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', true);
        } else {
            tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
            tableWrapper.find('.multi-select-actions').slideUp();
        }
    });
});

// 'Select none' action
$('.form-select-none').click(function(){
    let tableWrapper = $(this).closest('.entity-table');
    tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
    tableWrapper.find('.form-select-all').prop('checked', false);
    tableWrapper.find('.multi-select-actions').slideUp();
});

// Expand multi-select section on checkbox click
$('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
    let tableWrapper = $(this).closest('.entity-table');
    tableWrapper.find('.multi-select-actions').slideDown(300);

    tableWrapper.find('.list-group input[type="checkbox"]').change(function(){
        let allChecked = true;
        let allNonChecked = false;
        tableWrapper.find('.list-group input[type="checkbox"]').each(function () {
            allChecked &= $(this).prop('checked');
            allNonChecked ||= $(this).prop('checked');
        })
        tableWrapper.find('.form-select-all').prop('checked', allChecked);
        if (allNonChecked === false) {
            tableWrapper.find('.multi-select-actions').slideUp();
        }
    });
});

// Open first registration modal on page load
$(document).ready(function(){
    let registerModal = $('#register0ModalToggle');
    if( registerModal.length ) {
        let myModal = new bootstrap.Modal(registerModal);
        myModal.show();

        registerModal.addClass('fade');
    }
});

$(document).ready(function(){
    // Refresh the page with changed 'date' query parameter
    $('.go-to-date').click(function(){
        let dateTarget = $(this).attr('data-target-date');

        // Change query parameter and reload
        var queryParams = new URLSearchParams(window.location.search);
        queryParams.set("date", dateTarget);

        let studioLocation = $(this).attr('data-location');
        if( studioLocation ){
            queryParams.set("studio_location", studioLocation);
        }

        location.href = location.protocol + '//' + location.host + location.pathname + "?" + queryParams.toString();
    });
});

// Add decimal digits to number fields with 'step' attribute
$(document).ready(function() {
    $('input[type="number"][step]').change(function () {
        let step = 2;
        let stepAttr = $(this).attr('step');

        if( stepAttr ){
            let decimalIndex = stepAttr.indexOf('.');
            step = stepAttr.substring(decimalIndex).length - 1;
        }

        $(this).val(parseFloat($(this).val()).toFixed(step));
    });
});

function debounce(func, wait, immediate) {
    let timeout;
    return function () {
        let context = this, args = arguments;
        let later = function () {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        let callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}


$(document).on('select2:close', '.my-select2', function (e) {
    var evt = "scroll.select2"
    $(e.target).parents().off(evt)
    $(window).off(evt)
})


//tabs in Agreements popup
document.addEventListener("DOMContentLoaded", function () {
  const tabButtons = document.querySelectorAll(".tab-button");
  const tabContents = document.querySelectorAll(".popup-tab-content");

  tabButtons.forEach(button => {
    button.addEventListener("click", () => {
      const targetId = button.getAttribute("data-tab");

      tabButtons.forEach(btn => btn.classList.remove("active"));
      tabContents.forEach(content => content.classList.remove("active"));

      button.classList.add("active");
      document.getElementById(targetId).classList.add("active");
    });
  });
});

//agreements popup show/hide
 $(document).ready(function() {
    $('.all-statuses').click(function() {
    $('.all-statuses-wrap').addClass('show');
    });
    $('#popup-agr-body .modal-header .btn-close').click(function() {
    $('.statuses-wrap').removeClass('show');
    });
});

