<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'ERP') }}</title>

    <!-- Scripts -->
    @vite(['resources/sass/app.sass', 'resources/js/app.js', 'resources/css/custom.css'])
    <style>
        .schedule { background:#fff; padding:1rem; border-radius:4px; box-shadow:0 2px 4px rgba(0,0,0,0.1); margin-bottom:1.5rem; }
        .schedule h2 { margin-bottom:0.5rem; font-size:1.25rem; }
        .schedule ul { list-style:none; padding:0; }
        .schedule li { margin:0.5rem 0; }
        .schedule li strong { display:inline-block; width:140px; }
        #email_confirm {
            position: absolute;
            bottom: 10px;
            right: 10px;
            height: 20px;
            font-size: 9px;
            text-transform: uppercase;
            background: #000;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px;
            border-radius: 20px;
            cursor: pointer;
        }
        #email_confirm:hover {
            background: #999;
        }
        #payment-success {
            padding-top: 100px;
        }
        #payment-success svg {
            margin-bottom: 24px;
        }
        .w1000px {
            width: calc(100% - 60px);
            max-width: 1000px;
        }
        .w1000px h3 {
            font-size: 24px;
            line-height: 1.4;
        }
        #payment-form {
            width: 100%;
            max-width: 500px;
        }
        th, td {
            border-bottom: 1px solid #f0f0f0 !important;
        }
        .scroll-table {width: 100%; overflow-x: auto;border:1px solid #f0f0f0; border-radius:10px; max-width:800px}    
        .scroll-table table {}
        .text-start tr th {background: #fcfcfc;}
        .text-start {
            
            width: 100%;
            border-radius: 10px;
            margin: 0 auto;
        }
        .text-start tr th {
            font-weight:500;
            font-size: 12px;
            color:#969696;
            text-transform: uppercase;
        }
        .text-start tr th, .text-start tr td {
            height: 50px;
        }
        .text-start tr th:first-of-type, .text-start tr td:first-of-type {
            padding-left: 30px;
            font-weight: 500;
        }
        .text-start tr:last-of-type td {
            border-bottom:none !important;
        }
        .payment-page .border-bottom-grey {
            border-bottom: 1px solid #f0f0f0;
        }
        

        @media(max-width: 768px){ 
        .w1000px {
        	width: calc(100% - 50px);
	        max-width: 1000px;
        }
        .w1000px h3 {
	        font-size: 20px;
        }
        #payment-form-wrap .row {
            display: flex;
            flex-direction: column;
        }
        #payment-form-wrap .row .col-6 {
            width: 100%;
        }
        #payment-form-wrap .row .col-5 {
            width: 100%;
            margin: 20px 0 0 0;
        }
        #payment-success {
            padding-top: 75px;
        }
    }
     @media(max-width: 600px){ 
        #payment-success {
            padding-top: 28px;        
        }
        #payment-success svg {
	        margin-bottom: 0;
	        width: 80px;
            height: 80px;
        }
        .w1000px h3 {
        	font-size: 16px;
        }
    }

    </style>
</head>
<body class="font-sans antialiased payment-page">
    <!--<nav class="navbar d-flex justify-content-between border-bottom px-4 py-4">
        <div class="logo">
            <svg xmlns="http://www.w3.org/2000/svg" width="137.733" height="10" viewBox="0 0 137.733 15">
                <path id="Path_5652" data-name="Path 5652" d="M1.729.926a.4.4,0,0,0,.4.4H9.761a.4.4,0,0,0,.4-.4V-.785a.4.4,0,0,0-.4-.4H4.441v-11.7a.41.41,0,0,0-.4-.4H2.125a.4.4,0,0,0-.4.4Zm11.745.4H15.29A.629.629,0,0,0,15.895.9l1.022-2.253h6.238L24.177.9a.6.6,0,0,0,.605.417H26.6A.368.368,0,0,0,26.952.78l-6.426-14.04a.355.355,0,0,0-.355-.229h-.209a.375.375,0,0,0-.355.229L13.12.78A.368.368,0,0,0,13.474,1.322Zm4.465-4.986,2.045-4.59h.063l2.086,4.59Zm11.85-2.295a7.439,7.439,0,0,0,7.49,7.469A10.541,10.541,0,0,0,42.452.154a.4.4,0,0,0,.167-.334V-5.521a.392.392,0,0,0-.376-.4H38.238a.384.384,0,0,0-.4.4v1.648a.379.379,0,0,0,.4.376h1.669v1.773a6.526,6.526,0,0,1-2.483.522A4.746,4.746,0,0,1,32.73-5.98,4.783,4.783,0,0,1,37.4-10.82a4.73,4.73,0,0,1,3.15,1.21.356.356,0,0,0,.542,0l1.293-1.356a.4.4,0,0,0-.021-.584,7.887,7.887,0,0,0-5.09-1.94A7.492,7.492,0,0,0,29.789-5.959ZM47.772.926a.4.4,0,0,0,.4.4h1.919a.41.41,0,0,0,.4-.4v-5.32H52.8l2.775,5.529a.358.358,0,0,0,.334.188H58.1a.4.4,0,0,0,.355-.605L55.6-4.582A4.6,4.6,0,0,0,58.683-8.8a4.519,4.519,0,0,0-4.548-4.485H48.168a.4.4,0,0,0-.4.4Zm2.733-7.552v-4.152h3.4a2.076,2.076,0,0,1,2.065,2.024,2.125,2.125,0,0,1-2.065,2.128ZM63.753.926a.4.4,0,0,0,.4.4H72.64a.4.4,0,0,0,.4-.4V-.785a.4.4,0,0,0-.4-.4H66.465V-4.832h5.153a.4.4,0,0,0,.4-.4V-6.96a.41.41,0,0,0-.4-.4H66.465v-3.421H72.64a.4.4,0,0,0,.4-.4v-1.711a.4.4,0,0,0-.4-.4H64.149a.4.4,0,0,0-.4.4Zm14.353,0a.4.4,0,0,0,.4.4h8.491a.4.4,0,0,0,.4-.4V-.785a.4.4,0,0,0-.4-.4H80.818V-4.832h5.153a.4.4,0,0,0,.4-.4V-6.96a.41.41,0,0,0-.4-.4H80.818v-3.421h6.175a.4.4,0,0,0,.4-.4v-1.711a.4.4,0,0,0-.4-.4H78.5a.4.4,0,0,0-.4.4Zm21.05,0a.4.4,0,0,0,.4.4h8.491a.4.4,0,0,0,.4-.4V-.785a.4.4,0,0,0-.4-.4h-6.175V-4.832h5.153a.4.4,0,0,0,.4-.4V-6.96a.41.41,0,0,0-.4-.4h-5.153v-3.421h6.175a.4.4,0,0,0,.4-.4v-1.711a.4.4,0,0,0-.4-.4H99.552a.4.4,0,0,0-.4.4Zm14.353,0a.4.4,0,0,0,.4.4h1.919a.41.41,0,0,0,.4-.4v-5.32h2.316l2.775,5.529a.358.358,0,0,0,.334.188h2.191a.4.4,0,0,0,.355-.605l-2.858-5.3A4.6,4.6,0,0,0,124.42-8.8a4.519,4.519,0,0,0-4.548-4.485h-5.967a.4.4,0,0,0-.4.4Zm2.733-7.552v-4.152h3.4a2.076,2.076,0,0,1,2.065,2.024,2.125,2.125,0,0,1-2.065,2.128ZM129.49.926a.4.4,0,0,0,.4.4h1.919a.41.41,0,0,0,.4-.4v-4.9h2.608a4.692,4.692,0,0,0,4.652-4.694,4.666,4.666,0,0,0-4.673-4.611h-4.9a.4.4,0,0,0-.4.4ZM132.2-6.522v-4.193h2.42a2.081,2.081,0,0,1,2.149,2.024,2.15,2.15,0,0,1-2.149,2.17Z" transform="translate(-1.729 13.49)" fill="#000"/>
            </svg>
        </div>
        <div class="d-flex align-items-center justify-content-center">
            <p class="m-0 d-none d-md-block">{{ __('Today is') }} {{ date('l, F j, Y') }}</p>
        </div>
    </nav>--> 
    <div class="d-flex justify-content-center align-items-center border-bottom-grey px-4 py-5">
    <svg xmlns="http://www.w3.org/2000/svg" width="134" height="15.001" viewBox="0 0 134.799 15.001">
        <path id="Path_7120" data-name="Path 7120" d="M-143.154-1.223a.4.4,0,0,0,.4.4h7.625a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4h-5.313V-15.016a.41.41,0,0,0-.4-.4h-1.917a.4.4,0,0,0-.4.4Zm11.73.4h1.813a.628.628,0,0,0,.6-.417l1.021-2.25h6.23l1.021,2.25a.6.6,0,0,0,.6.417h1.813a.368.368,0,0,0,.354-.542l-6.417-14.022a.354.354,0,0,0-.354-.229h-.208a.374.374,0,0,0-.354.229l-6.48,14.022A.368.368,0,0,0-131.424-.827Zm4.459-4.979,2.042-4.584h.063l2.083,4.584ZM-115.132-8.1a7.429,7.429,0,0,0,7.48,7.459,10.527,10.527,0,0,0,5.167-1.354.4.4,0,0,0,.167-.333V-7.661a.392.392,0,0,0-.375-.4h-4a.383.383,0,0,0-.4.4v1.646a.379.379,0,0,0,.4.375h1.667v1.771a6.518,6.518,0,0,1-2.479.521,4.74,4.74,0,0,1-4.688-4.771,4.777,4.777,0,0,1,4.667-4.834,4.723,4.723,0,0,1,3.146,1.208.356.356,0,0,0,.542,0l1.292-1.354a.4.4,0,0,0-.021-.583,7.876,7.876,0,0,0-5.084-1.938A7.483,7.483,0,0,0-115.132-8.1Zm17.959,6.875a.4.4,0,0,0,.4.4h1.917a.41.41,0,0,0,.4-.4V-6.536h2.313l2.771,5.521a.357.357,0,0,0,.333.188h2.188a.394.394,0,0,0,.354-.6l-2.854-5.292a4.6,4.6,0,0,0,3.084-4.209,4.513,4.513,0,0,0-4.542-4.479h-5.959a.4.4,0,0,0-.4.4Zm2.729-7.542v-4.146h3.4a2.073,2.073,0,0,1,2.063,2.021,2.122,2.122,0,0,1-2.063,2.125Zm13.23,7.542a.4.4,0,0,0,.4.4h8.48a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-78.5V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-78.5v-3.417h6.167a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm14.334,0a.4.4,0,0,0,.4.4H-58a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-64.17V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-64.17v-3.417H-58a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm21.022,0a.41.41,0,0,0,.4.4h1.938a.41.41,0,0,0,.4-.4V-15.016a.41.41,0,0,0-.4-.4h-1.938a.41.41,0,0,0-.4.4Zm7.646-.083a.382.382,0,0,0,.4.479h1.875a.4.4,0,0,0,.375-.313l1.208-7.9h.063l3.688,8.188a.374.374,0,0,0,.354.229h.375a.354.354,0,0,0,.354-.229l3.646-8.188h.063l1.229,7.9a.452.452,0,0,0,.4.313h1.9a.362.362,0,0,0,.375-.479l-2.458-14a.351.351,0,0,0-.375-.313h-.333a.359.359,0,0,0-.354.208l-4.584,9.834h-.063l-4.584-9.834a.38.38,0,0,0-.354-.208h-.333a.351.351,0,0,0-.375.313Zm20.48-.938A6.7,6.7,0,0,0-13.147-.619c2.938,0,4.792-1.979,4.792-4.125,0-2.688-2.333-3.9-4.354-4.709-1.688-.688-2.458-1.354-2.458-2.333a1.659,1.659,0,0,1,1.833-1.458,6.854,6.854,0,0,1,2.9,1.146.548.548,0,0,0,.729-.25l.792-1.188a.516.516,0,0,0-.125-.688,7.335,7.335,0,0,0-4.167-1.4c-3.313,0-4.688,2.146-4.688,4,0,2.458,1.958,3.709,3.917,4.5,1.75.708,2.646,1.438,2.646,2.5a1.739,1.739,0,0,1-1.9,1.6,6.9,6.9,0,0,1-3.146-1.292.483.483,0,0,0-.708.146l-.75,1.292C-18.022-2.536-17.918-2.432-17.73-2.244Z" transform="translate(143.154 15.62)"/>
    </svg>
</div>
    {{-- <div class="page-title">
        <div class="title-left text-center w-100">
            <h3>{{ __('Checkout') }}</h3>
        </div>
    </div> --}}
    <div class="w1000px mx-auto">
         <div class="row" id="payment-success">
            <div class="page-title ttl-dropdown no-border-btm">
                <div class="d-flex align-items-center justify-center flex-column w-100 text-center gap-5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="119" height="119" viewBox="0 0 119 119">
                    <g id="Group_13063" data-name="Group 13063" transform="translate(-901 -220)">
                    <circle id="Ellipse_115" data-name="Ellipse 115" cx="59.5" cy="59.5" r="59.5" transform="translate(901 220)" fill="#52c15a" opacity="0.15"/>
                    <path id="Icon_material-check" data-name="Icon material-check" d="M17.827,32.054,8.344,22.725,5.115,25.879,17.827,38.385,45.115,11.539,41.909,8.385Z" transform="translate(934.885 256.115)" fill="#52c15a"/>
                     </g>
                    </svg>

                    @if($type === 'lease')
                        <h3>All Lease Payments Completed!</h3>
                        <p>All monthly installments for this lease have been successfully paid.</p>
                        <p class="m-0">Total Amount Paid: ${{ number_format($allPayments->sum('payment_amount') / 100, 2) }}</p>
                    @else
                        <h3 class="mb-0 text-uppercase">Payment was already made</h3>
                        <p class="f-14">Amount of ${{ number_format($payment->payment_amount / 100, 2) }} has been paid.</p>
                        <div style="width:100%; height: 1px; background:#f0f0f0;"></div>
                    @endif
                    <p class="m-0 f-14">All Payments of this {{ $type }}</p>
                    <div class="scroll-table">
                    <table class="text-start" cellpadding=5>
                        <tr>
                            <th>Payment ID</th>
                            @if($type === 'lease')
                                <th>Payment #</th>
                            @endif
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Invoice #</th>
                            <th>Paid</th>
                            <th>Paid at</th>
                        </tr>
                        @foreach ($allPayments as $single_payment)
                        <tr>
                            <td>{{ $single_payment->id }}</td>
                            @if($type === 'lease')
                                <td>#{{ $single_payment->payment_number }}</td>
                            @endif
                            <td>{{ $single_payment->description }}</td>
                            <td>${{ number_format($single_payment->payment_amount / 100, 2) }}</td>
                            <td>#{{ $single_payment->invoice->number ?? 'N/A' }}</td>
                            <td>{{ $single_payment->status == 1 ? 'Paid' : 'Unpaid' }}</td>
                            <td>{{ $single_payment->updated_at }}</td>
                        </tr>
                        @endforeach
                    </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
