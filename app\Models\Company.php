<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'owner_first_name',
        'owner_last_name',
        'email',
        'phone',
        'address',
        'address2',
        'city',
        'state_id',
        'zip',
        'status',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function sortableFields(): array
    {
        return [
            'id',
            'name',
            'status',
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function image(): MorphOne
    {
        return $this->morphOne(File::class, 'fileable');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function licenses(): HasMany
    {
        return $this->hasMany(License::class);
    }

    public function leases(): HasMany
    {
        return $this->hasMany(Lease::class);
    }

    public function invoiceProducts(): HasMany
    {
        return $this->hasMany(InvoiceProduct::class);
    }
}
