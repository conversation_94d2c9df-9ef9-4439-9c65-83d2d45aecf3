@extends('layouts.app')

@section('content')
    <div class="page-title ttl-dropdown no-border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>LOCATION: {{ $license->studio->name }}</h3>
                <span class="ms-3">
                     @include('partials.status-badge', [
                            'status' => $license->is_active ? 'active' : 'inactive',
                        ])
                </span>
            </div>
            <a href="{{route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence'])}}" class="back-link">← Back</a>
        </div>
    </div>

    <ul class="nav nav-tabs lh-1 mx-n12" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="payment-history-tab" data-bs-toggle="tab"
                    data-bs-target="#payment-history" data-tab="payment-history" type="button" role="tab" aria-controls="payment-history"
                    aria-selected="false">{{ __('PAYMENT HISTORY') }}</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="upcoming-payments-tab" data-bs-toggle="tab"
                    data-bs-target="#upcoming-payments" data-tab="upcoming-payments" type="button" role="tab" aria-controls="upcoming-payments"
                    aria-selected="false">{{ __('UPCOMING PAYMENTS') }}</button>
        </li>
        {{-- <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="files-tab" data-bs-toggle="tab"
            data-bs-target="#files" data-tab="files" type="button" role="tab" aria-controls="files"
            aria-selected="false">{{ __('AGREEMENTS (' . $license_files_count . ')') }}</button>
        </li> --}}
        <li class="nav-item" role="presentation">
            <button class="nav-link ls-0.6px px-2 px-sm-3 text-uppercase custom-fw-500" id="notes-tab" data-bs-toggle="tab"
                    data-bs-target="#notes" data-tab="notes" type="button" role="tab" aria-controls="notes"
                    aria-selected="false">{{ __('INTERNAL NOTES (' . count($license_notes) . ')') }}</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link active ps-0 ls-0.6px px-2 pe-sm-3 text-uppercase custom-fw-500" id="profile-tab" data-bs-toggle="tab"
                    data-bs-target="#profile" data-tab="profile" type="button" role="tab" aria-controls="profile"
                    aria-selected="true">{{ __('PROFILE') }}</button>
        </li>
    </ul>


    <div class="tab-content" id="myTabContent1">
        <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
            <h5 class="my-6 fs-14px form-section-title custom-fw-500 text-uppercase pb-05">{{ __('basic info') }}</h5>
            <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Studio Name:</div>
                    <div class="">
                        {{$license->studio->name}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Owner:</div>
                    <div class="">
                        {{$license->studio->owner_first_name}} {{$license->studio->owner_last_name}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Phone #:</div>
                    <div class="">
                        {{$license->studio->phone}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{$license->studio->email}}
                    </div>
                </div>
            </div>
            </div>

            <div class="main-subtitle no-border-btm">
                <h5>ADDRESS INFO</h5>
            </div>
            <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Address:</div>
                    <div class="">
                        {{$license?->studio?->address}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">City:</div>
                    <div class="">
                    {{$license?->studio?->city}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">ZIP Code:</div>
                    <div class="">
                        {{$license?->studio?->zip}}
                    </div>
                </div>
                @if($license->location === \App\Helpers\Constants::LOCATION_TYPE[0])
                    <div class="instructor-basic-info">
                        <div class="text-secondary">State:</div>
                        <div class="">
                            {{$license?->studio?->state?->name}}
                        </div>
                    </div>
                @else
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Country:</div>
                        <div class="">
                            {{$license?->studio?->country?->name}}
                        </div>
                    </div>
                @endif
            </div>
            </div>

            @if($license->type === \App\Helpers\Constants::LICENSE_TYPES[0])
                <h5 class="my-6 fs-14px form-section-title custom-fw-500 text-uppercase pb-05">{{ __('license info') }}</h5>
                <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Type:</div>
                        <div class="">
                            {{ucfirst($license->type)}} ({{ $package->name }})
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Price:</div>
                        <div class="">
                            <span class="text-success">{{$admin_currency_symbol}}{{number_format($licenseTotal, 2)}}</span>
                            &nbsp; ({{$admin_currency_symbol}}{{number_format($licenseMonthly, 2)}}/year)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Deposit:</div>
                        <div class="">
                            {{$admin_currency_symbol}}{{(number_format($licenseDeposit, 2)) ?? 'No Deposit'}}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Start date:</div>
                        <div class="">
                            {{\Carbon\Carbon::parse($license->starting_date)->format('m/d/Y')}}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Ends on:</div>
                        <div class="">
                            {{\Carbon\Carbon::parse($license->payments->sortByDesc('starting_date')->take(1)->first()->starting_date)->addYear(1)->format('m/d/Y')}}
                        </div>
                    </div>
                </div>
                </div>
                <div class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                    <a type="button" href="{{route('admin.licenses.edit', ['customer' => $customer, 'license' => $license])}}">
                        <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
                    </a>
                </div>
            @else
                <h5 class="mt-45 mb-45 fs-14px form-section-title text-uppercase">{{ __('Exclusivity Info') }}</h5>
                <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Type:</div>
                        <div class="">
                            {{ucfirst($license->type)}} ({{ $package->name }})
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Price:</div>
                        <div class="">
                            {{$admin_currency_symbol}}{{number_format($licenseTotal, 2)}}
                            ({{$admin_currency_symbol}}{{number_format($licenseMonthly / 100 / $conversionRate, 2)}}/month)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Remaining:</div>
                        <div class="">
                            {{$admin_currency_symbol}}{{number_format($licenseRemaining['paymentSum'], 2) }}
                            ({{$licenseRemaining['count']}}/{{number_format($license->duration / 100 / $conversionRate, 2)}} months)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Start date:</div>
                        <div class="">
                            {{\Carbon\Carbon::parse($license->starting_date)->format('m/d/Y')}}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Ends on:</div>
                        <div class="">
                            {{\Carbon\Carbon::parse($license->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y')}}
                        </div>
                    </div>
                </div>
                </div>
                <div class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                    <a type="button" href="{{route('admin.licenses.edit', ['customer' => $customer, 'license' => $license])}}">
                        <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
                    </a>
                </div>
            @endif
        </div>
        <div class="tab-pane fade" id="payment-history" role="tabpanel" aria-labelledby="payment-history-tab" tabindex="1">
        </div>
        <div class="tab-pane fade" id="upcoming-payments" role="tabpanel" aria-labelledby="upcoming-payments-tab" tabindex="2">
        </div>
        <div class="tab-pane fade" id="notes" role="tabpanel" aria-labelledby="notes-tab" tabindex="3">
        </div>
        <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab" tabindex="3">
        </div>
        <script type="module">
            $(document).ready(function () {
                $('button[data-bs-toggle="tab"]').on('click', function () {
                    var target = $(this).data('bs-target');
                    localStorage.setItem('licenseActiveTab', target);
                    loadTabContent(this);
                });

                var licenseActiveTab = localStorage.getItem('licenseActiveTab');
                if (licenseActiveTab) {
                    activateTab(licenseActiveTab);
                } else {
                    activateTab('#profile');
                }
                function activateTab(target) {
                    $('.nav-link').removeClass('active');
                    $('.tab-pane').removeClass('show active');
                    $(`button[data-bs-target="${target}"]`).addClass('active');
                    $(target).addClass('show active');
                    var tabElement = $(`button[data-bs-target="${target}"]`);
                    loadTabContent(tabElement);
                }
                function loadTabContent(tabElement) {
                    var target = $(tabElement).data('bs-target');
                    var tab = $(tabElement).data('tab');
                    var url = '{{ route('admin.licenses.load-tab-content', ['customer' => $customer, 'license' => $license]) }}' + '?tab=' + tab;
                    var targetContent = $(target);
                    if (!$.trim(targetContent.html())) {
                        $.ajax({
                            url: url,
                            method: 'GET',
                            success: function (data) {
                                setTimeout(function () {
                                    targetContent.html(data);
                                }, 300);
                            },
                            error: function () {
                                flasherJS.error('', 'Error occurred while loading data.');
                            }
                        });
                    }
                }
            });
        </script>
@endsection