<?php

namespace App\Http\Controllers;

use App\Helpers\NumberToWordsConverterHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Products\StoreProductsRequest;
use App\Http\Requests\Admin\Products\UpdateProductsRequest;
use App\Models\Company;
use App\Models\Products;
use App\Models\Purchase;
use App\Services\Admin\Products\IProductsService;
use App\Services\Admin\Woocommerce\IWoocommerceService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SyncController extends Controller
{
    private IWoocommerceService $WoocommerceService;

    public function __construct(IProductsService $ProductsService, IWoocommerceService $WoocommerceService)
    {
        $this->ProductsService = $ProductsService;
        $this->WoocommerceService = $WoocommerceService;
    }

    
    public function test_convert(){

        $purchase_id = 44; // 59 63
        $model = Purchase::find($purchase_id);
        $company_id = 1; // 59 63
        $company = Company::find($company_id);

        $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
        $sumPrice = round($model->machine_price / $conversionRate / 100,2) * $model->machine_quantity;
        $monthlySum =  round($model->price / $conversionRate / 100,2) * $model->machine_quantity;
        $depositAmount = ($model->deposit_amount) ? round($model->deposit_amount / $conversionRate / 100, 2) : 0;
        $machinePrice = round($model->machine_price / $conversionRate / 100, 2);
        $left_to_pay = round($sumPrice - $depositAmount, 2);
        // Fourteen (14) Megaformer, Model MEGA PRO
        $purchase_number_model = NumberToWordsConverterHelper::convert($model->machine_quantity) . ' (' . $model->machine_quantity . ') ' . $model->machine->name . ', Model ' . $model->machine->name;
        // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
        $purchase_amount = NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')';
        // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
        $purchase_per_machine = $model->machine->name . ' is ' . NumberToWordsConverterHelper::convert($machinePrice) . ' Dollars' . ' ($' . $machinePrice . ')';

        return response()->json([
            'agreement_date' => Carbon::now()->format('jS \d\a\y \o\f F Y'),
            'agreement_lagree_company' => $company->name,
            'agreement_company' => $model->customer->name,

            'agreement_address' => $model->customer->shipping_address ? $model->customer->shipping_address : $model->studio->address,
            'agreement_city' => $model->customer->shipping_city ? $model->customer->shipping_city : $model->customer->city,
            'agreement_zip' => $model->customer->shipping_zip ? $model->customer->shipping_zip : $model->customer->zip,
            'agreement_coutry_state' => $model->studio->country ? $model->studio->country?->name : $model->studio->state?->name,
                
            'agreement_annual_fee' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
            'agreement_deposit' => NumberToWordsConverterHelper::convert($depositAmount) . ' Dollars' . ' ($' . $depositAmount . ')',
            'agreement_six_months' => NumberToWordsConverterHelper::convert($left_to_pay) . ' Dollars' . ' ($' . $left_to_pay . ')',
            'agreement_annual' => NumberToWordsConverterHelper::convert($sumPrice) . ' Dollars' . ' ($' . $sumPrice . ')',
            'agreement_company_rep' => $model->studio->owner_first_name . ' ' . $model->studio->owner_last_name,
            'purchase_number_model' => $purchase_number_model, // Fourteen (14) Megaformer, Model MEGA PRO
            'purchase_amount' => $purchase_amount, // One Hundred Twenty-Four Thousand Six Hundred Dollars ($124,600)
            'purchase_per_machine' => $purchase_per_machine, // MEGA PRO is Eight Thousand Nine Hundred Dollars ($8,900)
        ]);
    }
    public function getAllProducts(): void
    {
        // dd(env('WOOCOMMERCE_STORE_URL'));
        $start_time = microtime(true);
        $list = [];
        $products = $this->WoocommerceService->getAllProducts();
        $count = count($products);
        echo "Compare based on $count products count<br><br>";
        if(!empty($products)){
            foreach($products as $key => $product){
                if(isset($product->status) AND $product->status != 'publish'){
                    unset($products[$key]);
                }else{
                    $local_product = Products::where(['name' => $product->name, 'custom_product' => 0])->first();
                    if(isset($product->name) AND isset($local_product->name) AND $product->name == $local_product->name){
                        echo "<span style='color:green;'>$key. $product->name</span><br>";
                        try {
                            $this->ProductsService->update([
                                'wp_id' => $product->id,
                                'price' => $product->price ?? '0.00',
                                'supplier_price' => $product->supplier_price ?? '0.00',
                                'stock' => $product->stock_quantity ?? 0,
                                'name'  => $product->name ?? '',
                                'custom_product'  => 0,
                            ], $local_product);
                            echo "<span style='color:green;'>UPDATED</span><br>";
                        } catch (\Exception $e) {
                            dd($e);
                            echo "<span style='color:red;'>NOT UPDATED</span><br>";
                        }
                    }else{
                        echo "<span style='color:red;'>$key. $product->name</span><br>";
                        try {
                            $this->ProductsService->store([
                                'wp_id' => $product->id,
                                'price' => $product->price ?? '0.00',
                                'supplier_price' => $product->supplier_price ?? '0.00',
                                'stock' => $product->stock_quantity ?? 0,
                                'name'  => $product->name ?? '',
                                'custom_product'  => 0,
                            ]);
                            echo "<span style='color:green;'>CREATED</span><br>";
                        } catch (\Exception $e) {
                            dd($e);
                            echo "<span style='color:red;'>NOT CREATED</span><br>";
                        }
                    }
                }
            }
        }else{
            echo "No products update in last 60 days";
        }
        $end_time = microtime(true);
        $execution_time = $end_time - $start_time;

        echo "<br><br>Script Execution Time = $execution_time sec";
    }
    public function getUpdatedProducts(): void{
        // dd(env('WOOCOMMERCE_STORE_URL'));
        // $list = [];
        $products = $this->WoocommerceService->getUpdatedProducts(date('Y-m-d', strtotime('-60 day')) . 'T00:00:00');
        // dd($products);
        if(!empty($products)){
            foreach($products as $key => $product){
                if(isset($product->status) AND $product->status != 'publish'){
                    unset($products[$key]);
                }else{
                    $local_product = Products::where(['name' => $product->name, 'custom_product' => 0])->first();
                    if(isset($product->name) AND isset($local_product->name) AND $product->name == $local_product->name){
                        echo "<span style='color:green;'>$key. $product->name</span><br>";
                        try {
                            $this->ProductsService->update([
                                'wp_id' => $product->id,
                                'price' => $product->price,
                                'stock' => $product->stock_quantity,
                                'name'  => $product->name
                            ], $local_product);
                            echo "<span style='color:green;'>UPDATED</span><br>";
                        } catch (\Exception $e) {
                            echo "<span style='color:red;'>NOT UPDATED</span><br>";
                        }
                    }else{
                        echo "<span style='color:red;'>$key. $product->name</span><br>";
                        try {
                            $this->ProductsService->store([
                                'wp_id' => $product->id,
                                'price' => $product->price,
                                'stock' => $product->stock_quantity,
                                'name'  => $product->name
                            ]);
                            echo "<span style='color:green;'>CREATED</span><br>";
                        } catch (\Exception $e) {
                            echo "<span style='color:red;'>NOT CREATED</span><br>";
                        }
                    }
                }
            }
        }else{
            echo "No products update in last 60 days";
        }
    }
}
