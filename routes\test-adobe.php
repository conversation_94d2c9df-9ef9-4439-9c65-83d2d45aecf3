<?php

use Illuminate\Support\Facades\Route;
use App\Services\Admin\AdobeSign\AdobeSignService;
use App\Models\Customer;
use App\Models\License;
use App\Models\Company;
use App\Models\Agreement;

// Test Adobe Sign API endpoint
Route::get('/test-adobe-sign', function () {
    $results = [];
    
    try {
        // Test 1: Basic API connectivity
        $results['test_1'] = 'Testing Adobe Sign API connectivity...';
        $service = new AdobeSignService();
        $baseUri = $service->getBaseUri();
        $results['test_1_result'] = "✓ SUCCESS: Base URI retrieved: $baseUri";
        
    } catch (Exception $e) {
        $results['test_1_result'] = "❌ FAILED: " . $e->getMessage();
        return response()->json($results, 500);
    }
    
    try {
        // Test 2: Configuration check
        $results['test_2'] = 'Checking configuration...';
        $accessToken = config('adobe-sign.access_token');
        $appUrl = env('APP_URL');
        $adobeSignName = env('ADOBE_SIGN_NAME');
        $adobeSignEmail = env('ADOBE_SIGN_EMAIL');
        
        $configResults = [];
        $configResults['access_token'] = empty($accessToken) ? "❌ NOT SET" : "✓ SET";
        $configResults['app_url'] = empty($appUrl) ? "❌ NOT SET" : "✓ SET ($appUrl)";
        $configResults['adobe_sign_name'] = empty($adobeSignName) ? "❌ NOT SET" : "✓ SET ($adobeSignName)";
        $configResults['adobe_sign_email'] = empty($adobeSignEmail) ? "❌ NOT SET" : "✓ SET ($adobeSignEmail)";
        
        $results['test_2_result'] = $configResults;
        
    } catch (Exception $e) {
        $results['test_2_result'] = "❌ FAILED: " . $e->getMessage();
    }
    
    try {
        // Test 3: Database connectivity and recent data
        $results['test_3'] = 'Checking database and recent data...';
        
        $customerCount = Customer::count();
        $licenseCount = License::count();
        $companyCount = Company::count();
        
        // Check recent licenses
        $recentLicense = License::orderBy('created_at', 'desc')->first();
        $recentLicenseData = null;
        
        if ($recentLicense) {
            $conversionRateCount = $recentLicense->conversionRate()->count();
            $recentLicenseData = [
                'id' => $recentLicense->id,
                'created_at' => $recentLicense->created_at,
                'initial_currency_id' => $recentLicense->initial_currency_id,
                'conversion_rate_count' => $conversionRateCount
            ];
        }
        
        $results['test_3_result'] = [
            'database_connected' => true,
            'customers' => $customerCount,
            'licenses' => $licenseCount,
            'companies' => $companyCount,
            'recent_license' => $recentLicenseData
        ];
        
    } catch (Exception $e) {
        $results['test_3_result'] = "❌ FAILED: " . $e->getMessage();
    }
    
    try {
        // Test 4: Template generation test
        $results['test_4'] = 'Testing HTML template generation...';
        
        $customer = Customer::first();
        $company = Company::first();
        $license = License::first();
        
        if (!$customer || !$company || !$license) {
            $results['test_4_result'] = "❌ FAILED: Missing required data (customer, company, or license)";
        } else {
            // Check if license has required data
            if (!$license->initial_currency_id) {
                $results['test_4_result'] = "❌ FAILED: License missing initial_currency_id";
            } else {
                $conversionRateCount = $license->conversionRate()->count();
                if ($conversionRateCount === 0) {
                    $results['test_4_result'] = "❌ FAILED: License missing conversion rates";
                } else {
                    // Try to generate prefill data
                    $templateNames = Agreement::LICENSE_TEMPLATE_MINI;
                    $prefillData = AdobeSignService::getPrefillData($license, 'license', $company, $templateNames);
                    
                    if (empty($prefillData)) {
                        $results['test_4_result'] = "❌ FAILED: Could not generate prefill data";
                    } else {
                        $results['test_4_result'] = "✓ SUCCESS: Template generation working";
                        $results['test_4_sample_data'] = array_slice($prefillData, 0, 3); // Show first 3 items
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        $results['test_4_result'] = "❌ FAILED: " . $e->getMessage();
    }
    
    // Test 5: Recent agreement analysis
    try {
        $results['test_5'] = 'Analyzing recent agreements...';
        
        $julyAgreements = Agreement::where('type', 'license')
            ->whereYear('created_at', 2025)
            ->whereMonth('created_at', 7)
            ->count();
            
        $augustAgreements = Agreement::where('type', 'license')
            ->whereYear('created_at', 2025)
            ->whereMonth('created_at', 8)
            ->count();
            
        $julySuccessful = Agreement::where('type', 'license')
            ->whereYear('created_at', 2025)
            ->whereMonth('created_at', 7)
            ->whereNotNull('adobe_agreement_id')
            ->count();
            
        $augustSuccessful = Agreement::where('type', 'license')
            ->whereYear('created_at', 2025)
            ->whereMonth('created_at', 8)
            ->whereNotNull('adobe_agreement_id')
            ->count();
        
        $results['test_5_result'] = [
            'july_total' => $julyAgreements,
            'august_total' => $augustAgreements,
            'july_successful' => $julySuccessful,
            'august_successful' => $augustSuccessful,
            'issue_detected' => ($augustAgreements > 0 && $augustSuccessful == 0)
        ];
        
    } catch (Exception $e) {
        $results['test_5_result'] = "❌ FAILED: " . $e->getMessage();
    }
    
    // Summary
    $results['summary'] = [
        'timestamp' => now(),
        'overall_status' => 'Check individual test results above'
    ];
    
    return response()->json($results, 200);
});
