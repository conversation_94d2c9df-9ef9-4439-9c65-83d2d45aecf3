<?php

namespace App\Helpers;

class Constants
{
    // Roles
    // const SUPER_ADMIN = 'super-admin';
    // const CUSTOMER = 'customer';
    const ROLES = "super-admin|b2c|studio|trainer|master-trainer";
    
    // Payment Status
    const PAYMENT_STATUS = [
        'not_paid' => 0,
        'paid'     => 1,
        'declined' => 2,
        'processing' => 3,
    ];

    const LICENSE_TYPES = [
        'license',
        'exclusivity'
    ];

    const LICENSE_STATUSES = [
        'License Sent (0/2)' => 0,
        'Signed 2/2 (wait4pay)' => 1,
        'Awaiting Delivery' => 2,
        'Active' => 3,
        'Inactive' => 4,
        'Signed 1/2' => 5,
        'Canceled' => 6,
        'Declined' => 7,
        'Pending' => 8,
        'Processing' => 9,
        'Payment method required' => 10,
        'Payment requires action' => 11,
        'Deposit paid' => 12,
        'Payment failed' => 13,
        'Other' => 14,
    ];

    const PURCHASE_STATUSES = [
        'Purchase Sent (0/2)' => 0,
        'Signed 2/2 (wait4pay)' => 1,
        'Awaiting delivery (paid)' => 2,
        'Delivered' => 3,
        'Inactive' => 4,
        'Signed 1/2' => 5,
        'Canceled' => 6,
        'Declined' => 7,
        'Pending' => 8,
        'Processing' => 9,
        'Payment method required' => 10,
        'Payment requires action' => 11,
        'Deposit paid' => 12,
        'Payment failed' => 13,
        'Other' => 14,
    ];

    const LEASE_STATUSES = [
        'Lease Sent (0/2)' => 0,
        'Signed 2/2 (wait4pay)' => 1,
        'Awaiting delivery (paid)' => 2,
        'Active (mp)' => 3,
        'Inactive' => 4,
        'Signed 1/2' => 5,
        'Canceled' => 6,
        'Declined' => 7,
        'Pending' => 8,
        'Processing' => 9,
        'Payment method required' => 10,
        'Payment requires action' => 11,
        'Deposit paid' => 12,
        'Payment failed' => 13,
        'Other' => 14,
        'Lease completed' => 15,
    ];

    const LICENSE_PACKAGES = [
        'Micro License' => 1,
        'Mini License' => 2,
        'Mega License' => 3,
        'EVO License' => 4,
    ];

    const LICENSE_PACKAGES_KEYS = [
        1 => 'Micro License',
        2 => 'Mini License',
        3 => 'Mega License',
        4 => 'EVO License',
    ];

    const LICENSE_PACKAGES_PRICES = [
        1 => 990,
        2 => 1990,
        3 => 3990,
        4 => 3990,
    ];

    const LEASE = 'lease';

    const LOCATION_TYPE = [
        'USA',
        'International'
    ];
}
