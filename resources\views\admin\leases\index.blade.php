<div class="main-subtitle">
    <h5>{{ __('leases') }}</h5>
    <a class="info-statuses leases-statuses">INFO</a>
</div>

<div class="nr-items">
    <h5 id="leasesCount"></h5>

    <div class="sortbysearch">
        <div class="zIndex-10000 filter-dropdown">
            <select class="form-select table-filter-select" aria-label="Default select example" id="statusSelect">
                <option selected value="all">Sort by: {{ __('All') }}</option>
                <option value="1">Sort by: {{ __('Active') }}</option>
                <option value="0">Sort by: {{ __('Inactive') }}</option>
            </select>
        </div>
        <div class="ms-auto lg-search h-40px pl-10">
            <input class="typeahead form-control" id="leases-search" type="text" name="lagree-search" placeholder="Search"autocomplete="off">
            <span class="lg-search-ico" onclick="$(this).prev().toggleClass('lg-search-expanded');"><img src="/search.svg" alt="search" class=""></span>
        </div>
    </div>
</div>

<div id="leases-table" class="entity-table bg--loading-black"></div>

<script type="module">
    const searchInput = $('#leases-search');
    const searchIcon = $('.lg-search-ico');
    const sortIcon = $('.sort-icon');
    let searchData = '';
    let orderParam = '';
    let orderType = 'desc';
    const leasesTable = $('#leases-table');
    const html = $('html, body');

    @php
        $route = route('admin.leases.search', $customer);
    @endphp

    function fetchData(url) {
        let perPage = $('.pagination-select').val() ?? 10;
        let selectedValue = $('#statusSelect').val();
        $.ajax({
            url: url,
            data: {
                status: selectedValue,
                search_data: searchData,
                order_param: orderParam,
                order_type: orderType,
                per_page: perPage,
            },
            success: function (data) {
                setTimeout(function () {
                    leasesTable.removeClass('bg--loading-black').html(data);
                    setSortIcon();
                    // leasesTable.trigger('resultLoaded');
                    $('.pagination-select').val(perPage);
                }, 300);
            },
            error: function () {
                flasherJS.error('', 'Error occurred while loading data.');
            }
        });
    }

    $('body').on('change', '#statusSelect', function (e) {
        fetchData("{{ $route }}");
    });

    $('body').on('change', '.pagination-select', function (e) {
        fetchData("{{ $route }}");
    });

    $('body').on('click', '.pagination a', function (e) {
        e.preventDefault();
        const url = $(this).attr('href');
        fetchData(url);
    });

    searchInput.on('input', debounce(function () {
        searchData = $(this).val();
        fetchData("{{ $route }}");
    }, 500));

    searchInput.on('click focus', function(e){
        e.stopPropagation();
    });

    $('body').on('click', '.sortable-list-header', function () {
        const newOrderParam = $(this).data('sort');
        if (orderParam === newOrderParam) {
            // Toggle sorting direction if the same column is clicked
            orderType = orderType === 'asc' ? 'desc' : 'asc';
        } else {
            // Set new sort column and default direction to ascending
            orderParam = newOrderParam;
            orderType = 'asc';
        }
        fetchData("{{ $route }}");
    });

    function debounce(func, wait, immediate) {
        let timeout;
        return function () {
            let context = this, args = arguments;
            let later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            let callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    function setSortIcon() {
        sortIcon.removeClass('asc desc'); // Remove existing sort classes
        $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(orderType); // Add active sort class
    }

    searchIcon.unbind('click').on('click', function (e) {
        console.log(searchInput);

        e.stopPropagation();
        // searchInput.get(0).classList.toggle("lg-search-expanded");

        if (searchData !== '' && !searchInput.get(0).classList.contains("lg-search-expanded")) {
            searchInput.val('');
            searchData = '';
            fetchData("{{ $route }}");
        }else{
            searchInput.focus();
        }
    });

    html.click(function (e) {
        e.stopPropagation();
        searchInput.get(0).classList.remove("lg-search-expanded");
        searchInput.val('');
        searchData = '';
    });

    // Initial load
    fetchData("{{ $route }}");
</script>


<div id="popup-agr-overlay" class="statuses-wrap leases-statuses-wrap">
<div id="popup-agr-body">
    <div class="modal-header">
        <h1 class="modal-title" id="">AGREEMENT - STATUSES</h1>
        <button type="button" onclick="hidePopup()" class="btn-close position-absolute" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    
<div class="popup-tabs">
  <div class="popup-tab-content active">
    <div class="status-box">
        <div class="status-txt">
            <p class="colorYellow">License Sent (0/2)</p>
            <span>The agreement was sent to the customer for signing</span>
        </div>
        <div class="circle-box orange"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p>Signed 1/2</p>
            <span>The agreement is signed by the customer</span>
        </div>
        <div class="circle-box black"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p>Signed 2/2 (wait4pay)</p>
            <span>The agreement is signed by both parties</span>
        </div>
        <div class="circle-box black"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-blue">Awaiting Delivery (paid)</p>
            <span>The deposit has been paid, and the customer is awaiting delivery</span>
        </div>
        <div class="circle-box blue"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-success">Active</p>
            <span>Machines delivered. Monthly payments begin</span>
        </div>
        <div class="circle-box green"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-secondary">Inactive</p>
            <span>The lease is inactive</span>
        </div>
        <div class="circle-box grey"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Canceled</p>
            <span>The lease has been cancelled</span>
        </div>
        <div class="circle-box red"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Payment failed</p>
            <span>The transaction attempt has failed</span>
        </div>
        <div class="circle-box red"></div>
    </div>
  </div>
</div>
</div>
</div>

<script>
 //leases INFO popup show/hide
 $(document).ready(function() {
    $('.leases-statuses').click(function() {
    $('.leases-statuses-wrap').addClass('show');
    });
    $('#popup-agr-body .modal-header .btn-close').click(function() {
    $('.statuses-wrap').removeClass('show');
    });
});
</script>