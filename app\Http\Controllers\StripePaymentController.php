<?php

namespace App\Http\Controllers;

use App\Helpers\Constants;
use App\Models\Agreement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;
// use Illuminate\Contracts\View\View;
// use Illuminate\Contracts\View\Factory;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\PaymentIntent;
use App\Models\License;
use App\Models\Lease;
use App\Models\Payment;
use App\Models\Purchase;
use App\Models\Machine;
use App\Models\Customer;
use App\Models\User;
use App\Mail\PaymentLinkEmail;

class StripePaymentController extends Controller
{
    public function show()
    {
        return view('payment');
    }

    public function paymentIntent(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        // Enable SSL verification for security
        Stripe::setVerifySslCerts(false);
        // Stripe::setVerifySslCerts(true);

        // Validate input parameters
        $request->validate([
            'amount' => 'required|integer|min:50|max:********', // Min $0.50, Max $100,000
            'email' => 'required|email|max:255'
        ]);

        $amount = $request->input('amount');
        $email = $request->input('email');
        $customerId = null;

        if ($email) {
            $customer = \Stripe\Customer::create(['email' => $email]);
            $customerId = $customer->id;
        }

        $params = [
            'amount' => $amount,
            'currency' => 'usd',
            'payment_method_types' => ['card', 'us_bank_account'],
        ];
        if (!empty($customerId)) {
            $params['customer'] = $customerId;
            $params['setup_future_usage'] = 'off_session';
        }

        $paymentIntent = PaymentIntent::create($params);

        return response()->json(['clientSecret' => $paymentIntent->client_secret, 'paymentIntent' => $paymentIntent, 'customerId' => $customerId]);

        // echo json_encode(['clientSecret' => $paymentIntent->client_secret]);
    }

    public function stripeForm(string $type, string $id)
    {
        // Validate input parameters
        if (!in_array($type, ['purchase', 'license', 'lease'])) {
            abort(404, 'Invalid payment type');
        }

        // Validate hash format
        if (!preg_match('/^IMS[A-F0-9]{128}$/', $id)) {
            abort(404, 'Invalid payment link');
        }

        switch ($type) {
            case 'purchase':
                $model = Purchase::all()->first(function ($model) use ($id) {
                    $expected = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $model->id))));
                    return $expected === $id;
                });

                if (!$model) {
                    \Log::warning('Invalid purchase payment link accessed', ['id' => $id, 'ip' => request()->ip()]);
                    abort(404, 'Payment not found');
                }

                $customer = Customer::where('id', $model->customer_id)->first();
                $machine = Machine::where('id', $model->machine_id)->first();
                $object_of_payment = $machine->name;

                // first next payment
                $payment = Payment::where(['purchase_id' => $model->id, 'status' => '0'])->orderBy('id', 'asc')->first();
                $allPayments = Payment::where('purchase_id', $model->id)->get();
                $next_payment = Payment::where(['purchase_id' => $model->id])->orderBy('id', 'desc')->skip(1)->first();
                $last_payment = Payment::where(['purchase_id' => $model->id])->orderBy('id', 'desc')->first();


                if($last_payment AND $last_payment->status == 1) {
                    $payment = $last_payment;
                    return view('payment.already-paid', compact('payment', 'model', 'customer', 'allPayments', 'type'));
                }else{
                    $payments = Payment::where(['purchase_id' => $model->id])->orderBy('id', 'desc')->first();
                }
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
                $currencySymbol = $model->initialCurrency->symbol;

                $model->checkoutRow = $model->machine->name . " - $" . number_format($model->machine_price * $model->machine_quantity / $conversionRate / 100, 2);
                $model->qtyRow = $model->machine_quantity;
                $model->rowTotal = "$" . number_format($model->machine_price / $conversionRate / 100, 2);
                $model->total = "$" . number_format($model->machine_price * $model->machine_quantity / $conversionRate / 100, 2);
                $model->grandTotal = "$" . number_format(($model->machine_price * $model->machine_quantity - $model->deposit_amount * $model->machine_quantity) / $conversionRate / 100, 2);

                return view('payment.form', compact('payment', 'allPayments', 'next_payment', 'object_of_payment', 'model', 'customer', 'conversionRate', 'currencySymbol', 'type'));

            case 'license':
                $model = License::all()->first(function ($model) use ($id) {
                    $expected = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $model->id))));
                    return $expected === $id;
                });

                if (!$model) {
                    \Log::warning('Invalid license payment link accessed', ['id' => $id, 'ip' => request()->ip()]);
                    abort(404, 'Payment not found');
                }

                $customer = Customer::where('id', $model->customer_id)->first();
                $payment = Payment::where('license_id', $model->id)->first();
                $allPayments = Payment::where('license_id', $model->id)->get();
                // first next payment
                $payment = Payment::where(['license_id' => $model->id, 'status' => '0'])->orderBy('id', 'asc')->first();
                $allPayments = Payment::where('license_id', $model->id)->get();
                $next_payment = Payment::where(['license_id' => $model->id])->orderBy('id', 'desc')->skip(1)->first();
                $last_payment = Payment::where(['license_id' => $model->id])->orderBy('id', 'desc')->first();


                if($last_payment AND $last_payment->status == 1) {
                    $payment = $last_payment;
                    return view('payment.already-paid', compact('payment', 'model', 'customer', 'allPayments', 'type'));
                }else{
                    $payments = Payment::where(['license_id' => $model->id])->orderBy('id', 'desc')->first(); 
                }
                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
                $currencySymbol = $model->initialCurrency->symbol;

                $object_of_payment = Constants::LICENSE_PACKAGES_KEYS[$model->package];
                $model->checkoutRow = $object_of_payment . " - $" . number_format($model->price / $conversionRate / 100, 2) . '/year';
                $model->qtyRow = NULL;
                $model->rowTotal = "$" . number_format($model->price / $conversionRate / 100, 2);
                $model->total = "$" . number_format($model->price / $conversionRate / 100, 2);
                $model->grandTotal = "$" . number_format(($model->price - $model->deposit_amount) / $conversionRate / 100, 2);

                return view('payment.form', compact('payment', 'allPayments', 'next_payment', 'object_of_payment', 'model', 'customer', 'conversionRate', 'currencySymbol', 'type'));

            case 'lease':
                $model = Lease::all()->first(function ($model) use ($id) {
                    $expected = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $model->id))));
                    return $expected === $id;
                });

                if (!$model) {
                    \Log::warning('Invalid lease payment link accessed', ['id' => $id, 'ip' => request()->ip()]);
                    abort(404, 'Payment not found');
                }

                $customer = Customer::where('id', $model->customer_id)->first();
                $machine = Machine::where('id', $model->machine_id)->first();
                $object_of_payment = $machine->name;

                // Get the next unpaid payment (monthly installment)
                $payment = Payment::where(['lease_id' => $model->id, 'status' => '0'])->orderBy('payment_number', 'asc')->first();
                $allPayments = Payment::where('lease_id', $model->id)->get();
                $next_payment = Payment::where(['lease_id' => $model->id])->orderBy('id', 'desc')->skip(1)->first();
                $last_payment = Payment::where(['lease_id' => $model->id])->orderBy('payment_number', 'desc')->first();

                // Check if all payments are completed
                if($last_payment AND $last_payment->status == 1 AND !$payment) {
                    return view('payment.already-paid', compact('last_payment', 'model', 'customer', 'allPayments', 'type'));
                }

                // If no unpaid payment found, something is wrong
                if (!$payment) {
                    abort(404, 'No pending payments found for this lease');
                }

                $conversionRate = $model->conversionRate()->where('currency_id', $model->initial_currency_id)->first()->rate;
                $currencySymbol = $model->initialCurrency->symbol;

                // Format display information for lease monthly payment
                // $model->checkoutRow = $machine->name . " - Monthly Installment #" . $payment->payment_number;
                $model->checkoutRow = $machine->name . " Lease - $" . number_format($model->monthly_installment * $model->machine_quantity / $conversionRate / 100, 2) . '/month';
                $model->qtyRow = $model->machine_quantity;
                $model->rowTotal = "$" . number_format($model->monthly_installment * $model->machine_quantity * $model->duration / $conversionRate / 100, 2);
                $model->total = "$" . number_format($model->monthly_installment * $model->machine_quantity * $model->duration / $conversionRate / 100, 2);
                $model->grandTotal = "$" . number_format($payment->payment_amount / $conversionRate / 100, 2);

                return view('payment.form', compact('payment', 'allPayments', 'next_payment', 'object_of_payment', 'model', 'customer', 'conversionRate', 'currencySymbol', 'type'));

            default:
                # code...
                break;
        }

    }

    // ------------------------------ OLD ----------------------------------

    public function stripeLicenseDepositLink($paymentId)
    {
        $payment = Payment::findOrFail($paymentId);
        $license = License::findOrFail($payment->license_id);
        $customer = Customer::findOrFail($license->customer_id);
        $owner = User::findOrFail($customer->owner_id);
        Stripe::setApiKey(env('STRIPE_SECRET'));
        // Stripe::setVerifySslCerts(true);
        Stripe::setVerifySslCerts(false);

        $session = Session::create([
            'payment_method_types' => ['card'],
            // 'ui_mode' => 'custom',
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => 'License Deposit Payment - ' . $customer->name . ", $customer->address, $customer->city",
                    ],
                    'unit_amount' => $payment->payment_amount,
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            // 'return_url' => route('payment.cancel'),
            'success_url' => route('payment.licenseSuccess') . '?type=license&license_id=' . $license->id . '&session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => route('payment.licenseCancel') . '?type=license&license_id=' . $license->id . '&session_id={CHECKOUT_SESSION_ID}',
        ]);

        Mail::to($owner->email)->send(new PaymentLinkEmail($session->url));
        return response()->json([
            'success' => true,
            'message' => 'Payment link generated and sent successfully!',
            'payment_url' => $session->url,
            'clientSecret' => $session->client_secret
        ]);
    }

    public function retrieveSession($sessionId)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        Stripe::setVerifySslCerts(false);
        // Stripe::setVerifySslCerts(true);
        if($sessionId != null AND $sessionId != ''){
            $session = Session::retrieve(
                $sessionId,
                []
            );

            $response = [
                'success' => true,
                'message' => 'Payment status',
                'status' => $session->status,
                'currency' => $session->currency,
                'amount_total' => "$" . number_format($session->amount_total / 100, 2)
            ];
            return $response;
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Payment link not found'
            ]);
        }
    }
    public function retrievePaymentIntent($PaymentIntentId)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        Stripe::setVerifySslCerts(false);
        // Stripe::setVerifySslCerts(true);
        if($PaymentIntentId != null AND $PaymentIntentId != ''){
            $session = PaymentIntent::retrieve(
                $PaymentIntentId,
                []
            );

            $response = [
                'success' => true,
                'message' => 'Payment status',
                'status' => $session->status,
                'currency' => $session->currency,
                'amount_total' => "$" . number_format($session->amount_total / 100, 2)
            ];
            return $response;
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Payment link not found'
            ]);
        }
    }
    public function stripePurchaseLink($paymentId)
    {
        $payment = Payment::findOrFail($paymentId);
        $purchase = Purchase::findOrFail($payment->purchase_id);
        $machine = Machine::findOrFail($payment->machine_id);
        $customer = Customer::findOrFail($payment->customer_id);
        $owner = User::findOrFail($customer->owner_id);
        Stripe::setApiKey(env('STRIPE_SECRET'));
        Stripe::setVerifySslCerts(false);
        // Stripe::setVerifySslCerts(true) ;

        $session = Session::create([
            'payment_method_types' => ['card', 'bank_transfer'],
            // 'ui_mode' => 'custom',
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => 'Machine Purchase - ' . $machine->name,
                    ],
                    'unit_amount' => $payment->payment_amount,
                ],
                // 'price' => $payment->payment_amount,
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            // 'return_url' => url('/') . '?session_id={CHECKOUT_SESSION_ID}',
            'success_url' => route('payment.purchaseSuccess') . '?type=purchase&purchase_id=' . $purchase->id . '&session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => route('payment.purchaseCancel') . '?type=purchase&purchase_id=' . $purchase->id . '&session_id={CHECKOUT_SESSION_ID}',
        ]);

        // echo '<pre>';
        // print_r($session->toArray());
        // echo '<br>';
        // echo '<br>';
        // print_r($session->url);
        // die();

        Mail::to($owner->email)->send(new PaymentLinkEmail($session->url));
        return response()->json([
            'success' => true,
            'message' => 'Payment link generated and sent successfully!',
            'payment_url' => $session->url,
            'clientSecret' => $session->client_secret
        ]);
    }

    public function  processPayment(Request $request)
    {
        // Validate input data
        $request->validate([
            'type' => 'required|in:purchase,license,lease',
            'payment_id' => 'required|integer|exists:payments,id',
            'paymentIntent_id' => 'required|string|max:255',
            'status' => 'required|string|in:succeeded,processing,requires_action,canceled,requires_payment_method',
            'purchase_id' => 'required_if:type,purchase|integer|exists:purchase,id',
            'license_id' => 'required_if:type,license|integer|exists:licenses,id',
            'lease_id' => 'required_if:type,lease|integer|exists:leases,id',
        ]);

        // Verify payment belongs to the specified model
        $payment = Payment::findOrFail($request->input('payment_id'));
        $this->validatePaymentOwnership($payment, $request);

        // Update purchase status to 1 (active)
        if($request->has('type') AND $request->input('type') == 'purchase'){
            if($request->has('purchase_id') AND ($request->input('purchase_id') != '' OR $request->input('purchase_id') != null)){
                $model = Purchase::findOrFail($request->input('purchase_id'));
                $agreement = Agreement::where('purchase_id', $request->input('purchase_id'));
                $customer = Customer::findOrFail($model->customer_id);

                $payment = Payment::where('id', $request->input('payment_id'))->first();
                switch ($request->input('status')) {
                    case 'succeeded':
                        $payment->status = '1'; // payment succeeded
                        $payment->save();
                        if($payment->description != 'Deposit'){
                            $model->status = '2';
                            $agreement->update([
                                'status' => Agreement::STATUSES['Awaiting delivery (paid)'],
                            ]);
                        }else{
                            $model->status = '12';
                            $agreement->update([
                                'status' => Agreement::STATUSES['Deposit Paid'],
                            ]);
                        }
                        $model->save();
                        break;

                    case 'processing':
                        $payment->status = '3'; // payment is processing (for bank transfers can take 2-3 days), can't be canceled
                        $payment->save();
                        $model->status = '9';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Processing'],
                        ]);
                        break;

                    case 'requires_action':
                        $payment->status = '0'; // payment requires action like 3D secure
                        $payment->save();
                        $model->status = '11';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment requires action'],
                        ]);
                        break;

                    case 'canceled':
                        $payment->status = '0'; // payment canceled by user
                        $payment->save();
                        $model->status = '2';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Canceled'],
                        ]);
                        break;

                    case 'requires_payment_method':
                        $payment->status = '0'; // payment failed but can try again with different payment method
                        $payment->save();
                        $model->status = '10';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment failed'],
                        ]);
                        break;

                    default:
                        # code...
                        break;
                }
                $payment->paymentIntent_id = $request->input('paymentIntent_id');
                $payment->stripe_session_id = $request->input('stripe_session_id');
                $payment->stripeCustomer_id = $request->input('stripeCustomer_id');
                $payment->save();
            }
        }else if($request->has('type') AND $request->input('type') == 'license'){
            if($request->has('license_id') AND ($request->input('license_id') != '' OR $request->input('license_id') != null)){
                $model = License::findOrFail($request->input('license_id'));
                $agreement = Agreement::where('license_id', $request->input('license_id'));
                $customer = Customer::findOrFail($model->customer_id);

                $payment = Payment::where('id', $request->input('payment_id'))->first();
                switch ($request->input('status')) {
                    case 'succeeded':
                        $payment->status = '1'; // payment succeeded
                        $payment->save();
                        if($payment->description != 'Deposit'){
                            $model->status = '3';
                            $agreement->update([
                                'status' => Agreement::STATUSES['Active'],
                            ]);
                        }else{
                            $model->status = '12';
                            $agreement->update([
                                'status' => Agreement::STATUSES['Deposit Paid'],
                            ]);
                        }
                        $model->save();
                        break;

                    case 'processing':
                        $payment->status = '3'; // payment is processing (for bank transfers can take 2-3 days), can't be canceled
                        $payment->save();
                        $model->status = '9';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Processing'],
                        ]);
                        break;

                    case 'requires_action':
                        $payment->status = '0'; // payment requires action like 3D secure
                        $payment->save();
                        $model->status = '11';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment requires action'],
                        ]);
                        break;

                    case 'canceled':
                        $payment->status = '0'; // payment canceled by user
                        $payment->save();
                        $model->status = '6';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Canceled'],
                        ]);
                        break;

                    case 'requires_payment_method':
                        $payment->status = '0'; // payment failed but can try again with different payment method
                        $payment->save();
                        $model->status = '10';
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment failed'],
                        ]);
                        break;

                    default:
                        # code...
                        break;
                }
                $payment->paymentIntent_id = $request->input('paymentIntent_id');
                $payment->stripe_session_id = $request->input('stripe_session_id');
                $payment->stripeCustomer_id = $request->input('stripeCustomer_id');
                $payment->save();
            }
        }else if($request->has('type') AND $request->input('type') == 'lease'){
            if($request->has('lease_id') AND ($request->input('lease_id') != '' OR $request->input('lease_id') != null)){
                $model = Lease::findOrFail($request->input('lease_id'));
                $agreement = Agreement::where('lease_id', $request->input('lease_id'));
                $customer = Customer::findOrFail($model->customer_id);

                $payment = Payment::where('id', $request->input('payment_id'))->first();
                switch ($request->input('status')) {
                    case 'succeeded':
                        $payment->status = '1'; // payment succeeded
                        $payment->save();

                        if($model->status != 2){
                            $model->status = '2'; // Awaiting Delivery
                            $model->save();
                            $agreement->update([
                                'status' => Agreement::STATUSES['Awaiting delivery (paid)'],
                            ]);
                        }

                        break;

                    case 'processing':
                        $payment->status = '3'; // payment is processing (for bank transfers can take 2-3 days), can't be canceled
                        $payment->save();
                        $model->status = '9'; // Processing
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Processing'],
                        ]);
                        break;

                    case 'requires_action':
                        $payment->status = '0'; // payment requires action like 3D secure
                        $payment->save();
                        $model->status = '11'; // Requires action
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment requires action'],
                        ]);
                        break;

                    case 'canceled':
                        $payment->status = '0'; // payment canceled by user
                        $payment->save();
                        $model->status = '6'; // Canceled
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Canceled'],
                        ]);
                        break;

                    case 'requires_payment_method':
                        $payment->status = '0'; // payment failed but can try again with different payment method
                        $payment->save();
                        $model->status = '10'; // Failed
                        $model->save();
                        $agreement->update([
                            'status' => Agreement::STATUSES['Payment failed'],
                        ]);
                        break;

                    default:
                        # code...
                        break;
                }
                $payment->paymentIntent_id = $request->input('paymentIntent_id');
                $payment->stripe_session_id = $request->input('stripe_session_id');
                $payment->stripeCustomer_id = $request->input('stripeCustomer_id');
                $payment->save();
            }
        }

        $response = [];
        if($request->has('paymentIntent_id') AND ($request->input('paymentIntent_id') != '' OR $request->input('paymentIntent_id') != null)){
            $response = self::retrievePaymentIntent($request->input('paymentIntent_id'));
            // $response = self::retrieveSession($request->input('session_id'));
        }

        return response()->json(['response' => $response, 'payment' => $payment, 'model' => $model, 'customer' => $customer]);
    }
    public function successPayment(string $payment_id)
    {
        $payment = Payment::findOrFail($payment_id);
        $customer = Customer::findOrFail($payment->customer_id);
        $typeObject = null;
        $type = null;

        if($payment->purchase_id) {
            $type = 'purchase';
            $typeObject = Purchase::findOrFail($payment->purchase_id);
            $typeObject->what_is_paid = $typeObject->machine->name;
        }else if($payment->license_id) {
            $type = 'license';
            $typeObject = License::findOrFail($payment->license_id);
            $typeObject->what_is_paid = Constants::LICENSE_PACKAGES_KEYS[$typeObject->package];
        }else if($payment->lease_id) {
            $type = 'lease';
            $typeObject = Lease::findOrFail($payment->lease_id);
            $typeObject->what_is_paid = $typeObject->machine->name . " - Monthly Installment " . $payment->payment_number . '/' . $typeObject->duration;
        }

        return view('payment.success', compact('payment', 'typeObject', 'type', 'customer'));
    }

    public function testPayment()
    {
        $payment = Payment::findOrFail(1333);
        $customer = Customer::findOrFail($payment->customer_id);
        $typeObject = null;
        $type = null;

        if($payment->purchase_id) {
            $type = 'purchase';
            $typeObject = Purchase::findOrFail($payment->purchase_id);
        }else if($payment->license_id) {
            $type = 'license';
            $typeObject = License::findOrFail($payment->license_id);
        }else if($payment->lease_id) {
            $type = 'lease';
            $typeObject = Lease::findOrFail($payment->lease_id);
        }

        return view('payment.success', compact('payment', 'typeObject', 'type', 'customer'));
    }


    public function cancelLicenseDeposit(Request $request)
    {
        // Update purchase status to 1 (active)
        if($request->has('type') AND $request->input('type') == 'license'){
            if($request->has('license_id') AND ($request->input('license_id') != '' OR $request->input('license_id') != null)){
                $license = License::findOrFail($request->input('license_id'));
                $license->status = '0';
                $license->save();

                $payment = Payment::where('license_id', $request->input('license_id'))->first();
                $payment->status = '1';
                $payment->stripe_session_id = $request->input('session_id');
                $payment->save();
            }
        }

        return view('payment.cancel'); // You can create a success page
    }

    public function processCheckout(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        Stripe::setVerifySslCerts(false);
        // Stripe::setVerifySslCerts(true);

        $checkout_session = Session::create([
            'ui_mode' => 'custom',
            'line_items' => [[
              # Provide the exact Price ID (e.g. price_1234) of the product you want to sell
              'price' => '{{PRICE_ID}}',
              'quantity' => 1,
            ]],
            'mode' => 'payment',
            'return_url' => '/payment/success?session_id={CHECKOUT_SESSION_ID}',
        ]);

        echo json_encode(array('clientSecret' => $checkout_session->client_secret));
    }

    public function checkout(Request $request)
    {
        $settings = \auth()->user()->adminSettings;
        $admin_week_start = $settings->week_start === 'monday' ? 1 : 0;

        return view('payment.index', compact('admin_week_start')); // You can create a checkout index page
    }

    public function retreiveCheckout(Request $request)
    {

        return view('payment.checkout'); // You can create a success page
    }

    public function cancelPayment()
    {
        return view('payment.cancel'); // You can create a cancel page
    }

    /**
     * Validate that the payment belongs to the specified model
     */
    private function validatePaymentOwnership(Payment $payment, Request $request)
    {
        $type = $request->input('type');

        switch ($type) {
            case 'purchase':
                if ($payment->purchase_id !== (int)$request->input('purchase_id')) {
                    abort(403, 'Payment does not belong to this purchase');
                }
                break;
            case 'license':
                if ($payment->license_id !== (int)$request->input('license_id')) {
                    abort(403, 'Payment does not belong to this license');
                }
                break;
            case 'lease':
                if ($payment->lease_id !== (int)$request->input('lease_id')) {
                    abort(403, 'Payment does not belong to this lease');
                }
                break;
        }
    }

}
