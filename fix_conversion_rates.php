<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\License;
use App\Models\Lease;
use App\Models\Purchase;
use App\Models\Currency;
use App\Helpers\CurrencyHelper;

echo "=== Fixing Missing Conversion Rates ===\n\n";

try {
    $currencies = Currency::all();
    echo "Found " . $currencies->count() . " currencies\n\n";
    
    // Fix Licenses
    echo "1. Checking Licenses...\n";
    $licenses = License::all();
    $fixedLicenses = 0;
    
    foreach ($licenses as $license) {
        $existingRates = $license->conversionRate()->count();
        if ($existingRates === 0) {
            echo "  - License ID {$license->id}: Creating conversion rates\n";
            foreach ($currencies as $currency) {
                $license->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }
            $fixedLicenses++;
        }
    }
    echo "✓ Fixed $fixedLicenses licenses\n\n";
    
    // Fix Leases
    echo "2. Checking Leases...\n";
    $leases = Lease::all();
    $fixedLeases = 0;
    
    foreach ($leases as $lease) {
        $existingRates = $lease->conversionRate()->count();
        if ($existingRates === 0) {
            echo "  - Lease ID {$lease->id}: Creating conversion rates\n";
            foreach ($currencies as $currency) {
                $lease->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }
            $fixedLeases++;
        }
    }
    echo "✓ Fixed $fixedLeases leases\n\n";
    
    // Fix Purchases
    echo "3. Checking Purchases...\n";
    $purchases = Purchase::all();
    $fixedPurchases = 0;
    
    foreach ($purchases as $purchase) {
        $existingRates = $purchase->conversionRate()->count();
        if ($existingRates === 0) {
            echo "  - Purchase ID {$purchase->id}: Creating conversion rates\n";
            foreach ($currencies as $currency) {
                $purchase->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }
            $fixedPurchases++;
        }
    }
    echo "✓ Fixed $fixedPurchases purchases\n\n";
    
    echo "=== Summary ===\n";
    echo "Fixed conversion rates for:\n";
    echo "- $fixedLicenses licenses\n";
    echo "- $fixedLeases leases\n";
    echo "- $fixedPurchases purchases\n\n";
    
    if ($fixedLicenses + $fixedLeases + $fixedPurchases > 0) {
        echo "✓ Adobe Sign should now work for new agreements!\n";
    } else {
        echo "✓ All records already have conversion rates\n";
    }
    
    echo "\nNow try creating a new license to test Adobe Sign integration.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
