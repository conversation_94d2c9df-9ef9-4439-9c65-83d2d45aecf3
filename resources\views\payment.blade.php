<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Stripe Embedded Payment Form</title>
<script src="https://js.stripe.com/v3/"></script>
<style>
#payment-form {
	width: 500px;
	background: #f7f7f7;
	padding: 20px;
}
#card-element {
	width: 460px;
	padding: 20px 0;
}
#submit {
	background: #000;
	border: none;
	color: #fff;
	padding: 10px 30px;
	border-radius: 40px;
	margin-top: 20px;
}
</style>
</head>
<body>
    <h2>Pay $10.00</h2>
    <form id="payment-form">
        <div id="card-element"></div>
        <button id="submit">Pay</button>
        <div id="payment-message"></div>
    </form>

    <script>
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        const form = document.getElementById('payment-form');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            const { paymentMethod, error } = await stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
            });

            if (error) {
                document.getElementById('payment-message').textContent = error.message;
            } else {
                // Send paymentMethod.id to server
                fetch('/payment/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify({ payment_method_id: paymentMethod.id }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'succeeded') {
                        document.getElementById('payment-message').textContent = 'Payment successful!';
                    } else if (data.status === 'requires_action') {
                        // Handle 3D Secure authentication
                        stripe.confirmCardPayment(data.client_secret).then(function(result) {
                            if (result.error) {
                                document.getElementById('payment-message').textContent = result.error.message;
                            } else {
                                document.getElementById('payment-message').textContent = 'Payment successful!';
                            }
                        });
                    } else {
                        document.getElementById('payment-message').textContent = 'Payment failed.';
                    }
                });
            }
        });
    </script>
</body>
</html>
