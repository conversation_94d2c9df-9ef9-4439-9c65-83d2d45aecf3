<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\License;
use App\Models\Agreement;

echo "=== Recent License Analysis ===\n\n";

try {
    // Check recent licenses
    echo "Recent Licenses (last 10):\n";
    $recentLicenses = License::orderBy('created_at', 'desc')->limit(10)->get();
    
    foreach ($recentLicenses as $license) {
        $conversionRateCount = $license->conversionRate()->count();
        $agreementCount = $license->agreement()->count();
        
        echo "License ID: {$license->id}\n";
        echo "  Created: {$license->created_at}\n";
        echo "  Currency ID: " . ($license->initial_currency_id ?? 'NULL') . "\n";
        echo "  Conversion Rates: {$conversionRateCount}\n";
        echo "  Agreements: {$agreementCount}\n";
        
        if ($agreementCount > 0) {
            $agreement = $license->agreement()->latest()->first();
            echo "  Last Agreement: {$agreement->created_at} (Status: {$agreement->status})\n";
        }
        echo "\n";
    }
    
    // Check recent agreements
    echo "\nRecent Agreements (last 10):\n";
    $recentAgreements = Agreement::where('type', 'license')
        ->orderBy('created_at', 'desc')
        ->limit(10)
        ->get();
    
    foreach ($recentAgreements as $agreement) {
        echo "Agreement ID: {$agreement->id}\n";
        echo "  Created: {$agreement->created_at}\n";
        echo "  License ID: {$agreement->license_id}\n";
        echo "  Status: {$agreement->status}\n";
        echo "  Adobe Agreement ID: " . ($agreement->adobe_agreement_id ?? 'NULL') . "\n";
        echo "\n";
    }
    
    // Check for licenses created in July vs August
    echo "\nLicenses by Month:\n";
    $julyCount = License::whereYear('created_at', 2025)->whereMonth('created_at', 7)->count();
    $augustCount = License::whereYear('created_at', 2025)->whereMonth('created_at', 8)->count();
    
    echo "July 2025: {$julyCount} licenses\n";
    echo "August 2025: {$augustCount} licenses\n";
    
    // Check for successful agreements in July vs August
    $julyAgreements = Agreement::where('type', 'license')
        ->whereYear('created_at', 2025)
        ->whereMonth('created_at', 7)
        ->whereNotNull('adobe_agreement_id')
        ->count();
        
    $augustAgreements = Agreement::where('type', 'license')
        ->whereYear('created_at', 2025)
        ->whereMonth('created_at', 8)
        ->whereNotNull('adobe_agreement_id')
        ->count();
    
    echo "\nSuccessful Adobe Sign Agreements:\n";
    echo "July 2025: {$julyAgreements} agreements\n";
    echo "August 2025: {$augustAgreements} agreements\n";
    
    if ($augustAgreements == 0 && $augustCount > 0) {
        echo "\n⚠️  WARNING: Licenses created in August but NO successful Adobe Sign agreements!\n";
        echo "This confirms Adobe Sign stopped working in August.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
