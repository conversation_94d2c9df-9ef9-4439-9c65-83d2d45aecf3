<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Currency;
use App\Models\AdminSettings;
use App\Models\License;
use App\Models\Customer;
use App\Helpers\CurrencyHelper;

echo "=== License Creation Debug Test ===\n\n";

try {
    // Step 1: Check currencies and admin settings
    echo "1. Checking System Configuration:\n";
    
    $currencies = Currency::all();
    echo "   Currencies found: " . $currencies->count() . "\n";
    
    $settings = AdminSettings::first();
    if (!$settings) {
        echo "   ❌ No admin settings found!\n";
        exit(1);
    }
    
    echo "   Admin currency ID: {$settings->currency_id}\n";
    echo "   Admin currency: {$settings->currency->code}\n";
    
    // Check for problematic currencies
    $problemCurrencies = $currencies->filter(function($currency) {
        return $currency->rate == 0 || $currency->rate === null;
    });
    
    if ($problemCurrencies->count() > 0) {
        echo "   ❌ Found currencies with zero/null rates:\n";
        foreach ($problemCurrencies as $currency) {
            echo "      - {$currency->code}: " . ($currency->rate ?? 'NULL') . "\n";
        }
        exit(1);
    }
    
    echo "   ✓ All currencies have valid rates\n\n";
    
    // Step 2: Test conversion rate calculation
    echo "2. Testing Conversion Rate Calculations:\n";
    foreach ($currencies as $currency) {
        try {
            $rate = CurrencyHelper::calculateRate($currency);
            echo "   ✓ {$currency->code}: {$currency->rate} → {$rate}\n";
        } catch (Exception $e) {
            echo "   ❌ {$currency->code}: ERROR - " . $e->getMessage() . "\n";
            exit(1);
        }
    }
    echo "\n";
    
    // Step 3: Check recent license creation attempts
    echo "3. Analyzing Recent License Creation:\n";
    
    $recentLicenses = License::orderBy('created_at', 'desc')->limit(5)->get();
    
    foreach ($recentLicenses as $license) {
        echo "   License ID: {$license->id}\n";
        echo "   Created: {$license->created_at}\n";
        echo "   Initial Currency ID: " . ($license->initial_currency_id ?? 'NULL') . "\n";
        
        $conversionRates = $license->conversionRate()->get();
        echo "   Conversion Rates: " . $conversionRates->count() . "\n";
        
        if ($conversionRates->count() === 0) {
            echo "   ❌ This license has NO conversion rates!\n";
        } else {
            foreach ($conversionRates as $rate) {
                $currency = Currency::find($rate->currency_id);
                echo "      - {$currency->code}: {$rate->rate}\n";
            }
        }
        echo "\n";
    }
    
    // Step 4: Test what happens during license creation
    echo "4. Simulating License Creation Process:\n";
    
    echo "   Step 4a: Setting initial_currency_id = {$settings->currency_id}\n";
    
    echo "   Step 4b: Creating conversion rates:\n";
    foreach ($currencies as $currency) {
        try {
            $rate = CurrencyHelper::calculateRate($currency);
            echo "      Would create: currency_id={$currency->id}, rate={$rate}\n";
        } catch (Exception $e) {
            echo "      ❌ Would FAIL for currency {$currency->id}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // Step 5: Check if there's a database transaction issue
    echo "5. Checking for Transaction Issues:\n";
    
    // Look for licenses created recently but without conversion rates
    $licensesWithoutRates = License::whereDoesntHave('conversionRate')
        ->where('created_at', '>=', now()->subDays(30))
        ->get();
    
    if ($licensesWithoutRates->count() > 0) {
        echo "   ❌ Found " . $licensesWithoutRates->count() . " recent licenses WITHOUT conversion rates:\n";
        foreach ($licensesWithoutRates as $license) {
            echo "      - License ID {$license->id} (created: {$license->created_at})\n";
        }
        echo "   This suggests the license creation process is failing at the conversion rate step!\n";
    } else {
        echo "   ✓ All recent licenses have conversion rates\n";
    }
    
    echo "\n=== Summary ===\n";
    echo "If you see licenses without conversion rates above, the issue is in the license creation process.\n";
    echo "If all licenses have conversion rates, the issue might be in the Adobe Sign integration itself.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
