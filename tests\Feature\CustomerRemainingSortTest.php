<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\License;
use App\Models\Lease;
use App\Models\Payment;
use App\Models\User;
use App\Services\Admin\Customer\CustomerService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerRemainingSortTest extends TestCase
{
    use RefreshDatabase;

    protected CustomerService $customerService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user with admin role
        $user = User::factory()->create();
        $this->actingAs($user);
        
        $this->customerService = new CustomerService();
    }

    /** @test */
    public function it_can_sort_customers_by_remaining_amount()
    {
        // Create test customers with different remaining amounts
        $owner1 = User::factory()->create();
        $owner2 = User::factory()->create();
        $owner3 = User::factory()->create();
        
        $customer1 = Customer::factory()->create(['owner_id' => $owner1->id, 'name' => 'Customer A']);
        $customer2 = Customer::factory()->create(['owner_id' => $owner2->id, 'name' => 'Customer B']);
        $customer3 = Customer::factory()->create(['owner_id' => $owner3->id, 'name' => 'Customer C']);

        // Create licenses with different payment amounts
        $license1 = License::factory()->create(['customer_id' => $customer1->id, 'price' => 1000]);
        $license2 = License::factory()->create(['customer_id' => $customer2->id, 'price' => 2000]);
        $license3 = License::factory()->create(['customer_id' => $customer3->id, 'price' => 500]);

        // Create payments (partial payments to create remaining amounts)
        Payment::factory()->create(['license_id' => $license1->id, 'amount' => 300]); // Remaining: 700
        Payment::factory()->create(['license_id' => $license2->id, 'amount' => 500]); // Remaining: 1500
        Payment::factory()->create(['license_id' => $license3->id, 'amount' => 400]); // Remaining: 100

        // Test ascending sort
        $response = $this->get(route('admin.customers.search', [
            'search_data' => '',
            'order_param' => 'remaining',
            'order_type' => 'asc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        
        // Test descending sort
        $response = $this->get(route('admin.customers.search', [
            'search_data' => '',
            'order_param' => 'remaining',
            'order_type' => 'desc',
            'per_page' => 10
        ]));

        $response->assertStatus(200);
        
        // The response should contain the customers in the correct order
        // This is a basic test to ensure the sorting doesn't break the functionality
        $this->assertTrue(true, 'Remaining sort functionality works without errors');
    }

    /** @test */
    public function it_handles_remaining_sort_parameter_in_service()
    {
        // Create test data
        $owner = User::factory()->create();
        $customer = Customer::factory()->create(['owner_id' => $owner->id, 'type' => 'studio']);

        // Test that the service handles 'remaining' as order parameter
        $result = $this->customerService->search('', 'remaining', 'asc', 10, 'all', 'studio');

        $this->assertNotNull($result);
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $result);
    }
}
