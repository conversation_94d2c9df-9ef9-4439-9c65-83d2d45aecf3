<?php

use App\Http\Controllers\Admin\AdobeWebhookController;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Route::get('/', function(){
//     return "home";
// });

// Route::get('admin/customers/b2c', function () {
//     return 'working!';
// });

Route::get('/', function () {
    // if (\auth()->user()) {
    //     return redirect(RouteServiceProvider::HOME);
    // }
    return view('auth.login');
});

Route::get('/new-account', function () {
    return view('auth.new-account');
});

Route::get('/signup', function () {
    $selection = request('type');
    return view('auth.signup', compact('selection'));
});

Route::get('/all-set', function () {
    return view('auth.all-set');
})->name('signup.all-set');

Route::get('/signin', function () {
    return view('auth.login');
});
Route::post('/signup', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'store'])->name('signup.store');

// Route::get('/', function(){
//     return "hello world";
// });
//
//Route::get('/styleguide', function () {
//    return view('styleguide');
//})->middleware(['auth', 'verified'])->name('styleguide');
//
Route::get('test_templates', [AdobeWebhookController::class, 'getTemplates'])->name('test_templates');
Route::get('test_adobe_html', [\App\Http\Controllers\Admin\CustomerController::class, 'adobeHtmlSend'])->name('adobe_html');
Route::get('view_agreement_html', [\App\Http\Controllers\Admin\CustomerController::class, 'viewAgreementHtml'])->name('adobe_html');
//Route::get('adobe-sign/webhook', [AdobeSignController::class, 'adobeWebHook'])->name('adobe-sign-webhook');
//Route::post('adobe-sign/webhook', [AdobeSignController::class, 'getWebHookData'])->name('get-adobe-sign-webhook-data');
//Route::get('/dashboard', [DashboardController::class, 'dashboard'])->middleware(['auth', 'verified'])->name('dashboard');
// Route::get('admin/newpass', [\App\Http\Controllers\Admin\DashboardController::class, 'newpass'])->name('newpass');

// Route::post('api/webhooks/woocommerce', [WooCommerceWebhookController::class, 'handle'])->prefix('api')->name('webhooks.woocommerce');
Route::any('sync/test_convert', [\App\Http\Controllers\SyncController::class, 'test_convert'])->name('sync.test_convert');
Route::any('sync/getUpdatedProducts', [\App\Http\Controllers\SyncController::class, 'getUpdatedProducts'])->name('sync.getUpdatedProducts');

Route::middleware(['auth', 'role:' . \App\Helpers\Constants::ROLES, 'set.timezone', 'set.date.format', 'set.week.start', 'set.currency'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('dashboard/payments/all', [\App\Http\Controllers\Admin\DashboardController::class, 'getAllPayments'])->name('dashboard.payments.all');
    Route::get('dashboard/payments/search', [\App\Http\Controllers\Admin\DashboardController::class, 'searchPayments'])->name('dashboard.payments.search');

    // Customers
    Route::get('customers', [\App\Http\Controllers\Admin\CustomerController::class, 'index'])->name('customers.index');
    Route::get('customers/create', [\App\Http\Controllers\Admin\CustomerController::class, 'create'])->name('customers.create');
    Route::post('customers', [\App\Http\Controllers\Admin\CustomerController::class, 'store'])->name('customers.store');
    Route::get('customers-search', [\App\Http\Controllers\Admin\CustomerController::class, 'search'])->name('customers.search');
    Route::get('customers/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'show'])->name('customers.show');
    Route::get('customers/{customer}/edit', [\App\Http\Controllers\Admin\CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('customers/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'update'])->name('customers.update');
    Route::delete('customers/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'destroy'])->name('customers.destroy');
    Route::post('customers/delete-multiple', [\App\Http\Controllers\Admin\CustomerController::class, 'deleteMultiple'])->name('customers.delete-multiple');
    Route::get('customers/{customer}/load-tab-content', [\App\Http\Controllers\Admin\CustomerController::class, 'loadTabContent'])->name('customers.load-tab-content');
    Route::get('customers-dashboard/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'showDashboard'])->name('customers.dashboard');
    Route::post('customers/{customer}/contact', [\App\Http\Controllers\Admin\CustomerController::class, 'contactCustomer'])->name('customers.contact');
    Route::post('customers/get-customer', [\App\Http\Controllers\Admin\CustomerController::class, 'getCustomer'])->name('customers.getCustomer');

    // Customer Categories - studio
    Route::get('customer/studio', [\App\Http\Controllers\Admin\CustomerController::class, 'studio'])->name('customers.studio.index');
    Route::get('customers/studio/create', [\App\Http\Controllers\Admin\CustomerController::class, 'studio_create'])->name('customers.studio.create');
    Route::post('customers/studio', [\App\Http\Controllers\Admin\CustomerController::class, 'studio_store'])->name('customers.studio.store');
    Route::put('customers-studio/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'update'])->name('customers.studio.update');

    // Customer Categories - b2c
    Route::get('customer/b2c', [\App\Http\Controllers\Admin\CustomerController::class, 'b2c'])->name('customers.b2c.index');
    Route::get('customers/b2c/create', [\App\Http\Controllers\Admin\CustomerController::class, 'b2c_create'])->name('customers.b2c.create');
    Route::post('customers/b2c', [\App\Http\Controllers\Admin\CustomerController::class, 'b2c_store'])->name('customers.b2c.store');
    Route::put('customers-b2c/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'b2c_update'])->name('customers.b2c.update');
    Route::get('customers-b2c-dashboard/{customer}', [\App\Http\Controllers\Admin\CustomerController::class, 'showDashboard'])->name('customers.b2c.dashboard');
    Route::get('customers-b2c/{customer}/edit', [\App\Http\Controllers\Admin\CustomerController::class, 'edit'])->name('customers.b2c.edit');
    Route::get('customers-search/b2c', [\App\Http\Controllers\Admin\CustomerController::class, 'search'])->name('customers-b2c.search');

    Route::get('customer/trainers', function () {
        return "Hello world";
    })->name('customers.trainers');
    Route::get('customers/master-trainers', function () {
        return "Hello world";
    })->name('customers.master-trainers');
    // Custom agreement
    Route::post('customers/{customer}/agreements', [\App\Http\Controllers\Admin\CustomerController::class, 'store_agreements'])->name('customers.store.agreements');
    Route::get('customer/{customer}/search-agreeements', [\App\Http\Controllers\Admin\CustomerController::class, 'search_agreements'])->name('customers.search.agreements');
    Route::post('/agreements/toggle-status', [\App\Http\Controllers\Admin\CustomerController::class, 'toggleStatus'])->name('customers.agreements.toggle');
    Route::post('/agreements/download', [\App\Http\Controllers\Admin\CustomerController::class, 'agreement_download'])->name('customer.agreements.download');
    Route::delete('/agreements/agreement_delete', [\App\Http\Controllers\Admin\CustomerController::class, 'agreement_delete'])->name('customer.agreements.delete');

    // Machines
    Route::get('machines', [\App\Http\Controllers\Admin\MachineController::class, 'index'])->name('machines.index');
    Route::get('machines/create', [\App\Http\Controllers\Admin\MachineController::class, 'create'])->name('machines.create');
    Route::post('machines', [\App\Http\Controllers\Admin\MachineController::class, 'store'])->name('machines.store');
    Route::get('machines-search', [\App\Http\Controllers\Admin\MachineController::class, 'search'])->name('machines.search');
    Route::get('machines/{machine}', [\App\Http\Controllers\Admin\MachineController::class, 'show'])->name('machines.show');
    Route::get('machines/{machine}/edit', [\App\Http\Controllers\Admin\MachineController::class, 'edit'])->name('machines.edit');
    Route::put('machines/{machine}', [\App\Http\Controllers\Admin\MachineController::class, 'update'])->name('machines.update');
    Route::delete('machines/{machine}', [\App\Http\Controllers\Admin\MachineController::class, 'destroy'])->name('machines.destroy');
    Route::post('/machines/delete-multiple', [\App\Http\Controllers\Admin\MachineController::class, 'deleteMultiple'])->name('machines.delete-multiple');
    Route::post('/machines/get-machine', [\App\Http\Controllers\Admin\MachineController::class, 'get_machine'])->name('machines.get-machine');

    // Product
    Route::get('products', [\App\Http\Controllers\Admin\ProductsController::class, 'index'])->name('products.index');
    Route::get('products/create', [\App\Http\Controllers\Admin\ProductsController::class, 'create'])->name('products.create');
    Route::post('products', [\App\Http\Controllers\Admin\ProductsController::class, 'store'])->name('products.store');
    Route::get('products-search', [\App\Http\Controllers\Admin\ProductsController::class, 'search'])->name('products.search');
    // Route::get('products/{product}', [\App\Http\Controllers\Admin\ProductsController::class, 'show'])->name('products.show');
    Route::get('products/{product}/edit', [\App\Http\Controllers\Admin\ProductsController::class, 'edit'])->name('products.edit');
    Route::put('products/{product}', [\App\Http\Controllers\Admin\ProductsController::class, 'update'])->name('products.update');
    Route::delete('products/{product}', [\App\Http\Controllers\Admin\ProductsController::class, 'destroy'])->name('products.destroy');
    Route::post('/products/delete-multiple', [\App\Http\Controllers\Admin\ProductsController::class, 'deleteMultiple'])->name('products.delete-multiple');

    // Product Fees
    Route::get('fees/create', [\App\Http\Controllers\Admin\ProductFeesController::class, 'create'])->name('productfees.create');
    Route::post('fees', [\App\Http\Controllers\Admin\ProductFeesController::class, 'store'])->name('productfees.store');
    Route::get('fees', [\App\Http\Controllers\Admin\ProductFeesController::class, 'index'])->name('productfees.index');
    Route::get('fees-search', [\App\Http\Controllers\Admin\ProductFeesController::class, 'search'])->name('productfees.search');
    Route::get('fees/{fee}/edit', [\App\Http\Controllers\Admin\ProductFeesController::class, 'edit'])->name('productfees.edit');
    Route::put('fees/{fee}', [\App\Http\Controllers\Admin\ProductFeesController::class, 'update'])->name('productfees.update');
    Route::delete('fees/{fee}', [\App\Http\Controllers\Admin\ProductFeesController::class, 'destroy'])->name('productfees.destroy');
    Route::post('/fees/delete-multiple', [\App\Http\Controllers\Admin\ProductFeesController::class, 'deleteMultiple'])->name('productfees.delete-multiple');

    // Bundles
    Route::get('bundles', [\App\Http\Controllers\Admin\BundleController::class, 'index'])->name('bundles.index');
    Route::get('bundles/create', [\App\Http\Controllers\Admin\BundleController::class, 'create'])->name('bundles.create');
    Route::post('bundles', [\App\Http\Controllers\Admin\BundleController::class, 'store'])->name('bundles.store');
    Route::get('bundles-search', [\App\Http\Controllers\Admin\BundleController::class, 'search'])->name('bundles.search');
    Route::get('bundles/{bundle}', [\App\Http\Controllers\Admin\BundleController::class, 'show'])->name('bundles.show');
    Route::get('bundles/{bundle}/edit', [\App\Http\Controllers\Admin\BundleController::class, 'edit'])->name('bundles.edit');
    Route::put('bundles/{bundle}', [\App\Http\Controllers\Admin\BundleController::class, 'update'])->name('bundles.update');
    Route::delete('bundles/{bundle}', [\App\Http\Controllers\Admin\BundleController::class, 'destroy'])->name('bundles.destroy');
    Route::post('bundles/delete-multiple', [\App\Http\Controllers\Admin\BundleController::class, 'deleteMultiple'])->name('bundles.delete-multiple');

    // Companies
    Route::get('companies', [\App\Http\Controllers\Admin\CompanyController::class, 'index'])->name('companies.index');
    Route::get('companies/create', [\App\Http\Controllers\Admin\CompanyController::class, 'create'])->name('companies.create');
    Route::post('companies', [\App\Http\Controllers\Admin\CompanyController::class, 'store'])->name('companies.store');
    Route::get('companies-search', [\App\Http\Controllers\Admin\CompanyController::class, 'search'])->name('companies.search');
    Route::get('companies/{company}', [\App\Http\Controllers\Admin\CompanyController::class, 'show'])->name('companies.show');
    Route::get('companies/{company}/edit', [\App\Http\Controllers\Admin\CompanyController::class, 'edit'])->name('companies.edit');
    Route::post('companies/delete-multiple', [\App\Http\Controllers\Admin\CompanyController::class, 'deleteMultiple'])->name('companies.delete-multiple');
    Route::post('companies/{company}', [\App\Http\Controllers\Admin\CompanyController::class, 'update'])->name('companies.update');
    Route::delete('companies/{company}', [\App\Http\Controllers\Admin\CompanyController::class, 'destroy'])->name('companies.destroy');

    // Studio
    Route::get('get-company-info/{studio}', [\App\Http\Controllers\Admin\StudioController::class, 'getCompanyInfo'])->name('studio.get-info');

    // Licences
    Route::get('customer/{customer}/licenses', [\App\Http\Controllers\Admin\LicenseController::class, 'index'])->name('licenses.index');
    Route::get('customer/{customer}/licenses/create', [\App\Http\Controllers\Admin\LicenseController::class, 'create'])->name('licenses.create');
    Route::post('customer/{customer}/licenses', [\App\Http\Controllers\Admin\LicenseController::class, 'store'])->name('licenses.store');
    Route::get('customer/{customer}/licenses-search', [\App\Http\Controllers\Admin\LicenseController::class, 'search'])->name('licenses.search');
    Route::get('customer/{customer}/licenses/{license}', [\App\Http\Controllers\Admin\LicenseController::class, 'show'])->name('licenses.show');
    Route::get('customer/{customer}/licenses/{license}/edit', [\App\Http\Controllers\Admin\LicenseController::class, 'edit'])->name('licenses.edit');
    Route::get('customer/{customer}/licenses/{license}/payment-link', [\App\Http\Controllers\Admin\LicenseController::class, 'paymentLink'])->name('licenses.payment-link');
    Route::post('customer/{customer}/licenses/delete-multiple', [\App\Http\Controllers\Admin\LicenseController::class, 'deleteMultiple'])->name('licenses.delete-multiple');
    Route::post('customer/{customer}/licenses/{license}', [\App\Http\Controllers\Admin\LicenseController::class, 'update'])->name('licenses.update');
    Route::delete('customer/{customer}/licenses/{license}', [\App\Http\Controllers\Admin\LicenseController::class, 'destroy'])->name('licenses.destroy');
    Route::get('customer/{customer}/licenses/{license}/payment-history', [\App\Http\Controllers\Admin\LicenseController::class, 'loadPaymentHistoryBlade'])->name('licenses.payment-history');
    Route::get('customer/{customer}/licenses/{license}/load-tab-content', [\App\Http\Controllers\Admin\LicenseController::class, 'loadTabContent'])->name('licenses.load-tab-content');
    Route::post('customer/{customer}/licenses/{license}/change-status', [\App\Http\Controllers\Admin\LicenseController::class, 'changeStatus'])->name('licenses.change-status');
    // Route::post('customer/{customer}/licenses/{license}/sign-license', [\App\Http\Controllers\Admin\LicenseController::class, 'signLicense'])->name('licenses.sign');
    Route::post('license/{license}/customer/{customer}/notes', [\App\Http\Controllers\Admin\LicenseController::class, 'store_license_notes'])->name('license-notes.store');
    Route::delete('licenses/{license}/customer/{customer}/notes/{notes}', [\App\Http\Controllers\Admin\LicenseController::class, 'destroy_note'])->name('license-notes.destroy');

    Route::post('/license/{license}/customer/{customer}/files', [\App\Http\Controllers\Admin\LicenseController::class, 'store_license_files'])->name('license.files.upload');
    Route::delete('/license/{license}/files/{file}', [\App\Http\Controllers\Admin\LicenseController::class, 'destroy_license_file'])->name('license.files.delete');
    // Payments

    Route::get('customer/{customer}/licenses/{license}/payments', [\App\Http\Controllers\Admin\PaymentController::class, 'searchLicensePayments'])->name('licenses.upcoming-payments');
    Route::post('payments/{payment}/change-status', [\App\Http\Controllers\Admin\PaymentController::class, 'changeStatus'])->name('payments.change-status');

    // Lease
    Route::get('customer/{customer}/leases', [\App\Http\Controllers\Admin\LeaseController::class, 'index'])->name('leases.index');
    Route::get('customer/{customer}/leases/create', [\App\Http\Controllers\Admin\LeaseController::class, 'create'])->name('leases.create');
    Route::post('customer/{customer}/leases', [\App\Http\Controllers\Admin\LeaseController::class, 'store'])->name('leases.store');
    Route::get('customer/{customer}/leases-search', [\App\Http\Controllers\Admin\LeaseController::class, 'search'])->name('leases.search');
    Route::get('customer/{customer}/leases/{lease}', [\App\Http\Controllers\Admin\LeaseController::class, 'show'])->name('leases.show');
    Route::get('customer/{customer}/leases/{lease}/edit', [\App\Http\Controllers\Admin\LeaseController::class, 'edit'])->name('leases.edit');
    Route::get('customer/{customer}/leases/{lease}/payment-link', [\App\Http\Controllers\Admin\LeaseController::class, 'paymentLink'])->name('leases.payment-link');
    Route::post('customer/{customer}/leases/delete-multiple', [\App\Http\Controllers\Admin\LeaseController::class, 'deleteMultiple'])->name('leases.delete-multiple');
    Route::post('customer/{customer}/leases/{lease}', [\App\Http\Controllers\Admin\LeaseController::class, 'update'])->name('leases.update');
    Route::delete('customer/{customer}/leases/{lease}', [\App\Http\Controllers\Admin\LeaseController::class, 'destroy'])->name('leases.destroy');
    Route::get('customer/{customer}/leases/{lease}/load-tab-content', [\App\Http\Controllers\Admin\LeaseController::class, 'loadTabContent'])->name('leases.load-tab-content');
    Route::get('customer/{customer}/leases/{lease}/payments', [\App\Http\Controllers\Admin\PaymentController::class, 'searchLeasePayments'])->name('leases.payments');
    Route::post('customer/{customer}/leases/{lease}/change-status', [\App\Http\Controllers\Admin\LeaseController::class, 'changeStatus'])->name('leases.change-status');
    Route::post('customer/{customer}/leases/{lease}/mark-as-delivered', [\App\Http\Controllers\Admin\LeaseController::class, 'markAsDelivered'])->name('leases.mark-as-delivered');

    // Lease Notes
    Route::post('leases/{lease}/customer/{customer}/notes', [\App\Http\Controllers\Admin\LeaseController::class, 'store_lease_notes'])->name('lease-notes.store');
    Route::delete('leases/{lease}/customer/{customer}/notes/{notes}', [\App\Http\Controllers\Admin\LeaseController::class, 'destroy_note'])->name('lease-notes.destroy');

    // Lease Files
    Route::post('/leases/{lease}/customer/{customer}/files', [\App\Http\Controllers\Admin\LeaseController::class, 'store_lease_files'])->name('lease.files.upload');
    Route::delete('/leases/{lease}/files/{file}', [\App\Http\Controllers\Admin\LeaseController::class, 'destroy_lease_file'])->name('lease.files.delete');



    //    Route::put('customer/{customer}/leases/{lease}/cancel', [\App\Http\Controllers\Admin\LeaseController::class, 'cancel'])->name('leases.load-tab-content');

    // Notes
    Route::get('customer/{customer}/notes', [\App\Http\Controllers\Admin\NoteController::class, 'index'])->name('notes.index');
    Route::post('customer/{customer}/notes', [\App\Http\Controllers\Admin\NoteController::class, 'store'])->name('notes.store');
    Route::delete('customer/{customer}/notes/{note}', [\App\Http\Controllers\Admin\NoteController::class, 'destroy'])->name('notes.destroy');

    // Invoices
    Route::get('customer/{customer}/invoices', [\App\Http\Controllers\Admin\InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('customer/{customer}/invoices-search', [\App\Http\Controllers\Admin\InvoiceController::class, 'search'])->name('invoices.search');
    Route::get('customer/{customer}/download-invoice/{invoice}', [\App\Http\Controllers\Admin\InvoiceController::class, 'downloadInvoice'])->name('download-invoice');
    Route::post('customer/{customer}/email-invoice/{invoice}', [\App\Http\Controllers\Admin\InvoiceController::class, 'emailInvoice'])->name('email-invoice');


    // Purchases
    Route::get('customer/{customer}/purchases-search', [\App\Http\Controllers\Admin\PurchaseController::class, 'search'])->name('purchases.search');
    Route::get('customer/{customer}/purchases/create', [\App\Http\Controllers\Admin\PurchaseController::class, 'create'])->name('purchases.create');
    Route::post('customer/{customer}/purchases', [\App\Http\Controllers\Admin\PurchaseController::class, 'store'])->name('purchases.store');
    Route::post('customer/{customer}/purchases/{purchase}/change-status', [\App\Http\Controllers\Admin\PurchaseController::class, 'changeStatus'])->name('purchases.change-status');
    Route::post('customer/{customer}/purchases/{purchase}/mark-as-delivered', [\App\Http\Controllers\Admin\PurchaseController::class, 'markAsDelivered'])->name('purchases.mark-as-delivered');
    Route::post('customer/{customer}/purchases/delete-multiple', [\App\Http\Controllers\Admin\PurchaseController::class, 'deleteMultiple'])->name('purchases.delete-multiple');
    Route::get('customer/{customer}/purchases/{purchase}/payment-link', [\App\Http\Controllers\Admin\PurchaseController::class, 'paymentLink'])->name('purchases.payment-link');

    // Payment History
    Route::get('customer/{customer}/payment-history-search', [\App\Http\Controllers\Admin\PaymentHistoryController::class, 'search'])->name('payment-history.search');

    // Suppliers
    Route::get('suppliers', [\App\Http\Controllers\Admin\SupplierController::class, 'index'])->name('suppliers.index');
    Route::get('suppliers/create', [\App\Http\Controllers\Admin\SupplierController::class, 'create'])->name('suppliers.create');
    Route::post('suppliers', [\App\Http\Controllers\Admin\SupplierController::class, 'store'])->name('suppliers.store');
    Route::get('suppliers-search', [\App\Http\Controllers\Admin\SupplierController::class, 'search'])->name('suppliers.search');
    Route::get('suppliers/{supplier}', [\App\Http\Controllers\Admin\SupplierController::class, 'show'])->name('suppliers.show');
    Route::get('suppliers/{supplier}/edit', [\App\Http\Controllers\Admin\SupplierController::class, 'edit'])->name('suppliers.edit');
    Route::post('suppliers/delete-multiple', [\App\Http\Controllers\Admin\SupplierController::class, 'deleteMultiple'])->name('suppliers.delete-multiple');
    Route::post('suppliers/{supplier}', [\App\Http\Controllers\Admin\SupplierController::class, 'update'])->name('suppliers.update');
    Route::delete('suppliers/{supplier}', [\App\Http\Controllers\Admin\SupplierController::class, 'destroy'])->name('suppliers.destroy');
    Route::get('suppliers/{supplier}/load-tab-content', [\App\Http\Controllers\Admin\SupplierController::class, 'loadTabContent'])->name('suppliers.load-tab-content');
    Route::post('suppliers/{supplier}/contact', [\App\Http\Controllers\Admin\SupplierController::class, 'contactSupplier'])->name('suppliers.contact');

    // License Settings
    Route::get('licenseSettings', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'index'])->name('licenseSettings.index');
    Route::get('licenseSettings/create', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'create'])->name('licenseSettings.create');
    Route::post('licenseSettings', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'store'])->name('licenseSettings.store');
    Route::get('licenseSettings-search', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'search'])->name('licenseSettings.search');
    Route::get('licenseSettings/{licenseSettings}', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'show'])->name('licenseSettings.show');
    Route::get('licenseSettings/{licenseSettings}/edit', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'edit'])->name('licenseSettings.edit');
    Route::post('licenseSettings/delete-multiple', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'deleteMultiple'])->name('licenseSettings.delete-multiple');
    Route::post('licenseSettings/{licenseSettings}', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'update'])->name('licenseSettings.update');
    Route::delete('licenseSettings/{licenseSettings}', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'destroy'])->name('licenseSettings.destroy');
    Route::get('licenseSettings/{licenseSettings}/load-tab-content', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'loadTabContent'])->name('licenseSettings.load-tab-content');
    Route::post('licenseSettings/{licenseSettings}/contact', [\App\Http\Controllers\Admin\LicenseSettingsController::class, 'contactSupplier'])->name('licenseSettings.contact');

    // Orders
    Route::get('orders', [\App\Http\Controllers\Admin\OrderController::class, 'index'])->name('orders.index');
    Route::get('orders/create', [\App\Http\Controllers\Admin\OrderController::class, 'create'])->name('orders.create');
    Route::post('orders', [\App\Http\Controllers\Admin\OrderController::class, 'store'])->name('orders.store');
    Route::get('orders-search', [\App\Http\Controllers\Admin\OrderController::class, 'search'])->name('orders.search');
    Route::get('orders/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'show'])->name('orders.show');
    Route::get('orders/{order}/edit', [\App\Http\Controllers\Admin\OrderController::class, 'edit'])->name('orders.edit');
    Route::post('orders/delete-multiple', [\App\Http\Controllers\Admin\OrderController::class, 'deleteMultiple'])->name('orders.delete-multiple');
    Route::post('orders/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'update'])->name('orders.update');
    Route::delete('orders/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'destroy'])->name('orders.destroy');
    Route::get('orders/{order}/download', [\App\Http\Controllers\Admin\OrderController::class, 'download'])->name('orders.download');
    Route::post('orders/{order}/send-to-supplier', [\App\Http\Controllers\Admin\OrderController::class, 'sendToSupplier'])->name('orders.send-to-supplier');

    // Agreement
    Route::get('customer/{customer}/agreements-search', [\App\Http\Controllers\Admin\AgreementController::class, 'search'])->name('agreements.search');
    Route::get('agreements/{agreement}/download', [\App\Http\Controllers\Admin\AgreementController::class, 'download'])->name('agreements.download');
    Route::get('agreements', [\App\Http\Controllers\Admin\AgreementController::class, 'index'])->name('agreements.index');
    Route::get('agreements/search', [\App\Http\Controllers\Admin\AgreementController::class, 'searchAll'])->name('agreements.searchAll');
    Route::delete('agreements/{agreement}', [\App\Http\Controllers\Admin\AgreementController::class, 'destroy'])->name('agreements.destroy');
    Route::post('agreements/preview_agreement_popup', [\App\Http\Controllers\Admin\AgreementController::class, 'previewAgreement'])->name('agreements.preview-agreement');

    // Notifications
    Route::get('notifications', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'index'])->name('notifications.index');
    Route::get('notifications/create', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'create'])->name('notifications.create');
    Route::post('notifications', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'store'])->name('notifications.store');
    Route::get('notifications-search', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'search'])->name('notifications.search');
    Route::get('notifications/{notification}', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'show'])->name('notifications.show');
    Route::get('notifications/{notification}/edit', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'edit'])->name('notifications.edit');
    Route::post('notifications/delete-multiple', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'deleteMultiple'])->name('notifications.delete-multiple');
    Route::put('notifications/{notification}', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'update'])->name('notifications.update');
    Route::delete('notifications/{notification}', [\App\Http\Controllers\Admin\AdminNotificationController::class, 'destroy'])->name('notifications.delete');

    // Settings
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');

    // Email Templates
    Route::get('email-templates', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'index'])->name('email-templates.index');
    Route::get('email-templates-search', [\App\Http\Controllers\Admin\EmailTemplateController::class, 'search'])->name('email-templates.search');

    // InvoiceProduct
    Route::get('invoice-products', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'index'])->name('invoice-products.index');
    Route::get('invoice-products/create', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'create'])->name('invoice-products.create');
    Route::post('invoice-products', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'store'])->name('invoice-products.store');
    Route::get('invoice-products-search', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'search'])->name('invoice-products.search');
    Route::get('invoices-search-customer', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'searchCustomerInvoices'])->name('invoice-products.searchCustomerInvoices');
    Route::get('invoice-products-download/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'downloadInvoice'])->name('invoice-products.download');
    Route::get('invoice-products-view/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'viewInvoice'])->name('invoice-products.view');
    Route::post('invoice-products-email/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'emailInvoice'])->name('invoice-products.email');
    Route::post('invoice-products-reminder/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'emailInvoiceReminder'])->name('invoice-products.emailInvoiceReminder');
    Route::get('invoice-products/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'show'])->name('invoice-products.show');
    Route::get('invoice-products/{invoiceProduct}/edit', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'edit'])->name('invoice-products.edit');
    Route::post('invoice-products/{invoiceProduct}/mark_as_paid', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'mark_as_paid'])->name('invoice-products.mark_as_paid');
    Route::post('invoice-products/get_bundle', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'get_bundle'])->name('invoice-products.get-bundle');
    Route::post('invoice-products/delete-multiple', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'deleteMultiple'])->name('invoice-products.delete-multiple');
    Route::post('invoice-products/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'update'])->name('invoice-products.update');
    Route::delete('invoice-products/{invoiceProduct}', [\App\Http\Controllers\Admin\InvoiceProductController::class, 'destroy'])->name('invoice-products.destroy');

    // Invoice Templates
    Route::get('invoice-templates', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'index'])->name('invoice-templates.index');
    Route::get('invoice-templates-search', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'search'])->name('invoice-templates.search');
    Route::get('invoice-templates/create', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'create'])->name('invoice-templates.create');
    Route::post('invoice-templates', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'store'])->name('invoice-templates.store');
    Route::post('invoice-templates/get-templates', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'get_templates'])->name('invoice-templates.get-templates');
    Route::post('invoice-templates/delete-multiple', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'deleteMultiple'])->name('invoice-templates.delete-multiple');
    Route::get('invoice-templates/{invoiceTemplate}/edit', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'edit'])->name('invoice-templates.edit');
    Route::post('invoice-templates/{invoiceTemplate}', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'update'])->name('invoice-templates.update');
    Route::delete('invoice-templates/{invoiceTemplate}', [\App\Http\Controllers\Admin\InvoiceTemplateController::class, 'destroy'])->name('invoice-templates.destroy');
});

Route::middleware('auth')->prefix('studio')->name('studio.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\Studio\StudioController::class, 'index'])->name('dashboard');
    Route::get('/location', [\App\Http\Controllers\Studio\StudioController::class, 'location'])->name('location');
    Route::get('/lease', [\App\Http\Controllers\Studio\StudioController::class, 'lease'])->name('lease');
    Route::get('/payment-history', [\App\Http\Controllers\Studio\StudioController::class, 'payment_history'])->name('payment-history');
    Route::get('/invoice', [\App\Http\Controllers\Studio\StudioController::class, 'invoice'])->name('invoice');
    Route::get('/agreement', [\App\Http\Controllers\Studio\StudioController::class, 'agreement'])->name('agreement');
    Route::get('/profile', [\App\Http\Controllers\Studio\StudioController::class, 'profile'])->name('profile');
    Route::get('/settings', [\App\Http\Controllers\Studio\StudioController::class, 'setting'])->name('setting');
    Route::get('/support/tickets', [\App\Http\Controllers\Studio\StudioController::class, 'tickets'])->name('support.tickets.ticket');

    Route::delete('/invoice-product/{invoiceProduct}', [\App\Http\Controllers\Studio\StudioController::class, 'invoice_destroy'])->name('invoice-products.destroy');
    
    Route::get('/{customer}/location-search', [\App\Http\Controllers\Studio\StudioController::class, 'location_search'])->name('location.search');
    Route::get('/{customer}/lease-search', [\App\Http\Controllers\Studio\StudioController::class, 'lease_search'])->name('lease.search');
    Route::get('/{customer}/payment-history-search', [\App\Http\Controllers\Studio\StudioController::class, 'payment_history_search'])->name('payment-history.search');
    Route::get('/{customer}/invoices-search-customer', [\App\Http\Controllers\Studio\StudioController::class, 'invoice_search'])->name('invoice.search');
    Route::get('/{customer}/search-agreeements', [\App\Http\Controllers\Studio\StudioController::class, 'agreements_search'])->name('agreements.search');
    Route::get('/{customer}/ticket-search', [\App\Http\Controllers\Studio\StudioController::class, 'ticket_search'])->name('ticket.search');

    Route::get('/{customer}/location/{license}', [\App\Http\Controllers\Studio\StudioController::class, 'location_show'])->name('location.show');
    Route::get('/{customer}/location/create', [\App\Http\Controllers\Studio\StudioController::class, 'location_create'])->name('location.create');
    Route::post('/{customer}/location/store', [\App\Http\Controllers\Studio\StudioController::class, 'location_store'])->name('location.store');
    Route::get('/{customer}/location/{license}/edit', [\App\Http\Controllers\Studio\StudioController::class, 'location_edit'])->name('location.edit');
    Route::post('/{customer}/location/{license}', [\App\Http\Controllers\Studio\StudioController::class, 'location_update'])->name('location.update');
    Route::delete('/{customer}/location/{license}', [\App\Http\Controllers\Studio\StudioController::class, 'location_destroy'])->name('location.destroy');

    Route::get('{customer}/leases/create', [\App\Http\Controllers\Studio\StudioController::class, 'lease_create'])->name('lease.create');
    Route::get('{customer}/leases/{lease}', [\App\Http\Controllers\Studio\StudioController::class, 'lease_show'])->name('lease.show');
    Route::get('/{customer}/leases/{lease}/edit', [\App\Http\Controllers\Studio\StudioController::class, 'lease_edit'])->name('lease.edit');
    Route::post('/customer/{customer}/leases/{lease}', [\App\Http\Controllers\Studio\StudioController::class, 'lease_update'])->name('lease.update');
    Route::delete('/{customer}/lease/{lease}', [\App\Http\Controllers\Studio\StudioController::class, 'lease_destroy'])->name('lease.destroy');

    Route::get('/{customer}/edit', [\App\Http\Controllers\Studio\StudioController::class, 'profile_edit'])->name('profile.edit');
    Route::put('/profile/{customer}', [\App\Http\Controllers\Studio\StudioController::class, 'profile_update'])->name('profile.update');
    Route::put('/setting/{customer}', [\App\Http\Controllers\Studio\StudioController::class, 'setting_update'])->name('setting.update');

    Route::get('customer/{customer}/download-invoice/{invoice}', [\App\Http\Controllers\Studio\StudioController::class, 'downloadInvoice'])->name('download-invoice');
});

Route::middleware('auth')->group(function () {
    Route::post('markAsReadNotification', [\App\Http\Controllers\NotificationController::class, 'markAsReadNotification'])->name('mark-as-read-notification');
    Route::post('markAsReadNewClientsNotification', [\App\Http\Controllers\NotificationController::class, 'markAsReadNewClientsNotification'])->name('mark-as-read-new-clients-notification');
    Route::post('markAsReadAll', [\App\Http\Controllers\NotificationController::class, 'markAsReadAll'])->name('mark-as-read-all');
});


// Route::get('/payment', [\App\Http\Controllers\StripePaymentController::class, 'show']);

// Route::get('/payment/{license}/license_link', [\App\Http\Controllers\StripePaymentController::class, 'stripeLicenseDepositLink'])->name('payment.link');
// Route::get('/payment/license/success', [\App\Http\Controllers\StripePaymentController::class, 'successLicenseDeposit'])->name('payment.licenseSuccess');
// Route::get('/payment/license/cancel', [\App\Http\Controllers\StripePaymentController::class, 'cancelLicenseDeposit'])->name('payment.licenseCancel');
// Route::get('/payment/{payment}/purchase_link', [\App\Http\Controllers\StripePaymentController::class, 'stripePurchaseLink'])->name('payment.purchaseLink');

Route::get('/pay/test_purchase', [\App\Http\Controllers\StripePaymentController::class, 'testPurchaseWebhookManually'])->name('payment.testPurchase');

// Payment routes with rate limiting and security measures
Route::middleware(['throttle:payment', 'payment.security'])->group(function () {
    Route::post('/pay/process', [\App\Http\Controllers\StripePaymentController::class, 'processPayment'])->name('payment.process');
    Route::get('/payment/paymentIntent', [\App\Http\Controllers\StripePaymentController::class, 'paymentIntent'])->name('payment.paymentIntent');
});

Route::middleware(['throttle:60,1', 'payment.security'])->group(function () {
    Route::get('/pay/{payment_id}/success', [\App\Http\Controllers\StripePaymentController::class, 'successPayment'])->name('payment.success');
    Route::get('/pay/{payment_id}/cancel', [\App\Http\Controllers\StripePaymentController::class, 'cancelPayment'])->name('payment.canceled');
    Route::get('/pay/{type}/{id}', [\App\Http\Controllers\StripePaymentController::class, 'stripeForm'])->name('payment.form');
});
Route::get('/payment/retrieve', [\App\Http\Controllers\StripePaymentController::class, 'retrievePaymentIntent'])->name('payment.retrievePaymentIntent');

require __DIR__ . '/auth.php';
