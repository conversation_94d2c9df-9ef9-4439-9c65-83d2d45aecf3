@if(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Purchase Sent (0/2)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-warning text-warning" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Purchase Sent (0/2)'] }}">
        {{ __('Purchase Sent (0/2)') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Signed 1/2'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Signed 1/2'] }}">
        {{ __('Signed 1/2') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Signed 2/2 (wait4pay)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Signed 2/2 (wait4pay)'] }}">
        {{ __('Signed 2/2 (wait4pay)') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Awaiting delivery (paid)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Awaiting delivery (paid)'] }}">
        {{ __('Awaiting delivery (paid)') }}
    </div>
@elseif( $status == \App\Helpers\Constants::PURCHASE_STATUSES['Delivered'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-success" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Delivered'] }}">
        {{ __('Delivered') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Deposit paid'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-secondary" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Deposit paid'] }}">
        {{ __('Deposit paid') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Inactive'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-light text-dark" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Inactive'] }}">
        {{ __('Inactive') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Canceled'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-danger text-danger" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Canceled'] }}">
        {{ __('Canceled') }}
    </div>
@elseif(  $status == \App\Helpers\Constants::PURCHASE_STATUSES['Other'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-light text-dark" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Helpers\Constants::PURCHASE_STATUSES['Other'] }}">
        {{ __('Other') }}
    </div>
@endif