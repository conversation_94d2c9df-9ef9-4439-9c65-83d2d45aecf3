@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="page-title">
            <div class="title-left">
                <h3>{{ __('edit license') }}</h3>
                <a href="{{ route('admin.customers.dashboard', $customer) }}" class="back-link">← Back</a>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.licenses.update', ['customer' => $customer, 'license' => $license]) }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('lagree company') }}</h5>

            @include('partials.forms.select', [
                'field_name' => 'company_id',
                'field_label' => null,
                'values' => $companies,
                'field' => 'company',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('company_id', $license->company_id),
                'include_empty' => true,
            ])
    
            {{-- <h5 class="form-section-title">{{ __('license type') }}</h5>
            @include('partials.forms.radio-group', [
                'field_name' => 'type',
                'field_label' => 'Radio group',
                'field_class' => 'form-mb-0',
                'values' => ['type_license' => ['text' => 'License', 'value' => 'license'], 'type_exclusivity' => ['text' => 'Exclusivity', 'value' => 'exclusivity']],
                'field' => 'status',
                'required' => 'required',
                'checked' => 'license',
            ]) --}}
    
            {{-- @include('partials.forms.radio-group', [
                'field_name' => 'studio_exists',
                'field_label' => 'Radio group location',
                'field_class' => 'license_studio',
                'values' => ['addNewStudio' => ['text' => 'Add a new studio', 'value' => '0'], 'assignExclusivity' => ['text' => 'Assign license to an existing studio', 'value' => '1']],
                'field' => 'status',
                'required' => 'required',
                'checked' => '0'
            ])--}}
            {{-- @include('partials.forms.select', [
                'field_name' => 'studio_id',
                'field_label' => null,
                'field_class' => 'mb-7 mt-n4',
                'values' => $studios,
                'field' => 'studio',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => null,
                'include_empty' => true,
                'field_id' => 'license_select'
            ]) --}}
            <div class="studio_info_wrap">
                <h5 class="form-section-title">{{ __('studio info') }}</h5>
                @include('partials.forms.input', [
                    'field_name' => 'studio[name]',
                    'field_label' => 'LOCATION NAME *',
                    'field_type' => 'text',
                    'field_value' => old('studio[name]', $license->studio->name),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_first_name]',
                    'field_label' => 'FIRST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_first_name]', $license->studio->owner_first_name),
                    'field_class' => 'studio-input'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_last_name]',
                    'field_label' => 'LAST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_last_name]', $license->studio->owner_last_name),
                    'field_class' => 'studio-input'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[phone]',
                    'field_label' => 'PHONE # *',
                    'field_type' => 'text',
                    'field_value' => old('studio[phone]', $license->studio->phone),
                    'field_class' => 'studio-input'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[email]',
                    'field_label' => 'EMAIL ADDRESS *',
                    'field_type' => 'email',
                    'field_value' => old('studio[email]', $license->studio->email),
                    'field_class' => 'studio-input'
                ])
    
                <h5 class="form-section-title">{{ __('address info') }}</h5>
    
                @include('partials.forms.radio-group', [
                    'field_name' => 'location',
                    'field_class' => 'mb-7',
                    'field_label' => 'Radio group location',
                    'values' => [
                        'location_usa' => ['text' => 'USA', 'value' => 'USA'], 
                        'location_international' => ['text' => 'International', 'value' => 'International']
                    ],
                    'field' => 'status',
                    'field_value' => old('location', $license->location),
                    'required' => 'required',
                    'checked' => 'USA'
                ])
    
                @include('partials.forms.input', [
                    'field_name' => 'studio[address]',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => old('studio[address]', $license->studio->address),
                    'field_class' => 'studio-input'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[address2]',
                    'field_label' => 'ADDRESS 2',
                    'field_type' => 'text',
                    'field_value' => old('studio[address2]', $license->studio->address2),
                    'field_class' => 'studio-input'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[city]',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => old('studio[city]', $license->studio->city),
                    'field_class' => 'studio-input'
                ])
    
                @include('partials.forms.input', [
                    'field_name' => 'studio[zip]',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => old('studio[zip]', $license->studio->zip),
                    'field_class' => 'studio-input',
                ])
                
                @include('partials.forms.select', [
                    'field_name' => 'studio[state_id]',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[state_id]', $license->studio->state_id),
                    'include_empty' => true
                ])
    
                @include('partials.forms.select', [
                    'field_name' => 'studio[country_id]',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'countries',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[country_id]', $license->studio->country_id),
                    'include_empty' => true
                ])
            </div>
    
            <h5 class="fs-14px mt-50 mb-45 pt-45 form-section-title custom-fw-500 text-uppercase border-top">{{ __('license Info') }}</h5>
    
            @include('partials.forms.input',  [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', $license->starting_date),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field'
            ])
    
            @include('partials.forms.select', [
                'field_name' => 'package',
                'field_label' => 'PACKAGE TYPE *',
                'placeholder' => 'Select',
                'values' => \App\Helpers\Constants::LICENSE_PACKAGES,
                'field' => 'package',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('package', $license->package),
                'include_empty' => true
            ])
    
            @include('partials.forms.input',  [
                'field_name' => 'price',
                'field_type' => 'hidden',
                'field_value' => round($license->converted_deposit, 2)
            ])
    
            @include('partials.forms.input',  [
                'field_name' => 'duration',
                'field_type' => 'hidden',
                'field_value' => 12
            ])
    
            @include('partials.forms.input',  [
                'field_name' => 'type',
                'field_type' => 'hidden',
                'field_value' => 'license'
            ])
    
            @include('partials.forms.input',  [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d')
            ])
    
            <div class="deposit-wrap">
                @include('partials.forms.input', [
                    'field_name' => 'deposit_amount',
                    'field_label' => 'DEPOSIT AMOUNT *',
                    'field_type' => 'text',
                    'currency' => true,
                    'field_value' => old('deposit_amount', round($license->converted_deposit, 2)),
                    'input_field_class' => 'decimal-field'
                ])
            </div>
    
            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', $customer->description),
            ])

            <div class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button" href="{{route('admin.licenses.edit', ['customer' => $customer, 'license' => $license])}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'License'}}{{$license->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>

        </form>
    </div>
@endsection

@include('partials.modals.delete', [
        'type' => 'License',
        'id' => $license->id,
        'route' => route('admin.licenses.destroy', ['customer' => $customer, 'license' => $license]),
        'title' => $license->name,
    ])

<script type="module">
const package_prices = @json(\App\Helpers\Constants::LICENSE_PACKAGES_PRICES);

document.addEventListener('DOMContentLoaded', function () {
    // Function to toggle visibility based on the location radio button
    function toggleLocationInputs() {
        const isUSA = document.getElementById('location_usa').checked;

        // Toggle visibility for State and Country inputs
        const stateSelect = document.querySelector("[name='studio[state_id]']");
        const countrySelect = document.querySelector("[name='studio[country_id]']");
        const price = package_prices[$('#package-package').val()];

        if (isUSA) {
            stateSelect.closest('.form-group').style.display = 'block';
            countrySelect.closest('.form-group').style.display = 'none';
            // $('.deposit-wrap').removeClass('disabled');

            if($('#package-package').val() != 0) {
                $('#deposit_amount').val(490);
            }else{
                $('#deposit_amount').val(0);
            }
        } else {
            stateSelect.closest('.form-group').style.display = 'none';
            countrySelect.closest('.form-group').style.display = 'block';
            // $('.deposit-wrap').addClass('disabled');
            if($('#package-package').val() != 0) {
                $('#deposit_amount').val(price);
            }else{
                $('#deposit_amount').val(0);
            }
        }
    }

    // Event listeners for the radio buttons
    document.getElementById('location_usa').addEventListener('change', toggleLocationInputs);
    document.getElementById('location_international').addEventListener('change', toggleLocationInputs);

    let inputField = document.querySelector('.is-invalid');
    if (inputField) {
        inputField.focus();
    }

    $('form').on('submit', function() {
        $('input[name="location"]').prop('disabled', false);
    });

    $('#package-package').change(function() {
        var val = $(this).val();
        console.log('val: ', val);

        $('#price').val(package_prices[val] || '');
        toggleLocationInputs();
    });

    toggleLocationInputs();

    // Initialize Google Places Autocomplete
    window.initGooglePlaces = function() {
        const autocompleteService = new GooglePlacesAutocomplete();

        const fieldConfigs = [
            {
                inputId: 'studio[address]',
                fields: {
                    address: 'studio[address]',
                    city: 'studio[city]',
                    zip: 'studio[zip]',
                    state: 'studio[state_id]',
                    country: 'studio[country_id]'
                },
                locationRadios: {
                    usa: 'location_usa',
                    international: 'location_international'
                }
            }
        ];

        autocompleteService.init(fieldConfigs);
    };

    // Load Google Maps API
    const googleApiKey = '{{ config("services.google_maps.api_key") }}';
    if (googleApiKey && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
        GooglePlacesAutocomplete.loadGoogleMapsAPI(googleApiKey, window.initGooglePlaces);
    }
});
</script>

<!-- Google Places Autocomplete Script -->
@vite('resources/js/google-places-autocomplete.js')
