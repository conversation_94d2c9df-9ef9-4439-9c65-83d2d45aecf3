@if(  $status == \App\Models\Agreement::STATUSES['Sent (0/2)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-warning text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Sent (0/2)'] }}">
        {{ ucfirst($type) . __(' Sent (0/2)') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Signed 1/2'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Signed 1/2'] }}">
        {{ __('Signed 1/2') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Signed 2/2 (wait4pay)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-dark text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Signed 2/2 (wait4pay)'] }}">
        {{ __('Signed 2/2 (wait4pay)') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Awaiting delivery (paid)'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Awaiting delivery (paid)'] }}">
        {{ __('Awaiting delivery (paid)') }}
    </div>
@elseif( $status == \App\Models\Agreement::STATUSES['Active'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Active'] }}">
        @switch($type)
            @case('lease')
                {{ __('Active (mp)') }}
                @break
            @case('purchase')
                {{ __('Delivered') }}
                @break
            @case('license')
                {{ __('Active') }}
                @break
        @endswitch
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Inactive'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium" style="max-width: 170px;background: #969696; color: #fff;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Inactive'] }}">
        {{ __('Inactive') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Canceled'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-danger text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Canceled'] }}">
        {{ __('Canceled') }}
    </div>
@elseif(  $status == \App\Models\Agreement::STATUSES['Deposit Paid'] )
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-blue text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Deposit Paid'] }}">
        {{ __('Deposit Paid') }}
    </div>
@else
    <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-black text-white" style="max-width: 170px;" data-status="{{ $status }}" data-statuses="{{ \App\Models\Agreement::STATUSES['Uploaded'] }}">
        {{ __('Uploaded') }}
    </div>
@endif