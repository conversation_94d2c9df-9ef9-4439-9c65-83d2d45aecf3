<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
// use App\Models\LeaseNotes;
// use App\Models\LeaseFiles;

class Purchase extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'purchase';
    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    protected $fillable = [
        'company_id',
        'studio_id',
        'customer_id',
        'machine_id',
        'configuration_id',
        'machine_price',
        'monthly_installment',
        'machine_quantity',
        // 'duration',
        'starting_date',
        'deposit_amount',
        'deposit_date',
        // 'is_active',
        'condition',
        'status',
        // 'buy_out',
        'initial_currency_id'
    ];

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'company'   => ['id', 'leases.studio_id'],
        'machine'   => ['id', 'leases.machine_id'],
        'invoice'   => ['payment_id', 'id'],
    ];

    public function getPrimaryAndForeignKeys(string $relation): array
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function sortableFields(): array
    {
        return [
            'id',
            'machine_quantity',
            'machine_price',
            // 'is_active',
            'studio.name',
            'machine.name',
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function studio(): BelongsTo
    {
        return $this->belongsTo(Studio::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function machine(): BelongsTo
    {
        return $this->belongsTo(Machine::class);
    }

    public function configuration(): BelongsTo
    {
        return $this->belongsTo(Machine::class, 'configuration_id');
    }

    public function payments(): HasMAny
    {
        return $this->hasMany(Payment::class);
    }

    public function conversionRate(): MorphMany
    {
        return $this->morphMany(ConversionRate::class, 'rateable');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function initialCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    // public function notes(): HasMany
    // {
    //     return $this->hasMany(LeaseNotes::class);
    // }

    // public function files(): HasMany
    // {
    //     return $this->hasMany(LeaseFiles::class);
    // }

    // public function leaseNotes(): HasMany
    // {
    //     return $this->hasMany(LeaseNotes::class, 'lease_id', 'id');
    // }

    public function agreement(): HasMany
    {
        return $this->hasMany(Agreement::class);
    }

}
