@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('agreements') }}</h3>
        </div>
        {{-- <a href="{{ route('admin.orders.create') }}" class="btn btn-primary index-addnew-desk">{{ __('ADD NEW') }}</a>
        <a href="{{ route('admin.orders.create') }}" class="btn btn-primary index-addnew-mob">+</a> --}}
        <a class="info-statuses all-statuses">INFO</a>
    </div>

    <div class="nr-items">
        <h5 id="agreementsCount"></h5>

        <div class="sortbysearch">
            {{-- <div class="zIndex-10000 filter-dropdown">
                <select class="form-select table-filter-select" aria-label="Default select example" id="statusSelect">
                    <option selected value="all">Sort by: {{ __('All') }}</option>
                    <option value="{{ \App\Models\Agreement::STATUSES['Pending'] }}">Sort by: Pending</option>
                    <option value="{{ \App\Models\Agreement::STATUSES['Active'] }}">Sort by: Active</option>
                </select>
            </div> --}}
            <div class="ms-auto lg-search h-40px">
                <input class="typeahead form-control" id="agreements-search" type="text" name="lagree-search" placeholder="Search" autocomplete="off">
                <span class="lg-search-ico" onclick="$(this).prev().toggleClass('lg-search-expanded');"><img src="/search.svg" alt="search" class=""></span>
            </div>
        </div>
    </div>
<div id="all-agreements-table" class="entity-table bg--loading-black"></div>
@endsection




<script type="module">
document.addEventListener('DOMContentLoaded', function () {
    const agreementsCount = @json($agreements).total;
    const descriptiveLabel = agreementsCount === 1 ? ' Agreement' : ' Agreements';
    setTimeout(function(){
        $('#agreementsCount').text(agreementsCount + descriptiveLabel);        
    }, 300);

    const searchInput = $('#agreements-search');
    const searchIcon = $('.lg-search-ico');
    const sortIcon = $('.sort-icon');
    let searchData = '';
    let orderParam = 'created_at';
    let orderType = 'desc';
    const agreementsTable = $('#all-agreements-table');
    const html = $('html, body');

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        if(dropdownContent.hasClass('show')){
            dropdownContent.removeClass('show');
        }else{
            dropdownContent.addClass('show');
        }
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    @php
        $route = route('admin.agreements.searchAll');
    @endphp

    function fetchData(url) {
        let perPage = $('.pagination-select').val() ?? 10;
        let selectedValue = $('#statusSelect').val();
        $.ajax({
            url: url,
            data: {
                status: selectedValue,
                search_data: searchData,
                order_param: orderParam,
                order_type: orderType,
                per_page: perPage,
            },
            success: function (data) {
                setTimeout(function () {
                    agreementsTable.removeClass('bg--loading-black').html(data);
                    setSortIcon();
                    // agreementsTable.trigger('resultLoaded');
                    $('.pagination-select').val(perPage);
                }, 300);
            },
            error: function() {
                flasherJS.error('', 'Error occurred while loading data.');
            }
        });
    }

    $('body').on('change', '#statusSelect', function(e) {
        fetchData("{{ $route }}");
    });

    $('body').on('change', '.pagination-select', function(e) {
        fetchData("{{ $route }}");
    });

    $('body').on('click', '.pagination a', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        fetchData(url);
    });

    searchInput.on('input', debounce(function() {
        searchData = $(this).val();
        fetchData("{{ $route }}");
    }, 500));

    searchInput.on('click focus', function(e) {
        e.stopPropagation();
    });

    $('#all-agreements-table').on('click', '.sortable-list-header', function() {
        const newOrderParam = $(this).data('sort');
        $('.sortable-list-header').removeClass('active');
        $(this).addClass('active');
        if (orderParam === newOrderParam) {
            // Toggle sorting direction if the same column is clicked
            orderType = orderType === 'asc' ? 'desc' : 'asc';
        } else {
            // Set new sort column and default direction to ascending
            orderParam = newOrderParam;
            orderType = 'asc';
        }
        fetchData("{{ $route }}");
    });

    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            let context = this,
                args = arguments;
            let later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            let callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    searchInput.on('click focus', function(e) {
        e.stopPropagation();
    });

    function setSortIcon() {
        sortIcon.removeClass('asc desc'); // Remove existing sort classes
        $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(orderType); // Add active sort class
    }

    searchIcon.click(function(e) {
        e.stopPropagation();
        if (searchData !== '' && !searchInput.get(0).classList.contains("lg-search-expanded")) {
            // searchInput.val('');
            // searchData = '';
            fetchData("{{ $route }}");
        } else {
            searchInput.focus();
        }
    });

    html.click(function(e) {
        e.stopPropagation();
        if (searchData == ''){
            searchInput.get(0).classList.remove("lg-search-expanded");
        };
        // searchInput.val('');
        // searchData = '';
    });

    // Initial load
    fetchData("{{ $route }}");
});
</script>


<div id="popup-agr-overlay" class="statuses-wrap all-statuses-wrap">
<div id="popup-agr-body">
    <div class="modal-header">
        <h1 class="modal-title" id="">AGREEMENT - STATUSES</h1>
        <button type="button" onclick="hidePopup()" class="btn-close position-absolute" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    
<div class="popup-tabs">
  <div class="tab-buttons">
    <button class="tab-button active" data-tab="tab1">License</button>
    <button class="tab-button" data-tab="tab2">Lease</button>
    <button class="tab-button" data-tab="tab3">Purchase</button>
  </div>

  <div class="popup-tab-content active" id="tab1">
    <div class="status-box">
        <div class="status-txt">
            <p class="colorYellow">License Sent (0/2)</p>
            <span>The agreement was sent to the customer for signing</span>
        </div>
        <div class="circle-box orange"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p>Signed 1/2</p>
            <span>The agreement is signed by the customer</span>
        </div>
        <div class="circle-box black"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p>Signed 2/2 (wait4pay)</p>
            <span>The agreement is signed by both parties</span>
        </div>
        <div class="circle-box black"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-success">Active</p>
            <span>The license is active</span>
        </div>
        <div class="circle-box green"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-secondary">Inactive</p>
            <span>The license is inactive</span>
        </div>
        <div class="circle-box grey"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Canceled</p>
            <span>The license has been cancelled</span>
        </div>
        <div class="circle-box red"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Payment failed</p>
            <span>The transaction attempt has failed</span>
        </div>
        <div class="circle-box red"></div>
    </div>
  </div>
  <div class="popup-tab-content" id="tab2">
    <div class="status-box">
        <div class="status-txt">
            <p class="colorYellow">License Sent (0/2)</p>
            <span>The agreement was sent to the customer for signing</span>
        </div>
        <div class="circle-box orange"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p>Signed 1/2</p>
            <span>The agreement is signed by the customer</span>
        </div>
        <div class="circle-box black"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p>Signed 2/2 (wait4pay)</p>
            <span>The agreement is signed by both parties</span>
        </div>
        <div class="circle-box black"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-blue">Awaiting Delivery (paid)</p>
            <span>The deposit has been paid, and the customer is awaiting delivery</span>
        </div>
        <div class="circle-box blue"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-success">Active</p>
            <span>Machines delivered. Monthly payments begin</span>
        </div>
        <div class="circle-box green"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-secondary">Inactive</p>
            <span>The lease is inactive</span>
        </div>
        <div class="circle-box grey"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Canceled</p>
            <span>The lease has been cancelled</span>
        </div>
        <div class="circle-box red"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Payment failed</p>
            <span>The transaction attempt has failed</span>
        </div>
        <div class="circle-box red"></div>
    </div>
  </div>
  <div class="popup-tab-content" id="tab3">
    <div class="status-box">
        <div class="status-txt">
            <p class="colorYellow">License Sent (0/2)</p>
            <span>The agreement was sent to the customer for signing</span>
        </div>
        <div class="circle-box orange"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p>Signed 1/2</p>
            <span>The agreement is signed by the customer</span>
        </div>
        <div class="circle-box black"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p>Signed 2/2 (wait4pay)</p>
            <span>The agreement is signed by both parties</span>
        </div>
        <div class="circle-box black"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-blue">Awaiting Delivery (paid)</p>
            <span>Paid, the customer is awaiting delivery</span>
        </div>
        <div class="circle-box blue"></div>
    </div>
    <div class="status-box">
        <div class="status-txt">
            <p class="text-success">Completed</p>
            <span>The machines have been delivered</span>
        </div>
        <div class="circle-box green"></div>
    </div>

    <div class="status-box">
        <div class="status-txt">
            <p class="text-danger">Payment failed</p>
            <span>The transaction attempt has failed</span>
        </div>
        <div class="circle-box red"></div>
    </div>
  </div>
</div>
</div>
</div>


