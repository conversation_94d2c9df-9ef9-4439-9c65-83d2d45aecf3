<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<!-- CSRF Token -->
<meta name="csrf-token" content="{{ csrf_token() }}">
<title>{{ config('app.name', 'ERP') }}</title>

<!-- Scripts -->
@vite(['resources/sass/app.sass', 'resources/js/app.js', 'resources/css/custom.css'])
<style>
    .schedule { background:#fff; padding:1rem; border-radius:4px; box-shadow:0 2px 4px rgba(0,0,0,0.1); margin-bottom:1.5rem; }
    h2 { font-size:18px;text-transform:uppercase; }
    .schedule ul { list-style:none; padding:0; }
    .schedule li { margin:0.5rem 0; }
    .schedule li strong { display:inline-block; width:140px; }
    #email_confirm {
        position: absolute;
        bottom: 10px;
        right: 10px;
        height: 20px;
        font-size: 9px;
        text-transform: uppercase;
        background: #000;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
        border-radius: 20px;
        cursor: pointer;
    }
    #email_confirm:hover {
        background: #999;
    }
    .w1000px {
        width: calc(100% - 100px);
        max-width: 1000px;
    }
    .w1000px h3 {
        font-size: 24px;
        line-height: 1.4;
    }
    #payment-form {
        width: 100%;
        max-width: 500px;
    }
    .payment-page{
        overflow: visible !important;
    }
    .grandtotal-wrap {
        padding: 10px 30px;
        background: #000;
        color: #fff;
    }
    /**/
    .payment-page .border-bottom-grey {
        border-bottom: 1px solid #f0f0f0;
    }
    #payment-form-wrap h2 {
        margin-bottom: 25px !important; 
        line-height: 1.4;
    }
    #payment-form-wrap p {
        font-size: 14px;
        line-height: 25px;
    }
    #payment-form-wrap b {
        font-weight: 500;
    }
    #payment-form label {
        text-transform: uppercase; 
        font-size: 11px; 
        margin-bottom: 9px;
    }
    #payment-form input {
        height: 38px; 
        max-height: 38px; 
        min-height: 38px !important;
    }
    #card-panel p {
        font-size: 28px !important; 
        font-weight: 400;
    }
    .border-bottom, .payouts th, .payouts tr:last-of-type td {
        border-bottom: 1px solid #272727 !important;
    }
    .next-payment {
        color: #969696;
    }
    .p-TabIcon--selected {fill:#000 !important;}
    #payment-element {margin-bottom: 2rem !important;}

    @media(max-width: 768px){ 
        .w1000px {
        	width: calc(100% - 50px);
	        max-width: 1000px;
        }
        #payment-form-wrap .row {
            display: flex;
            flex-direction: column;
        }
        #payment-form-wrap .row .col-6 {
            width: 100%;
        }
        #payment-form-wrap .row .col-5 {
            width: 100%;
            margin: 20px 0 0 0;
        }
        .w1000px h3 {
	        font-size: 20px;
        }
    }

</style>
</head>
<body class="font-sans antialiased payment-page">
<div class="d-flex justify-content-center align-items-center border-bottom-grey px-4 py-5">
    <svg xmlns="http://www.w3.org/2000/svg" width="134" height="15.001" viewBox="0 0 134.799 15.001">
        <path id="Path_7120" data-name="Path 7120" d="M-143.154-1.223a.4.4,0,0,0,.4.4h7.625a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4h-5.313V-15.016a.41.41,0,0,0-.4-.4h-1.917a.4.4,0,0,0-.4.4Zm11.73.4h1.813a.628.628,0,0,0,.6-.417l1.021-2.25h6.23l1.021,2.25a.6.6,0,0,0,.6.417h1.813a.368.368,0,0,0,.354-.542l-6.417-14.022a.354.354,0,0,0-.354-.229h-.208a.374.374,0,0,0-.354.229l-6.48,14.022A.368.368,0,0,0-131.424-.827Zm4.459-4.979,2.042-4.584h.063l2.083,4.584ZM-115.132-8.1a7.429,7.429,0,0,0,7.48,7.459,10.527,10.527,0,0,0,5.167-1.354.4.4,0,0,0,.167-.333V-7.661a.392.392,0,0,0-.375-.4h-4a.383.383,0,0,0-.4.4v1.646a.379.379,0,0,0,.4.375h1.667v1.771a6.518,6.518,0,0,1-2.479.521,4.74,4.74,0,0,1-4.688-4.771,4.777,4.777,0,0,1,4.667-4.834,4.723,4.723,0,0,1,3.146,1.208.356.356,0,0,0,.542,0l1.292-1.354a.4.4,0,0,0-.021-.583,7.876,7.876,0,0,0-5.084-1.938A7.483,7.483,0,0,0-115.132-8.1Zm17.959,6.875a.4.4,0,0,0,.4.4h1.917a.41.41,0,0,0,.4-.4V-6.536h2.313l2.771,5.521a.357.357,0,0,0,.333.188h2.188a.394.394,0,0,0,.354-.6l-2.854-5.292a4.6,4.6,0,0,0,3.084-4.209,4.513,4.513,0,0,0-4.542-4.479h-5.959a.4.4,0,0,0-.4.4Zm2.729-7.542v-4.146h3.4a2.073,2.073,0,0,1,2.063,2.021,2.122,2.122,0,0,1-2.063,2.125Zm13.23,7.542a.4.4,0,0,0,.4.4h8.48a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-78.5V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-78.5v-3.417h6.167a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm14.334,0a.4.4,0,0,0,.4.4H-58a.4.4,0,0,0,.4-.4V-2.932a.4.4,0,0,0-.4-.4H-64.17V-6.974h5.146a.4.4,0,0,0,.4-.4V-9.1a.41.41,0,0,0-.4-.4H-64.17v-3.417H-58a.4.4,0,0,0,.4-.4v-1.708a.4.4,0,0,0-.4-.4h-8.48a.4.4,0,0,0-.4.4Zm21.022,0a.41.41,0,0,0,.4.4h1.938a.41.41,0,0,0,.4-.4V-15.016a.41.41,0,0,0-.4-.4h-1.938a.41.41,0,0,0-.4.4Zm7.646-.083a.382.382,0,0,0,.4.479h1.875a.4.4,0,0,0,.375-.313l1.208-7.9h.063l3.688,8.188a.374.374,0,0,0,.354.229h.375a.354.354,0,0,0,.354-.229l3.646-8.188h.063l1.229,7.9a.452.452,0,0,0,.4.313h1.9a.362.362,0,0,0,.375-.479l-2.458-14a.351.351,0,0,0-.375-.313h-.333a.359.359,0,0,0-.354.208l-4.584,9.834h-.063l-4.584-9.834a.38.38,0,0,0-.354-.208h-.333a.351.351,0,0,0-.375.313Zm20.48-.938A6.7,6.7,0,0,0-13.147-.619c2.938,0,4.792-1.979,4.792-4.125,0-2.688-2.333-3.9-4.354-4.709-1.688-.688-2.458-1.354-2.458-2.333a1.659,1.659,0,0,1,1.833-1.458,6.854,6.854,0,0,1,2.9,1.146.548.548,0,0,0,.729-.25l.792-1.188a.516.516,0,0,0-.125-.688,7.335,7.335,0,0,0-4.167-1.4c-3.313,0-4.688,2.146-4.688,4,0,2.458,1.958,3.709,3.917,4.5,1.75.708,2.646,1.438,2.646,2.5a1.739,1.739,0,0,1-1.9,1.6,6.9,6.9,0,0,1-3.146-1.292.483.483,0,0,0-.708.146l-.75,1.292C-18.022-2.536-17.918-2.432-17.73-2.244Z" transform="translate(143.154 15.62)"/>
    </svg>
</div>
<div class="w1000px mx-auto my-7">
    <div class="" id="payment-form-wrap">
        <div class="row">
            <div class="col-6">
                <h2 class="mb-4">Payment of '{{ $payment->description }}' for {{ $object_of_payment }} {{ $type != 'license' ? ucfirst($type) : '' }}</h2>
                <p class="mb-0">You will be charged for '{{ $payment->description }}' amount of <b>${{ number_format($payment->payment_amount / 100, 2) }}</b>.</p>
                <p>Also, you will be notified about rest of the payment by agreement manager, once the machines are in production.</p>
                <form id="payment-form" class="mt-6">
                    <div id="email_confirm_wrap">
                        <label for="email">Your email</label>
                        <div class="input-placeholder">
                            <input type="email" id="email" name="email" class="form-control" placeholder="Email address" value="{{ $customer->owner->email }}" />
                            <span id="email_confirm">Confirm</span>
                        </div>
                    </div>
                    <div id="error-message" role="alert" style="color:red; margin-top:0.5rem;"></div>
                    <div id="payment-element" style="margin-bottom:1rem;"></div>
                    <button id="submit" class="btn btn-primary" style="display: none;">Finish Payment</button>
                </form>
                <div id="payment-instructions" style="margin-top:1rem;"></div>
            </div>
            <div class="col-5 offset-1">
                <div class="grandtotal-wrap w-100">
                    <table class="table table-borderless custom-fs-14px">
                        <tbody>
                            <tr class="total">
                                <td class="border-light border-bottom pt-2 pb-4" colspan="2">{{ $model->checkoutRow }}</td>
                            </tr>
                            @if ($model->qtyRow)
                            <tr class="total-deposit">
                                <td class="pt-4 pb-2"># of Machines:</td>
                                <td class="text-end pt-4 pb-2">{{ $model->qtyRow }}</td>
                            </tr>
                            @endif 
                            <tr class="total">
                                <td class="@if ($model->qtyRow) py-2 @else pt-4 pb-2 @endif">Total:</td>
                                <td class="text-end @if ($model->qtyRow) py-2 @else pt-4 pb-2 @endif"><span>{{ $model->total }}</span></td>
                            </tr>
                            @if ($model->deposit_amount)
                            <tr class="total-deposit">
                                <td class="border-light border-bottom pt-2 pb-4">Deposit:</td>
                                <td class="border-light border-bottom text-end pt-2 pb-4"><span>${{ number_format(($model->deposit_amount * ($model->machine_quantity ? $model->machine_quantity : 1)) / 100, 2) }}</span></td>
                            </tr>
                            @endif 
                            <tr class="grand-total custom-fs-14">
                                <td class="pt-4 pb-2 fw-medium">@if ($model->deposit_amount) Next @else Total @endif payment:<br><span class="next-payment fw-normal fw-regular fs-12px">{{ $type != 'purchase' ? \Carbon\Carbon::parse($next_payment->payment_date)->format('m/d/Y') : '' }}</span></td>
                                <td class="text-end pt-4 pb-2 fw-medium"><span>${{ number_format($next_payment->payment_amount / 100, 2) }}</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="row" id="payment-success" style="display: none;">
        <div class="page-title ttl-dropdown no-border-btm">
            <div class="d-flex align-items-center justify-center flex-column w-100 text-center py-8 mt-8 gap-5">
                <img src="{{ asset('/success.png') }}" alt="" class="img-fluid" style="width: 200px;"/>
                <h3 class="text-uppercase">Payment for Machine Purchase was successful</h3>
                <p class="f-14">Amount of {{ number_format($payment->payment_amount / 100, 2) }} has been paid.</p>
                {{-- <a href="{{ route('admin.licenses.show', ['customer' => $customer, 'license' => $license]) }}" class="btn btn-primary">Back</a> --}}
            </div>
        </div>
    </div>
    <div class="row" id="payment-processing" style="display: none;">
        <div class="page-title ttl-dropdown no-border-btm">
            <div class="d-flex align-items-center justify-center flex-column w-100 text-center py-8 mt-8 gap-5">
                <img src="{{ asset('/success.png') }}" alt="" class="img-fluid" style="width: 200px;"/>
                <h3>Payment for Machine Purchase</h3>
                <p>Amount of {{ number_format($payment->payment_amount / 100, 2) }} has been paid and is being processed now. <br>It can take up to 1-2 business days.</p>
                {{-- <a href="{{ route('admin.licenses.show', ['customer' => $customer, 'license' => $license]) }}" class="btn btn-primary">Back</a> --}}
            </div>
        </div>
    </div>
</div>
<script src="https://js.stripe.com/basil/stripe.js"></script>
<script type="module">
const stripe = Stripe("{{ env('STRIPE_KEY') }}");
let elements;
let clientSecret;
let ci;

const appearance = {
    theme: 'flat',
    variables: { 
        colorPrimary:'#000', 
        colorBackground:'#fdfdfd', 
        colorText:'#000', 
        colorTextSecondary:'#000', 
        fontFamily:'Graphik, sans-serif, system-ui', 
        fontSizeBase:'14px',
        spacingUnit:'4px', 
        iconColor:'#000',
        borderRadius:'8px' 
    },
    rules: { 
        '.Tab': {
            border: '1px solid #dddddd',
            // boxShadow: '0px 1px 1px rgba(0, 0, 0, 0.03), 0px 3px 6px rgba(18, 42, 66, 0.02)',
        },
        '.TabLabel': {
            fontWeight:'400',
        },
        // '.Tab--selected': {
        //     borderColor: '#E0E6EB',
        // },
        '.Input':{
            border:'1px solid #ddd',
            padding:'10px 15px',
            letterSpacing: '0.7px',
        }, 
        '.Input:focus':{
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.05)'
        }, 
        '.Error':{
            letterSpacing: '0.7px',
            fontSize: '10px',
            letterSpacing: '0.5px',
            marginTop: '7px',
        }, 
        '.Label':{
            color:'#000',
            fontWeight:'300',
            textTransform: 'uppercase',
            marginBottom: '10px',
            marginTop: '10px',
            fontSize: '11px',
            letterSpacing: '0.55px',
        }, 
        '.Tab--selected':{
            colorBackground: '#F8F8F8',
            colorText: '#000',
        }, 
        '.p-TermsText':{
            fontFamily: 'GraphikRegular',
            fontSizeBase:'14px',
        }, 
        '.PickerItem':{
            border: '1px solid #ddd',
        }
    },
};

async function initializePayment() {
    const emailInput = document.getElementById('email');
    const emailWrap = document.getElementById('email_confirm_wrap');
    const email = emailInput.value.trim();
    if (!email) {
        document.getElementById('error-message').textContent = 'Please enter your email.';
        return;
    }
    document.getElementById('error-message').textContent = '';
    document.getElementById('payment-instructions').innerHTML = '';

    @if ($payment->stripeCustomer_id && $payment->stripe_session_id)
        clientSecret = '{{ $payment->stripe_session_id }}';
        // 2) Unmount existing element if any
        if (elements) {
            const existing = elements.getElement('payment');
            if (existing) existing.unmount();
        }

        // 3) Define Appearance + Prefill email
        elements = stripe.elements({
            clientSecret,
            appearance,
            fields: {
                billingDetails: {
                    email: { value: email }
                }
            }
        });

        // 4) Mount the Payment Element
        const paymentElement = elements.create('payment', {
            layout: 'tabs'
        });
        paymentElement.mount('#payment-element');

        // 5) Hide the standalone email input
        emailWrap.style.display = 'none';
        document.getElementById('submit').style.display = 'block';

    @else
        // 1) Create PaymentIntent + Customer on backend
        var ajax = $.ajax({
            type: 'GET',
            url: '{{ route('payment.paymentIntent') }}',
            data: {
                amount: {{ $payment->payment_amount }},
                email
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.paymentIntent.status === 'requires_payment_method'){
                    clientSecret = data.clientSecret;
                    ci = data.customerId;
                    // 2) Unmount existing element if any
                    if (elements) {
                        const existing = elements.getElement('payment');
                        if (existing) existing.unmount();
                    }

                    // 3) Define Appearance + Prefill email
                    elements = stripe.elements({
                        clientSecret,
                        appearance,
                        fields: {
                            billingDetails: {
                                email: { value: email }
                            }
                        }
                    });

                    // 4) Mount the Payment Element
                    const paymentElement = elements.create('payment', {
                        layout: 'tabs'
                    });
                    paymentElement.mount('#payment-element');

                    // 5) Hide the standalone email input
                    emailWrap.style.display = 'none';
                    // document.getElementById('submit').disabled = false;
                    document.getElementById('submit').style.display = 'block';
                }
            },
            error: function (request, status, error) {
                alert(request.responseJSON.message);
                // console.log(status);
                // console.log(error);
                console.log('PHP Error');
            }
        });
    @endif

    // if(ajax.status == 500){
    //     console.log(ajax.responseJSON.message);
    // }
}

async function handleSubmit(e) {
    e.preventDefault();
    document.getElementById('submit').disabled = true;
    document.getElementById('error-message').textContent = '';

    // 1) Confirm without redirect to inspect next_action
    const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {},
        redirect: 'if_required',
    });

    console.log('paymentIntent: ', paymentIntent);
    
    // 2) Handle errors
    if (error) {
        document.getElementById('error-message').textContent = error.message;
        document.getElementById('submit').disabled = false;
        return;
    }

    // 3) Bank transfer instructions
    if (paymentIntent.next_action?.type === 'display_bank_transfer_instructions') {
        const instr = paymentIntent.next_action.display_bank_transfer_instructions;
        const amt = (instr.amount_remaining / 100).toFixed(2);
        document.getElementById('payment-instructions').innerHTML = `
            <h3>Send a bank transfer of \$${amt}</h3>
            <p><strong>Account:</strong> ${instr.financial_institution.name}</p>
            <p><strong>Routing Number:</strong> ${instr.financial_institution.routing_number}</p>
            <p><strong>Reference:</strong> ${instr.reference}</p>
            <p><a href="${instr.hosted_instructions_url}" target="_blank">
                View detailed instructions &raquo;
            </a></p>
        `;
        return;
    }

    // 4) ACH micro-deposit verification
    if (paymentIntent.next_action?.type === 'verify_with_microdeposits') {
        const url = paymentIntent.next_action.verify_with_microdeposits.hosted_verification_url;
        document.getElementById('payment-instructions').innerHTML = `
            <h3>Verify your bank account</h3>
            <p>Stripe has sent two small deposits. Please confirm the amounts to complete the payment.</p>
            <p><a href="${url}" target="_blank">Verify with Stripe &raquo;</a></p>
        `;
        return;
    }

    // 5) Final statuses
    if (paymentIntent.status === 'processing' || paymentIntent.status === 'succeeded') {
        // document.body.innerHTML = '<h1>Thank you—your payment succeeded!</h1>';
        $('#payment-form-wrap').hide();
        // $('#payment-success').show();
        // $('#payment-processing').hide();
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            type: 'POST',
            url: "{{ route('payment.process') }}",
            data: {
                type: '{{ $type }}',
                @if($type == 'purchase')
                    purchase_id: '{{ $payment->purchase_id }}',
                @elseif($type == 'lease')
                    lease_id: '{{ $payment->lease_id }}',
                @elseif($type == 'license')
                    license_id: '{{ $payment->license_id }}',
                @endif
                payment_id: '{{ $payment->id }}',
                paymentIntent_id: paymentIntent.id,
                stripe_session_id: clientSecret,
                stripeCustomer_id: ci,
                status: paymentIntent.status
            },
            dataType: 'json',
            success: function (data) {
                console.log(data);
                console.log('Success');
                window.location.href = "{{ route('payment.success', ['payment_id' => $payment->id]) }}";
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    } else {
        $('#payment-form-wrap').show();
        // $('#payment-success').hide();
        // $('#payment-processing').hide();
        document.getElementById('submit').disabled = false;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    document.getElementById('email_confirm').addEventListener('click', initializePayment);
    document.getElementById('payment-form').addEventListener('submit', handleSubmit);
});
</script>
</body>
</html>