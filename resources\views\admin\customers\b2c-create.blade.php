@extends('layouts.app')
@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('New b2c customer') }}</h3>
            <a href="{{ route('admin.customers.b2c.index') }}" class=" back-link">← Back</a>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.customers.b2c.store') }}" id="studio-location-form">
            @csrf
            <input name="type" value="b2c" style="display: none;" />
            <h5 class="form-section-title first-title">{{ __('status') }}</h5>
            @include('partials.forms.radio-group', [
                'field_name' => 'is_active',
                'field_label' => 'Radio group status',
                'values' => [
                    'status_active' => ['text' => 'Active', 'value' => '1'],
                    'status_inactive' => ['text' => 'Inactive', 'value' => '0'],
                ],
                'field' => 'status',
                'required' => 'required',
                'checked' => '1',
            ])

            <h5 class="form-section-title">{{ __('account info') }}</h5>

            {{-- @include('partials.forms.input', [
                    'field_name' => 'customer_name',
                    'field_label' => 'LICENSEE NAME *',
                    'field_type' => 'text',
                    'field_value' => old('customer_name', ''),
                ]) --}}

            @include('partials.forms.input', [
                'field_name' => 'owner_first_name',
                'field_label' => 'FIRST NAME *',
                'field_type' => 'text',
                'field_value' => old('owner_first_name', ''),
            ])

            @include('partials.forms.input', [
                'field_name' => 'owner_last_name',
                'field_label' => 'LAST NAME *',
                'field_type' => 'text',
                'field_value' => old('owner_last_name', ''),
            ])

            @include('partials.forms.input', [
                'field_name' => 'phone',
                'field_label' => 'PHONE #',
                'field_type' => 'text',
                'field_value' => old('phone', ''),
            ])

            @include('partials.forms.input', [
                'field_name' => 'email',
                'field_label' => 'EMAIL ADDRESS *',
                'field_type' => 'email',
                'field_value' => old('email', ''),
            ])

            @include('partials.forms.checkbox', [
                'field_name' => 'toggle',
                'field_to_toggle' => 'email2_container',
                'field_label' => 'Another email address',
                'wrap_class_name' => ' mb-5 toggle_bottom',
            ])

            @include('partials.forms.input', [
                'field_name' => 'email2',
                'field_label' => 'EMAIL ADDRESS',
                'field_type' => 'email',
                'field_class' => 'd-none email2_container',
            ])

            <h5 class="form-section-title">{{ __('Address info') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'location',
                'field_class' => 'mb-7',
                'field_label' => 'Radio group location',
                'values' => [
                    'location_usa' => ['text' => 'USA', 'value' => 'USA'],
                    'location_international' => ['text' => 'International', 'value' => 'International'],
                ],
                'field' => 'status',
                'required' => 'required',
                'checked' => 'USA',
            ])

            @include('partials.forms.input', [
                'field_name' => 'address',
                'field_label' => 'ADDRESS',
                'field_type' => 'text',
                'field_value' => old('address', ''),
            ])

            @include('partials.forms.input', [
                'field_name' => 'address2',
                'field_label' => 'ADDRESS 2',
                'field_type' => 'text',
                'field_value' => old('address2', ''),
            ])

            @include('partials.forms.input', [
                'field_name' => 'city',
                'field_label' => 'CITY',
                'field_type' => 'text',
                'field_value' => old('city', ''),
            ])


            @include('partials.forms.input', [
                'field_name' => 'zip',
                'field_label' => 'ZIP CODE',
                'field_type' => 'text',
                'field_value' => old('zip', ''),
            ])

            <div id="location_usa_container">
                @include('partials.forms.select', [
                    'field_name' => 'state_id',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('state_id', null),
                    'include_empty' => true,
                ])
            </div>

            <div id="location_international_container" style="display: none;">
                @include('partials.forms.select', [
                    'field_name' => 'country_id',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'countries',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('country_id', null),
                    'include_empty' => true,
                ])
            </div>

            {{-- <h5 class="form-section-title">{{ __('shipping address') }}</h5> --}}

            <div style="display: none;">
                @include('partials.forms.checkbox', [
                    'field_name' => 'use_billing_address',
                    'field_to_toggle' => 'shipping_container',
                    'field_label' => 'Use billing address',
                    'wrap_class_name' => 'mb-50 toggle_bottom',
                    'field_value' => old('use_billing_address', null),
                ])
            </div>
            <div class="shipping_container d-none">
                @include('partials.forms.radio-group', [
                    'field_name' => 'shipping_location',
                    'field_class' => 'mb-7',
                    'field_label' => 'Radio group location',
                    'values' => [
                        'shipping_location_usa' => ['text' => 'USA', 'value' => 'USA'],
                        'shipping_location_international' => [
                            'text' => 'International',
                            'value' => 'International',
                        ],
                    ],
                    'field' => 'shipping_location',
                    'required' => 'required',
                    'checked' => true,
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_address',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => old('shipping_address', ''),
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_city',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => old('shipping_city', ''),
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_zip',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => old('shipping_zip', ''),
                ])
                <div id="shipping_location_usa_container">
                    @include('partials.forms.select', [
                        'field_name' => 'shipping_state_id',
                        'field_label' => 'STATE',
                        'values' => $states_countries['states'],
                        'field' => 'shipping_state',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => old('shipping_state_id', ''),
                        'include_empty' => true,
                    ])
                </div>
                <div id="shipping_location_international_container" style="display: none">
                    @include('partials.forms.select', [
                        'field_name' => 'shipping_country_id',
                        'field_label' => 'COUNTRY',
                        'values' => $states_countries['countries'],
                        'field' => 'shipping_country',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => old('shipping_country_id', ''),
                        'include_empty' => true,
                    ])
                </div>
            </div>
            <h5 class="form-section-title">{{ __('password management') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'password',
                'field_label' => 'NEW PASSWORD *',
                'field_type' => 'password',
            ])

            @include('partials.forms.input', [
                'field_name' => 'password_confirmation',
                'field_label' => 'REPEAT NEW PASSWORD *',
                'field_type' => 'password',
            ])
            <div class="cust-pass-text">
                <p>The password field must be at least 8 characters.<br>
                    The password field must contain at least one uppercase and one lowercase letter.</p>
            </div>
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{ route('admin.customers.create') }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        function toggleLocationContainers() {
            if ($('#location_international').is(':checked')) {
                $('#location_usa_container').hide();
                $('#location_international_container').show();
            } else if ($('#location_usa').is(':checked')) {
                $('#location_usa_container').show();
                $('#location_international_container').hide();
            }
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        }

        $('#location_international, #location_usa').on('click', toggleLocationContainers);

        $(document).ready(toggleLocationContainers);

        // Initialize Google Places Autocomplete
        window.initGooglePlaces = function() {
            const autocompleteService = new GooglePlacesAutocomplete();

            const fieldConfigs = [
                {
                    inputId: 'address',
                    fields: {
                        address: 'address',
                        city: 'city',
                        zip: 'zip',
                        state: 'state_id',
                        country: 'country_id'
                    },
                    locationRadios: {
                        usa: 'location_usa',
                        international: 'location_international'
                    }
                }
            ];

            autocompleteService.init(fieldConfigs);
        };

        // Load Google Maps API
        const googleApiKey = '{{ config("services.google_maps.api_key") }}';
        if (googleApiKey && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
            GooglePlacesAutocomplete.loadGoogleMapsAPI(googleApiKey, window.initGooglePlaces);
        }
    });
</script>

<!-- Google Places Autocomplete Script -->
@vite('resources/js/google-places-autocomplete.js')
