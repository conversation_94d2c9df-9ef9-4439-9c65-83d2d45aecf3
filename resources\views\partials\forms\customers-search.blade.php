@if(count($customers) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase" data-orderParam="{{ $customers[0]->orderParam }}" data-orderType="{{ $customers[0]->orderType }}">
        <div id="id">
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $customers[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="name" class="cust-name sortable-list-header {{ $customers[0]->orderParam == 'name' ? 'active' : '' }}" data-sort="name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="owner" class="owner-name sortable-list-header {{ $customers[0]->orderParam == 'owner' ? 'active' : '' }}" data-sort="owner">{{ __('Licensee') }}
            <div class="sort-icon asc"></div>
        </div>
        <div class="licenses-preview ">{{ __('Location') }}</div>
        <div class="exclusivity-preview">{{ __('Exclusivity') }}</div>
        <div class="leases-preview ">{{ __('Lease') }}</div>
        <div id="remaining" class="remaining-preview sortable-list-header {{ $customers[0]->orderParam == 'remaining' ? 'active' : '' }}" data-sort="remaining">{{ __('Remaining') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="users.is_active" class="cust-statusbadge sortable-list-header {{ ($customers[0]->orderParam == 'users.is_active') ? 'active' : '' }}" data-sort="users.is_active">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
        <div class="round-button-dropdown"></div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button" onclick="document.querySelectorAll('.open-multi-actions:checked').length > 1 ? document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete these customers?') }}' : document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete this customer?') }}'">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1" id="multi-delete-title">{{ __('Are you sure you want to delete these customers?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.customers.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">
            @foreach($customers as $customer)
                <div class="list-group-item list-group-item-action d-grid align-items-center fw-normal">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                               value="{{ $customer->id }}"
                               id="flexCheckDefault_{{$customer->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$customer->id}}"></label>
                    </div>
                    <p class="my-0 hide-transp">{{$customer->sequential_id}}</p>
                    <p class="table-name-col cust-name"><a href="{{ route('admin.customers.dashboard', $customer) }}" style="text-decoration: none;">{{$customer->name}}</a></p>
                    <p class="my-0 normal text-secondary owner-name"><a href="{{ route('admin.customers.dashboard', $customer) }}" style="text-decoration: none;color: #969696;">{{$customer->owner->first_name}} {{$customer->owner->last_name}}@if($customer->licensee2_first_name != '' OR $customer->licensee2_last_name != ''),<br> {{ $customer->licensee2_first_name }} {{ $customer->licensee2_last_name }}@endif</a></p>
                    <a href="" class="licenses-preview remove-item"  data-bs-toggle="modal" data-bs-target="#licensePreviewModal{{$customer->id}}">
                        <div>
                            @if($customer->licenses_count > 0)
                                <p class="my-0">{{$customer->licenses_count}} {{($customer->licenses_count > 1) ? 'Locations' : 'Location'}}</p>
                                <p class="my-0 text-success">
                                    {{$admin_currency_symbol}}{{number_format($customer->licenses_remaining, 2)}} left
                                </p>
                            @else
                                <p class="my-0">-</p>
                            @endif
                        </div>
                    </a>
                    <a href="" class="exclusivity-preview remove-item"  data-bs-toggle="modal" data-bs-target="#exclusivityPreviewModal{{$customer->id}}">
                        <div>
                            @if($customer->exclusivity_count > 0)
                                {{--                                <p class="my-0">{{$customer->exclusivity_count}} Exclusivity </p>--}}
                                {{--                                <p class="my-0">${{$customer->exclusivity_monthly_average}}/month</p>--}}
                                <p class="my-0">{{$customer->exclusivity_count}} {{($customer->exclusivity_count > 1) ? 'Exclusivities' : 'Exclusivity'}}</p>
                                <p class="my-0 text-warning">
                                    {{$admin_currency_symbol}}{{number_format($customer->exclusivity_remaining, 2)}} left
                                </p>
                            @else
                                <p class="my-0">-</p>
                            @endif
                        </div>
                    </a>
                    <a href="" class="leases-preview remove-item"  data-bs-toggle="modal" data-bs-target="#leasesPreviewModal{{$customer->id}}">
                        <div>
                            @if($customer->lease_count > 0)
                                <p class="my-0">{{$customer->lease_count}} {{($customer->lease_count > 1) ? 'Leases' : 'Lease'}}</p>
                                <p class="my-0 text-success">
                                    {{$admin_currency_symbol}}{{number_format($customer->lease_remaining, 2)}} left
                                </p>
                            @else
                                <p class="my-0">-</p>
                            @endif
                        </div>
                    </a>
                    <div class="remaining-preview">
                        @if($customer->remaining != 0)
                            <p class="my-0">
                                {{$admin_currency_symbol}}{{number_format($customer->remaining, 2)}}
                            </p>
                        @else
                            <p class="my-0">-</p>
                        @endif
                    </div>

                    <div class="cust-statusbadge">
                        @include('partials.status-badge', [
                            'status' => $customer->owner->is_active ? 'active' : 'inactive',
                        ])
                    </div>
                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="customers-dropdown-content">
                            <a href="{{ route('admin.customers.dashboard', $customer) }}">{{ __('View') }}</a>
                            <a href="{{ route('admin.customers.edit', $customer) }}">{{ __('Edit') }}</a>
                            <a href=""  class="contact-customer" data-bs-toggle="modal" data-bs-target="#emailModalUser{{$customer->owner->id}}">{{ __('Email Customer') }}</a>
                            <a href=""  class="add-note" data-bs-toggle="modal" data-bs-target="#addInternalNote{{$customer->id}}">{{ __('Add a note') }}</a>
                            <a href="" class="terminate" data-bs-toggle="modal"
                               data-bs-target="#deleteModal{{'Customer'}}{{$customer->id}}">{{ __('Terminate') }}</a>
                        </div>
                    </div>
                </div>
                @include('partials.modals.contact-customer', [
                    'id' => $customer->owner->id,
                    'route' => route('admin.customers.contact', ['customer' => $customer]),
                    'title' => 'License',
                ])

                @include('partials.modals.note', [
                    'id' => $customer->id,
                    'route' => route('admin.notes.store', ['customer' => $customer]),
                ])
                @include('partials.modals.delete', [
                  'type' => 'Customer',
                  'id' => $customer->id,
                  'route' => route('admin.customers.destroy', ['customer' => $customer]),
                  'title' => $customer->name,
                ])
                @include('partials.modals.licenses-preview', [
                  'id' => $customer->id,
                  'customer' => $customer,
                ])
                @include('partials.modals.exclusivity-preview', [
                  'id' => $customer->id,
                  'customer' => $customer,
                ])
                @include('partials.modals.leases-preview', [
                 'id' => $customer->id,
                 'customer' => $customer,
               ])


            @endforeach
            <div id="paginate paginate-customers">
                @if($customers)
                    <div class="">
                        {!! $customers->links() !!}
                    </div>
                @endif
            </div>
        </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const customersCount = @json($customers).total;
    const descriptiveLabel = customersCount === 1 ? ' Item' : ' Items';
    $('#customersCount').text(customersCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });
    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.customers.delete-multiple') }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });
    $('.dropdown-content').on('click', function(event) {
        event.stopPropagation();
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.contact-customer').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    $('.add-note').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    $('.terminate').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    $('.licenses-preview').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

</script>
