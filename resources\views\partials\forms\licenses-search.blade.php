@if(count($licenses) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $licenses[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="studio.name" class="sortable-list-header {{ $licenses[0]->orderParam == 'studio.name' ? 'active' : '' }}" data-sort="studio.name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>

        <div id="type" class="sortable-list-header {{ $licenses[0]->orderParam == 'type' ? 'active' : '' }}" data-sort="type">{{ __('Type') }}
            <div class="sort-icon asc"></div>
        </div>
        {{-- <div>{{ __('Zip') }}</div> --}}
        <div id="price" class="sortable-list-header {{ $licenses[0]->orderParam == 'price' ? 'active' : '' }}" data-sort="price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{-- {{ __('Remaining') }} --}}</div>
        <div>{{ __('Expires On') }}</div>
        <div id="is_active" class="sortable-list-header {{ $licenses[0]->orderParam == 'is_active' ? 'active' : '' }}" data-sort="is_active">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these licenses?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.licenses.delete-multiple', ['customer' => $customer]) }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach($licenses as $license)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <div class="form-check mb-0">
                    <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                            value="{{ $license->id }}"
                            id="flexCheckDefault_{{$license->id}}">
                    <label class="form-check-label d-none" for="flexCheckDefault_{{$license->id}}"></label>
                </div>
                <p class="my-0 hide-transp">{{$license->sequential_id}}</p>
                <a href="{{ route('admin.licenses.show', ['customer' => $customer, 'license' => $license, 'tab' => 'profile']) }}" style="text-decoration: none;" class="my-0 d-flex align-items-center gap-1 medium">
                    <!-- {{$license->studio->zip}}/{{$license->studio->city}}, {{ $license->studio->name }} #{{$license->sequential_id}} 
                    {{-- ({{ $license->note_count }}) --}} -->
                        {{$license->studio->name}}
                        @if($license->type == 'exclusivity') <img src="{{ asset('/exclusivity-icon.svg') }}" alt="exclusivity" class="ms-1"> @endif
                </a>
                <p class="my-0 text-secondary">{{ucfirst($license->type)}}</p>
                {{-- <p class="my-0">{{$license->studio->zip}}</p> --}}
                <div class="table-div">
                    <p class="my-0 text-success">
                        {{$admin_currency_symbol}}{{number_format($license->price, 2)}}
                    </p>
                    <p class="my-0">/year</p>
                </div>
{{--                    @if($license->type === \App\Helpers\Constants::LICENSE_TYPES[0])--}}
                <div class="table-div">
                    {{-- <p class="my-0 text-danger">{{$admin_currency_symbol}}{{number_format($license->remaining['paymentSum'], 2)}}</p>
                    <p class="my-0"> {{$license->remaining['count']}}/{{$license->duration}} years</p> --}}
                </div>
{{--                @else--}}
{{--                    <p class="my-0">-</p>--}}
{{--                @endif--}}
                <p class="my-0">{{\Carbon\Carbon::parse($license->expires)->format('m/d/Y')}}</p>
                <div class="status-div">
                    @include('partials.customer-licenses-status-badge', [
                        'status' => $license->status,
                    ])
                </div>
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                <script>
                    function openIframePopup(url){
                        var iframe = document.createElement('iframe');
                        iframe.src = url;
                        var wrapper = document.createElement('div');
                        var close_iframe = document.createElement('span');
                        close_iframe.classList.add('close_iframe');
                        close_iframe.textContent = '×';
                        close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
                        wrapper.id = 'iframe-popup';
                        document.body.appendChild(wrapper);
                        wrapper.appendChild(iframe);
                        wrapper.append(close_iframe);
                        iframe.focus();
                        iframe.contentWindow.focus(); 
                    }
                </script>
                    <div class="dropdown-content" id="licences-dropdown-content">
                        @if ($license->status == \App\Helpers\Constants::LICENSE_STATUSES['Signed 1/2'])
                        <a href="javascript:;" onclick="openIframePopup('{{ $license->sign_url }}')">{{ __('SIGN NOW') }}</a>                            
                        @endif
                        {{-- @if ($license->status == \App\Helpers\Constants::LICENSE_STATUSES['Signed 2/2 (wait4pay)'])
                        <a href="{{ route('admin.licenses.payment-link', ['customer' => $customer, 'license' => $license]) }}">{{ __('Send Payment Link') }}</a>
                        @endif --}}
                        @if ($license->status != '0' AND $license->agreement?->status != '5')
                        <a href="javascript:void(0);" class="download-agreement" data-id="{{ $license->agreement?->id }}">
                            {{ __('Read Agreement') }}
                        </a>
                        {{-- <a href="javascript:void(0)">{{ __('Send to customer') }} </a> --}}
                        @endif
                        <a href="{{ route('admin.licenses.edit', ['customer' => $customer, 'license' => $license]) }}">{{ __('Edit') }}</a>
                        <a href=""  class="cancel-license text-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{'License'}}{{$license->id}}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </div>
            @include('partials.modals.cancel', [
                'type' => 'License',
                'id' => $license->id,
                'route' => route('admin.licenses.change-status', ['customer' => $customer, 'license' => $license, 'is_active' => '0', 'status' => '6']),
                'title' => 'License',
            ])

        @endforeach
        <div id="paginate paginate-licences">
            @if($licenses)
                <div class="">
                    {!! $licenses->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    $(document).ready(function() {
        $(document).on('click', '.close_iframe', function(event) {
            $('#iframe-popup').remove();
        });
    });
    function clearLicenseActiveTab() {
        localStorage.removeItem('licenseActiveTab');
    }

    const licensesCount = @json($licenses).total;
    const descriptiveLabel = licensesCount === 1 ? ' Item' : ' Items';
    $('#licensesCount').text(licensesCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.cancel-license').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.licenses.delete-multiple', ['customer' => $customer]) }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });
</script>
