<?php

namespace App\Services\Admin\License;

use App\Helpers\Constants;
use App\Helpers\CurrencyHelper;
use App\Jobs\SendAgreement;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\ConversionRate;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\License;
use App\Models\Studio;
use App\Models\LicenseNotes;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

use Stripe\Stripe;
use Stripe\Checkout\Session;
use App\Mail\SendPaymentLink;


class LicenseService implements ILicenseService
{
    public function store(array $data, Customer $customer)
    {
        DB::beginTransaction();
        try {
            $data['studio']['location'] = $data['location'] ?? null;
            $studio = Studio::create($data['studio']);
            $data['studio_id'] = $studio->id;
            $data['customer_id'] = $customer->id;
            $settings = AdminSettings::first();

            if ($settings->currency->code !== Currency::USD) {
                $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);
                $data['price'] = round($currencyToDollarRate * $data['price'] * 100);
                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                }
            } else {
                $data['price'] = round($data['price'] * 100);
                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                }
            }
            $data['initial_currency_id'] = $settings->currency_id;
            $license = License::create($data);
            if (isset($data['description']) and $data['description'] != '') {
                LicenseNotes::create([
                    'license_id' => $license->id,
                    'body' => $data['description']
                ]);
            }
            foreach (Currency::all() as $currency) {
                $license->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }

            if ($data['location'] == 'USA') {
                if ($data['package'] == 1) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MICRO;
                } else if ($data['package'] == 2) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MINI;
                } else {
                    $license_template = Agreement::LICENSE_TEMPLATE_MEGA;
                }
            } else {
                if ($data['package'] == 1) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MICRO_INT;
                } else if ($data['package'] == 2) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MINI_INT;
                } else {
                    $license_template = Agreement::LICENSE_TEMPLATE_MEGA_INT;
                }
            }
            SendAgreement::dispatch(Agreement::LICENSE_TYPE, $license_template, $customer, $license, Company::find($license->company_id));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, License $license, Customer $customer)
    {
        DB::beginTransaction();
        try {
            if (empty($data['customer_id'])) {
                $studioData = $data['studio'] ?? null;
                if ($studioData) {
                    $license->studio->update($studioData);
                }
            } else {
                $data['customer_id'] = $customer->id;
                $settings = AdminSettings::first();
                if ($settings->currency->code !== Currency::USD) {
                    $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);
                    $data['price'] = round($currencyToDollarRate * $data['price'] * 100);
                    if (isset($data['deposit_amount'])) {
                        $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                    }
                } else {
                    $data['price'] = round($data['price'] * 100);
                    if (isset($data['deposit_amount'])) {
                        $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                    }
                }

                $data['initial_currency_id'] = $settings->currency_id;
                $studioData = $data['studio'] ?? null;
                unset($data['studio']);
                $license->update($data);
                if ($studioData) {
                    $license->studio->update($studioData);
                }
                if (isset($data['description']) and $data['description'] != '') {
                    LicenseNotes::firstOrCreate([
                        'license_id' => $license->id,
                        'body' => $data['description']
                    ]);
                }

                $license->conversionRate()->delete();
                foreach (Currency::all() as $currency) {
                    $license->conversionRate()->create([
                        'currency_id' => $currency->id,
                        'rate' => CurrencyHelper::calculateRate($currency)
                    ]);
                }
            }
            if ($data['location'] == 'USA') {
                if ($data['package'] == 1) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MICRO;
                } else if ($data['package'] == 2) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MINI;
                } else {
                    $license_template = Agreement::LICENSE_TEMPLATE_MEGA;
                }
            } else {
                if ($data['package'] == 1) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MICRO_INT;
                } else if ($data['package'] == 2) {
                    $license_template = Agreement::LICENSE_TEMPLATE_MINI_INT;
                } else {
                    $license_template = Agreement::LICENSE_TEMPLATE_MEGA_INT;
                }
            }
            SendAgreement::dispatch(Agreement::LICENSE_TYPE, $license_template, $customer, $license, Company::find($license->company_id));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator
    {
        $query = License::with('studio', 'payments', 'conversionRate')->withCount('licenseNotes')
            ->where('customer_id', $customer->id);
        $perPage = ($perPage != 0) ? $perPage : $query->count();
        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('status', $status);
        });
        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('type', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhere('price', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                    ->orWhereHas('studio', function ($q) use ($searchData) {
                        $q->where('name', 'LIKE', '%' . $searchData . '%');
                        $q->orWhere('zip', 'LIKE', '%' . $searchData . '%');
                    })
                    ->orWhereHas('payments', function ($q) use ($searchData) {
                        $q->where('payment_date', 'LIKE', '%' . $searchData . '%');
                    });
            });
        });
        $query->orderBy($orderParam, $orderType);
        return $query->paginate($perPage);
    }

    public function delete(License $license)
    {
        $payments = $license->payments;
        foreach ($payments as $payment) {
            $payment->invoice()->delete();
            $payment->delete();
        }
        $license->delete();
    }
}
