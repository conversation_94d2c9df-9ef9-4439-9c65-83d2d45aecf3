<?php

/**
 * Standalone Adobe Sign Test Script
 * Run with: php test_adobe_standalone.php
 */

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        die("❌ .env file not found at: $path\n");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);
            
            // Remove quotes if present
            if (preg_match('/^"(.*)"$/', $value, $matches)) {
                $value = $matches[1];
            } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                $value = $matches[1];
            }
            
            $_ENV[$name] = $value;
            putenv("$name=$value");
        }
    }
}

// Load .env file
loadEnv(__DIR__ . '/.env');

echo "=== Adobe Sign Standalone Test ===\n\n";

// Test 1: Environment Variables
echo "1. Checking Environment Variables:\n";
$requiredVars = [
    'ADOBE_SIGN_ACCESS_TOKEN',
    'APP_URL',
    'ADOBE_SIGN_NAME',
    'ADOBE_SIGN_EMAIL'
];

$envIssues = [];
foreach ($requiredVars as $var) {
    $value = $_ENV[$var] ?? null;
    if (empty($value)) {
        echo "   ❌ $var: NOT SET\n";
        $envIssues[] = $var;
    } else {
        if ($var === 'ADOBE_SIGN_ACCESS_TOKEN') {
            echo "   ✓ $var: SET (length: " . strlen($value) . ")\n";
        } else {
            echo "   ✓ $var: $value\n";
        }
    }
}

if (!empty($envIssues)) {
    echo "\n❌ Missing required environment variables. Please check your .env file.\n";
    exit(1);
}

echo "\n";

// Test 2: Adobe Sign API Connectivity
echo "2. Testing Adobe Sign API Connectivity:\n";

$accessToken = $_ENV['ADOBE_SIGN_ACCESS_TOKEN'];
$baseUrl = 'https://api.na3.adobesign.com:443/api/rest/v6/baseUris';

// Initialize cURL
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $baseUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ],
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false,
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$curlError = curl_error($curl);
curl_close($curl);

if ($response === false) {
    echo "   ❌ cURL Error: $curlError\n";
    exit(1);
}

echo "   HTTP Status Code: $httpCode\n";

switch ($httpCode) {
    case 200:
        $responseData = json_decode($response, true);
        if (isset($responseData['apiAccessPoint'])) {
            echo "   ✓ SUCCESS: API Access Point: " . $responseData['apiAccessPoint'] . "\n";
            $apiAccessPoint = $responseData['apiAccessPoint'];
        } else {
            echo "   ❌ FAILED: Unexpected response format\n";
            echo "   Response: $response\n";
            exit(1);
        }
        break;
        
    case 401:
        echo "   ❌ FAILED: Unauthorized (401) - Access token is invalid or expired\n";
        echo "   Response: $response\n";
        exit(1);
        
    case 403:
        echo "   ❌ FAILED: Forbidden (403) - Insufficient permissions\n";
        echo "   Response: $response\n";
        exit(1);
        
    case 429:
        echo "   ❌ FAILED: Rate limit exceeded (429)\n";
        echo "   Response: $response\n";
        exit(1);
        
    default:
        echo "   ❌ FAILED: HTTP $httpCode\n";
        echo "   Response: $response\n";
        exit(1);
}

echo "\n";

// Test 3: Test Document Upload
echo "3. Testing Document Upload to Adobe Sign:\n";

// Create a simple test HTML file
$testHtml = '<!DOCTYPE html>
<html>
<head><title>Test Agreement</title></head>
<body>
<h1>Test Agreement</h1>
<p>This is a test document for Adobe Sign integration.</p>
<p>Customer: Test Customer</p>
<p>Date: ' . date('Y-m-d H:i:s') . '</p>
</body>
</html>';

$testFileName = 'test-agreement-' . time() . '.html';
$testFilePath = __DIR__ . '/' . $testFileName;
file_put_contents($testFilePath, $testHtml);

echo "   Created test file: $testFileName\n";

// Upload to Adobe Sign
$uploadUrl = $apiAccessPoint . 'api/rest/v6/transientDocuments?File-Name=' . urlencode($testFileName) . '&Mime-Type=text%2Fhtml';

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $uploadUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => [
        'File' => new CURLFile($testFilePath),
        'File-Name' => $testFileName,
        'Mime-Type' => 'text/html'
    ],
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer ' . $accessToken
    ],
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false,
]);

$uploadResponse = curl_exec($curl);
$uploadHttpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$uploadError = curl_error($curl);
curl_close($curl);

// Clean up test file
unlink($testFilePath);

if ($uploadResponse === false) {
    echo "   ❌ Upload cURL Error: $uploadError\n";
} else {
    echo "   Upload HTTP Status Code: $uploadHttpCode\n";
    
    if ($uploadHttpCode === 200 || $uploadHttpCode === 201) {
        $uploadData = json_decode($uploadResponse, true);
        if (isset($uploadData['transientDocumentId'])) {
            echo "   ✓ SUCCESS: Document uploaded successfully\n";
            echo "   Transient Document ID: " . $uploadData['transientDocumentId'] . "\n";
        } else {
            echo "   ❌ FAILED: No transient document ID in response\n";
            echo "   Response: $uploadResponse\n";
        }
    } else {
        echo "   ❌ FAILED: Upload failed with HTTP $uploadHttpCode\n";
        echo "   Response: $uploadResponse\n";
    }
}

echo "\n";

// Test 4: Summary
echo "4. Summary:\n";
echo "   ✓ Environment variables loaded\n";
echo "   ✓ Adobe Sign API accessible\n";
echo "   ✓ Document upload tested\n";
echo "\n";

echo "=== Test Complete ===\n";
echo "If all tests passed, your Adobe Sign integration should be working.\n";
echo "If you're still having issues, the problem is likely with:\n";
echo "1. Database conversion rates (run fix_conversion_rates.php)\n";
echo "2. License creation process not setting initial_currency_id\n";
echo "3. Missing conversion rate creation in license observers\n";
