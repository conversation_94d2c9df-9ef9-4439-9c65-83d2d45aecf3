<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Customer;

class CustomerAgreement extends Model
{
    use HasFactory;

    const STATUSES = [
        'Active' => 0,
        'Accepted' => 1,
        'Awaiting Delivery' => 2,
        'Inactive' => 3,
        'Signed (payment)' => 4,
    ];
    protected $fillable = [
        'customer_id',
        'type',
        'file_name',
        'file_path',
        'original_name',
        'location',
        'status'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}
