<?php

namespace App\Services\Admin\Supplier;

use App\Models\Supplier;
use Illuminate\Pagination\LengthAwarePaginator;

class SupplierService implements ISupplierService
{
    public function store(array $data)
    {
        $supplier = Supplier::create($data);
        $supplier->contact()->create($data['contact']);
    }

    public function update(array $data, Supplier $supplier)
    {
        $supplier->update($data);
        $supplier->contact()->update($data['contact']);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator
    {
        $query = Supplier::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('is_active', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('id', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('address', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('address2', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('zip', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('city', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function delete(Supplier $supplier)
    {
        $supplier->contact()->delete();
        $supplier->orders()->delete();
        $supplier->delete();
    }

    public function deleteMultiple(array $ids)
    {
        foreach ($ids as $id) {
            $supplier = Supplier::find($id);
            $this->delete($supplier);
        }
    }
}
