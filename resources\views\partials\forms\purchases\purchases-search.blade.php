@if(count($purchases) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-medium">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $purchases[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="machine.name" class="sortable-list-header {{ $purchases[0]->orderParam == 'machine.name' ? 'active' : '' }}" data-sort="machine.name">{{ __('Machine') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="lease.machine_quantity" class="sortable-list-header {{ $purchases[0]->orderParam == 'lease.machine_quantity' ? 'active' : '' }}" data-sort="lease.machine_quantity">{{ __('Qty') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="payment_amount" class="sortable-list-header {{ $purchases[0]->orderParam == 'payment_amount' ? 'active' : '' }}" data-sort="payment_amount">{{ __('Amount') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="invoice.number" class="sortable-list-header {{ $purchases[0]->orderParam == 'invoice.number' ? 'active' : '' }}" data-sort="invoice.number">{{ __('Invoice') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="payment_date" class="sortable-list-header {{ $purchases[0]->orderParam == 'payment_date' ? 'active' : '' }}" data-sort="payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="invoice.number" class="sortable-list-header {{ $purchases[0]->orderParam == 'invoice.number' ? 'active' : '' }}" data-sort="invoice.number">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>
    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these purchases?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.purchases.delete-multiple', ['customer' => $customer]) }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">

        @foreach($purchases as $purchase)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <div class="form-check mb-0">
                    <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                            value="{{ $purchase->id }}"
                            id="flexCheckDefault_{{$purchase->id}}">
                    <label class="form-check-label d-none" for="flexCheckDefault_{{$purchase->id}}"></label>
                </div>
                <p class="my-0 hide-transp" style="padding: 0 !important;width: 0 !important;">{{$purchase->id}}</p>
                <p class="my-0">{{$purchase->machine}} <span class="text-secondary" style="display: block;">({{ $purchase->condition }})</span></p>
                <p class="my-0">{{$purchase->machine_quantity}}</p>
                <p class="my-0 text-success">+ ${{number_format($purchase->machine_price / 100 * $purchase->machine_quantity, 2)}}</p>
                <p class="my-0 text-secondary">Invoice #{{$purchase->sequential_id}}</p>
                <p class="my-0">{{\Carbon\Carbon::parse($purchase->starting_date)->format('m/d/Y')}}</p>
                <div class="my-0">
                    @include('partials.purchase-badge', [
                        'status' => $purchase->status,
                     ])  
                </div>

                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <script>
                        function openIframePopup(url){
                            var iframe = document.createElement('iframe');
                            iframe.src = url;
                            var wrapper = document.createElement('div');
                            var close_iframe = document.createElement('span');
                            close_iframe.classList.add('close_iframe');
                            close_iframe.textContent = '×';
                            close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
                            wrapper.id = 'iframe-popup';
                            document.body.appendChild(wrapper);
                            wrapper.appendChild(iframe);
                            wrapper.append(close_iframe);
                            iframe.focus();
                            iframe.contentWindow.focus(); 
                        }
                    </script>
                    <div class="dropdown-content">
                        {{-- <a href="javascript:;">Download Invoice</a>
                        <a href="javascript:;"  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice1">{{ __('Email Invoice') }}</a> --}}
                        @if ($purchase->status === 2)
                        <a href="javascript:void(0);" class="text-success" data-id="{{ $purchase->id }}" data-bs-toggle="modal" data-bs-target="#deliveredModal{{'Purchase'}}{{$purchase->id}}">
                            Mark as Delivered
                        </a>                        
                        @endif
                        @if ($purchase->status === \App\Helpers\Constants::PURCHASE_STATUSES['Signed 1/2'])
                        <a href="javascript:;" onclick="openIframePopup('{{ $purchase->sign_url }}')">{{ __('SIGN NOW') }}</a>                            
                        @endif
                        @if ($purchase->status != '0' AND $purchase->agreement?->status != '5')
                        <a href="javascript:void(0);" class="download-agreement" data-id="{{ $purchase->agreement?->id }}">
                            {{ __('Read Agreement') }}
                        </a>
                        {{-- <a href="javascript:void(0)">{{ __('Send to customer') }} </a> --}}
                        @endif
                        @if ($purchase->invoice)
                        <a href="{{ route('admin.download-invoice', ['customer' => $customer, 'invoice' => $purchase->invoice ])}}">Download Invoice</a>
                        <a href="javascript:;" class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$purchase->invoice->id}}">{{ __('Email Invoice') }}</a>
                        @endif
                        <a href="javascript:;" class="cancel-license text-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{'Purchase'}}{{$purchase->id}}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </div>
            {{-- @include('partials.modals.email-invoice', [
                'id' => $purchase->invoice->id,
                'route' => route('admin.email-invoice', ['customer' => $customer, 'invoice' => $purchase->invoice]),
                'invoice' => $purchase->invoice
            ]) --}}
            @include('partials.modals.cancel', [
                'type' => 'Purchase',
                'id' => $purchase->id,
                'route' => route('admin.purchases.change-status', ['customer' => $customer, 'purchase' => $purchase, 'is_active' => '0', 'status' => '6']),
                'title' => 'Purchase',
            ])
            @include('partials.modals.delivered', [
                'type' => 'Purchase',
                'id' => $purchase->id,
                'status' => '3',
                'route' => route('admin.purchases.mark-as-delivered', ['customer' => $customer, 'purchase' => $purchase, 'status' => '3', 'is_active' => '1']),
                'title' => 'Purchase',
            ])
        @endforeach
        <div id="paginate">
            @if($purchases)
                <div class="">
                    {!! $purchases->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results. route</p>
@endif

{{-- @push('scripts') --}}
<script type="module">
    $(document).ready(function() {
        $(document).on('click', '.openIframePopup', function(event) {
            var url = $(this).data('url');
            var iframe = document.createElement('iframe');
            iframe.src = url;
            var wrapper = document.createElement('div');
            var close_iframe = document.createElement('span');
            close_iframe.classList.add('close_iframe');
            close_iframe.textContent = '×';
            close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
            wrapper.id = 'iframe-popup';
            document.body.appendChild(wrapper);
            wrapper.appendChild(iframe);
            wrapper.append(close_iframe);
            iframe.focus();
            iframe.contentWindow.focus();            
        });
        $(document).on('click', '.close_iframe', function(event) {
            $('#iframe-popup').remove();
        });
    });
    const purchasesCount = '{{count($purchases)}}';
    const descriptiveLabel = purchasesCount === 1 ? ' Item' : ' Items';
    $('#purchasesCount').text(purchasesCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
    // $('.mark-as-delivered').click(function() {
    //     let button = $(this);
    //     let purchaseId = button.data('id');

    //     $.ajax({
            // url: "{{--  route('admin.purchases.mark-as-delivered', ['customer' => $customer, 'purchase' => $purchase, 'status' => '3']) --}}",
    //         type: 'POST',
    //         data: {
    //             _token: '{{ csrf_token() }}'
    //         },
    //         success: function(response) {
    //             if (response.success) {
    //                 window.location.reload();
    //             }
    //         },
    //     });
    // });
    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.purchases.delete-multiple', ['customer' => $customer]) }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });
</script>
{{-- @endpush --}}