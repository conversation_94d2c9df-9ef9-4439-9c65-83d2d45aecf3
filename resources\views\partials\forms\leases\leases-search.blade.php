@if(count($leases) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $leases[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="studio.name" class="sortable-list-header {{ $leases[0]->orderParam == 'studio.name' ? 'active' : '' }}" data-sort="studio.name">{{ __('Studio') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="machine.name" class="sortable-list-header {{ $leases[0]->orderParam == 'machine.name' ? 'active' : '' }}" data-sort="machine.name">{{ __('Machine') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="machine_quantity" class="sortable-list-header {{ $leases[0]->orderParam == 'machine_quantity' ? 'active' : '' }}" data-sort="machine_quantity">{{ __('Qty') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="studio.zip" class="sortable-list-header {{ $leases[0]->orderParam == 'studio.zip' ? 'active' : '' }}" data-sort="studio.zip">{{ __('Zip') }}</div>
        <div id="machine_price" class="sortable-list-header {{ $leases[0]->orderParam == 'machine_price' ? 'active' : '' }}" data-sort="machine_price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Remaining') }}</div>
        <div>{{ __('Expires On') }}</div>
        <div id="is_active" class="sortable-list-header {{ $leases[0]->orderParam == 'invoice.number' ? 'active' : '' }}" data-sort="is_active" style="justify-content: flex-end;">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>
    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these leases?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.leases.delete-multiple', ['customer' => $customer]) }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">

        @foreach($leases as $lease)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <div class="form-check mb-0">
                    <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                            value="{{ $lease->id }}"
                            id="flexCheckDefault_{{$lease->id}}">
                    <label class="form-check-label d-none" for="flexCheckDefault_{{$lease->id}}"></label>
                </div>
                <p class="my-0 hide-transp">{{$lease->sequential_id}}</p>
                <a href="{{ route('admin.leases.show', ['customer' => $customer, 'lease' => $lease, 'tab' => 'profile']) }}" style="text-decoration: none;" class="table-name-col">{{$lease->studio->name}} ({{ $lease->note_count }})</a>
                <p class="my-0 text-secondary">{{ucfirst($lease->machine->name)}}</p>
                <p class="my-0">{{$lease->machine_quantity}}</p>
                <p class="my-0">{{$lease->studio->zip}}</p>
                <div class="table-div">
                    <p class="my-0 text-success">{{$admin_currency_symbol}}{{number_format($lease->total, 2)}}</p>
                    <p class="my-0">{{$admin_currency_symbol}}{{number_format($lease->monthly, 2)}}/month</p>
                </div>
                <div class="table-div">
                    <p class="my-0 text-danger">{{$admin_currency_symbol}}{{number_format($lease->remaining['paymentSum'], 2)}}</p>
                    <p class="my-0">{{$lease->remaining['count']}}/{{$lease->duration}} months</p>
                </div>
                <p class="my-0">{{\Carbon\Carbon::parse($lease->expires)->format('m/d/Y')}}</p>
                <div class="status-div text-end">
                    @include('partials.leases-badge', [
                        'status' => $lease->status,
                    ])
                </div>
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                <script>
                    function openIframePopup(url){
                        var iframe = document.createElement('iframe');
                        iframe.src = url;
                        var wrapper = document.createElement('div');
                        var close_iframe = document.createElement('span');
                        close_iframe.classList.add('close_iframe');
                        close_iframe.textContent = '×';
                        close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
                        wrapper.id = 'iframe-popup';
                        document.body.appendChild(wrapper);
                        wrapper.appendChild(iframe);
                        wrapper.append(close_iframe);
                        iframe.focus();
                        iframe.contentWindow.focus(); 
                    }
                </script>
                    <div class="dropdown-content" id="leases-dropdown-content">
                        @if ($lease->status === \App\Helpers\Constants::LEASE_STATUSES['Signed 1/2'])
                        <a href="javascript:;" onclick="openIframePopup('{{ $lease->sign_url }}')">{{ __('SIGN NOW') }}</a>                            
                        @endif
                        @if ($lease->status === 2)
                        <a href="javascript:void(0);" class="text-success" data-id="{{ $lease->id }}" data-bs-toggle="modal" data-bs-target="#deliveredModal{{'Lease'}}{{$lease->id}}">
                            Mark as Delivered
                        </a>                        
                        @endif
                        {{-- @if ($lease->status === \App\Helpers\Constants::LEASE_STATUSES['Signed 2/2 (wait4pay)'])
                        <a href="{{ route('admin.leases.payment-link', ['customer' => $customer, 'lease' => $lease]) }}">{{ __('Send Payment Link') }}</a>
                        @endif --}}
                        @if ($lease->status != '0' AND $lease->agreement?->status != '5')
                        <a href="javascript:void(0);" class="download-agreement" data-id="{{ $lease->agreement?->id }}">
                            {{ __('Read Agreement') }}
                        </a>
                        {{-- <a href="javascript:void(0)">{{ __('Send to customer') }} </a> --}}
                        @endif

                        <a href="{{ route('admin.leases.edit', ['customer' => $customer, 'lease' => $lease]) }}">Edit</a>
                        <a href=""  class="cancel-lease text-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{'Lease'}}{{$lease->id}}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </div>
            @include('partials.modals.cancel', [
                'type' => 'Lease',
                'id' => $lease->id,
                'route' => route('admin.leases.change-status', ['customer' => $customer, 'lease' => $lease, 'is_active' => '0', 'status' => '6']),
                'title' => 'Lease',
            ])
            @include('partials.modals.delivered', [
                'type' => 'Lease',
                'id' => $lease->id,
                'status' => '3',
                'route' => route('admin.leases.mark-as-delivered', ['customer' => $customer, 'lease' => $lease, 'status' => '3', 'is_active' => '1']),
                'title' => 'Lease',
            ])

        @endforeach
        <div id="paginate paginate-leases">
            @if($leases)
                <div class="">
                    {!! $leases->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    $(document).ready(function() {
        $(document).on('click', '.close_iframe', function(event) {
            $('#iframe-popup').remove();
        });
    });
    function clearLicenseActiveTab() {
        localStorage.removeItem('leaseActiveTab');
    }
    const leasesCount = @json($leases).total;
    const descriptiveLabel = leasesCount === 1 ? ' Item' : ' Items';
    $('#leasesCount').text(leasesCount + descriptiveLabel);


    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.cancel-lease').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.leases.delete-multiple', ['customer' => $customer]) }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });
</script>
