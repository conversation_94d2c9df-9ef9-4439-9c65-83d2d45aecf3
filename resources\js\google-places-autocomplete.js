/**
 * Google Places Autocomplete functionality for address fields
 */

class GooglePlacesAutocomplete {
    constructor() {
        this.autocompleteInstances = [];
        this.isGoogleMapsLoaded = false;
    }

    /**
     * Initialize Google Places Autocomplete for specified fields
     * @param {Array} fieldConfigs - Array of field configuration objects
     */
    init(fieldConfigs) {
        if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
            console.error('Google Maps Places API not loaded');
            return;
        }

        this.isGoogleMapsLoaded = true;
        
        fieldConfigs.forEach(config => {
            this.initializeAutocomplete(config);
        });
    }

    /**
     * Initialize autocomplete for a single field configuration
     * @param {Object} config - Field configuration object
     */
    initializeAutocomplete(config) {
        const inputElement = document.getElementById(config.inputId);
        if (!inputElement) {
            console.warn(`Input element with ID '${config.inputId}' not found`);
            return;
        }

        // Create autocomplete instance
        const autocomplete = new google.maps.places.Autocomplete(inputElement, {
            types: ['address'],
            componentRestrictions: {} // Allow all countries
        });

        // Store the instance
        this.autocompleteInstances.push({
            autocomplete: autocomplete,
            config: config
        });

        // Add place changed listener
        autocomplete.addListener('place_changed', () => {
            this.handlePlaceChanged(autocomplete, config);
        });
    }

    /**
     * Handle place selection
     * @param {google.maps.places.Autocomplete} autocomplete
     * @param {Object} config
     */
    handlePlaceChanged(autocomplete, config) {
        const place = autocomplete.getPlace();

        if (!place.geometry) {
            console.warn('No details available for input: ' + place.name);
            return;
        }

        // Extract address components
        const addressComponents = this.parseAddressComponents(place.address_components);

        // Populate form fields (this will handle location changes internally)
        this.populateFields(config, addressComponents);
        console.log(addressComponents);
        console.log(config);
        
    }

    /**
     * Parse Google Places address components
     * @param {Array} components 
     * @returns {Object}
     */
    parseAddressComponents(components) {
        const result = {
            street_number: '',
            route: '',
            locality: '',
            administrative_area_level_1: '',
            country: '',
            postal_code: ''
        };

        components.forEach(component => {
            const types = component.types;
            
            if (types.includes('street_number')) {
                result.street_number = component.long_name;
            }
            if (types.includes('route')) {
                result.route = component.long_name;
            }
            if (types.includes('locality')) {
                result.locality = component.long_name;
            }
            if (types.includes('administrative_area_level_1')) {
                result.administrative_area_level_1 = component.short_name;
            }
            if (types.includes('country')) {
                result.country = component.long_name;
                result.country_code = component.short_name;
            }
            if (types.includes('postal_code')) {
                result.postal_code = component.long_name;
            }
        });

        return result;
    }

    /**
     * Populate form fields with address data
     * @param {Object} config
     * @param {Object} addressComponents
     */
    populateFields(config, addressComponents) {
        const fields = config.fields;
        const isUSA = addressComponents.country_code === 'US';

        console.log('isUSA: ', isUSA);
        
        // Populate basic address fields immediately
        if (fields.address) {
            const addressValue = `${addressComponents.street_number} ${addressComponents.route}`.trim();
            this.setFieldValue(fields.address, addressValue);
        }

        if (fields.city) {
            this.setFieldValue(fields.city, addressComponents.locality);
        }

        if (fields.zip) {
            this.setFieldValue(fields.zip, addressComponents.postal_code);
        }

        // Handle location change first, then populate state/country fields
        this.handleLocationChange(config, addressComponents);

        // Populate state/country fields after a short delay to ensure containers are visible
        setTimeout(() => {
            if (isUSA && fields.state) {
                this.setStateValue(fields.state, addressComponents.administrative_area_level_1);
            } else if (!isUSA && fields.country) {
                this.setCountryValue(fields.country, addressComponents.country);
            }
        }, 200);
    }

    /**
     * Set field value
     * @param {string} fieldId 
     * @param {string} value 
     */
    setFieldValue(fieldId, value) {
        const field = document.getElementById(fieldId);
        if (field && value) {
            field.value = value;
            // Trigger change event
            field.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }

    /**
     * Set state dropdown value for USA addresses
     * @param {string} fieldId
     * @param {string} stateCode
     */
    setStateValue(fieldId, stateCode) {
        const select = $('select[name="' + fieldId + '"]');
        console.log(`SELECT FOR CHANGE: `, select[0]);
        console.log(`STATE CODE: `, stateCode.toLowerCase());
        if (select && stateCode) {
            // Find option by state code (short name like 'CA', 'NY', etc.)
            const options = select.find('option').get();
            let found = false;

            for (let option of options) {
                // Check if option text contains the state code or full state name
                const optionText = option.text.toLowerCase();
                const optionValue = option.value;

                // Try to match by state abbreviation first, then by full name
                if (optionText.includes(stateCode.toLowerCase()) || optionValue === stateCode || this.getStateFullName(stateCode).toLowerCase() === optionText) {
                    console.log(`State: ${option.value}`);
                    
                    select.val(option.value).change();
                    select.value = option.value;
                    found = true;
                    break;
                }
            }

            if (found) {
                // Update Select2 display
                $(select).trigger('change');
                console.log(`State selected: ${stateCode} for field ${fieldId}`);
            } else {
                console.warn(`State not found: ${stateCode} for field ${fieldId}`);
            }
        }
    }

    /**
     * Set country dropdown value for international addresses
     * @param {string} fieldId
     * @param {string} countryName
     */
    setCountryValue(fieldId, countryName) {
        const select = $('select[name="' + fieldId + '"]');
        console.log(`SELECT FOR CHANGE: `, select[0]);
        if (select && countryName) {
            // Find option by country name
            // const options = select.querySelectorAll('option');
            const options = select.find('option').get();
            let found = false;

            for (let option of options) {
                const optionText = option.text.toLowerCase();
                const countryLower = countryName.toLowerCase();

                // Try exact match first, then partial match
                if (optionText === countryLower || optionText.includes(countryLower)) {
                    select.val(option.value).change();
                    select.value = option.value;
                    found = true;
                    break;
                }
            }

            if (found) {
                // Update Select2 display
                $(select).trigger('change');
                console.log(`Country selected: ${countryName} for field ${fieldId}`);
            } else {
                console.warn(`Country not found: ${countryName} for field ${fieldId}`);
            }
        }
    }

    /**
     * Get full state name from abbreviation
     * @param {string} stateCode
     * @returns {string}
     */
    getStateFullName(stateCode) {
        const stateMap = {
            'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
            'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
            'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
            'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
            'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
            'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
            'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
            'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
            'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
            'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming'
        };
        return stateMap[stateCode] || stateCode;
    }

    /**
     * Handle location radio button changes based on country
     * @param {Object} config
     * @param {Object} addressComponents
     */
    handleLocationChange(config, addressComponents) {
        const isUSA = addressComponents.country_code === 'US';
        const locationRadios = config.locationRadios;

        if (locationRadios) {
            if (isUSA) {
                // Select USA radio button
                const usaRadio = document.getElementById(locationRadios.usa);
                if (usaRadio && !usaRadio.checked) {
                    usaRadio.checked = true;
                    // Trigger click event to activate existing form logic
                    usaRadio.dispatchEvent(new Event('click', { bubbles: true }));
                    console.log('Switched to USA location for', config.inputId);
                }
            } else {
                // Select International radio button
                const intlRadio = document.getElementById(locationRadios.international);
                if (intlRadio && !intlRadio.checked) {
                    intlRadio.checked = true;
                    // Trigger click event to activate existing form logic
                    intlRadio.dispatchEvent(new Event('click', { bubbles: true }));
                    console.log('Switched to International location for', config.inputId);
                }
            }
        }
    }

    /**
     * Load Google Maps API dynamically
     * @param {string} apiKey 
     * @param {Function} callback 
     */
    static loadGoogleMapsAPI(apiKey, callback) {
        if (typeof google !== 'undefined' && google.maps && google.maps.places) {
            callback();
            return;
        }

        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGooglePlaces`;
        script.async = true;
        script.defer = true;
        
        // Set global callback
        window.initGooglePlaces = callback;
        
        document.head.appendChild(script);
    }
}

// Export for use in other files
window.GooglePlacesAutocomplete = GooglePlacesAutocomplete;

export default GooglePlacesAutocomplete;
