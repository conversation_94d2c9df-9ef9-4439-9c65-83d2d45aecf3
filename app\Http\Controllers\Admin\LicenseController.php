<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\License\ChangeLicenseStatusRequest;
use App\Http\Requests\Admin\License\StoreLicenseRequest;
use App\Http\Requests\Admin\License\UpdateLicenseRequest;
use App\Http\Requests\Admin\LicenseNote\AddLicenseNoteRequest;
use App\Http\Requests\Admin\LicenseFile\AddLicenseFileRequest;
use App\Mail\PaymentLinkEmail;
use App\Models\AdminNotification;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Countries;
use App\Models\Customer;
use App\Models\License;
use App\Models\LicenseSettings;
use App\Models\Payment;
use App\Models\State;
use App\Models\Studio;
use App\Models\Note;
use App\Models\LicenseNotes;
use App\Models\LicenseFiles;
use App\Services\Admin\License\ILicenseService;
use App\Services\Admin\AdobeSign\AdobeSignService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;
use Illuminate\Support\Facades\Notification;

class LicenseController extends Controller
{
    private ILicenseService $licenseService;

    public function __construct(
        ILicenseService $licenseService,
    )
    {
        $this->licenseService = $licenseService;
    }

    public function index(Customer $customer): View
    {
        return view('admin.licenses.index', compact('customer'));
    }

    public function show(Customer $customer, License $license, Request $request): View
    {
        $tab              = $request->query('tab', 'profile');
//        $license          = $license->load('payments', 'conversionRate');
        $currentCurrency  = AdminSettings::first()->currency;
        $package          = LicenseSettings::where('id', $license->package)->first();
        $conversionRate   = $license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;

        $licenseRemaining = CurrencyConversionHelper::calculateLicensePaymentsSum($license, $conversionRate);
        $licenseMonthly   = CurrencyConversionHelper::calculateLicenseMonthlyPrice($license, $conversionRate);
        $licenseTotal     = CurrencyConversionHelper::calculateLicenseTotalPrice($license, $conversionRate);
        $licenseDeposit   = CurrencyConversionHelper::calculateLicenseDeposit($license, $conversionRate);
        $license_notes = $license->notes()->get();
        $license_files_count = count($license->files()->get());
        return view('admin.licenses.show', compact('customer', 'license', 'tab', 'package', 'licenseRemaining', 'conversionRate', 'licenseTotal', 'licenseMonthly', 'licenseDeposit', 'license_notes', 'license_files_count'));
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';

        $licenses = $this->licenseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }
        foreach ($licenses as $license) {
            $license['sequential_id'] = $sequential_id++;
            $conversionRate             = $license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            // $license['remaining']       = CurrencyConversionHelper::calculateLicensePaymentsSum($license, $conversionRate);
            // $license['expires']         = Carbon::parse($license->payments->sortByDesc('payment_date')->take(1)->first()?->payment_date)->addYear()->format('m/d/Y');
            $license['expires']         = Carbon::parse($license->starting_date)->addYear()->format('m/d/Y');
            $license['conversion_rate'] = $conversionRate;
            $license['price']           = CurrencyConversionHelper::calculateLicenseMonthlyPrice($license, $conversionRate);
            $license['note_count'] = $license->license_notes_count;
            $agreement = Agreement::where('license_id', $license->id)->first();
            $license['agreement'] = $agreement;            
            $sign = NULL;
            if($license->status == Constants::LICENSE_STATUSES['Signed 1/2']){
                $sign = AdobeSignService::getAgreementInfo($agreement->adobe_agreement_id);
                if(isset($sign['message'])){
                    $license['sign_url'] = 'javascript:;';
                }else{
                    $license['sign_url'] = $sign['signingUrlSetInfos'][0]['signingUrls'][0]['esignUrl'];
                }                
            }
        }

        $viewContent = view('partials.forms.licenses-search', compact('licenses', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function create(Customer $customer): View
    {
        $companies = Company::select('id', 'name')->get();
        $studios   = Studio::select('id', 'name')->get();
        $packages   = LicenseSettings::select('id', 'name')->get();
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        return view('admin.licenses.create', compact('customer', 'studios', 'states_countries', 'companies', 'packages'));
    }
    
    public function store(Customer $customer, StoreLicenseRequest $request)
    {
        try {
            $this->licenseService->store($request->validated(), $customer);
            toastr()->addSuccess('', 'License created successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            // dd($request);
            dd($e);
            toastr()->addError('License creation failed');
            return redirect()->back()->withInput();
        }
    }

    public function store_license_notes(License $license, Customer $customer, AddLicenseNoteRequest $request){
        try {
            // dd($license);
            $license->notes()->create($request->validated());
            toastr()->addSuccess('', 'License note created successfully.');
            return redirect()->route('admin.licenses.show', ['customer'=> $customer, 'license' => $license, 'tab' => 'notes']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('License note creation failed');
            return redirect()->back();
        }
    }

    public function store_license_files(License $license, Customer $customer, AddLicenseFileRequest $request)
    {
        try {
            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $uniqueName = pathinfo($originalName, PATHINFO_FILENAME) . '-' . \Illuminate\Support\Str::random(8) . '.' . $extension;
                $uploadPath = public_path('uploads');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }
                $file->move($uploadPath, $uniqueName);
                $license->files()->create([
                    'file_path' => 'uploads/' . $uniqueName,
                    'original_name' => $originalName,
                ]);
            }

            toastr()->addSuccess('', 'License files uploaded successfully.');
            return redirect()->route('admin.licenses.show', [
                'customer' => $customer,
                'license' => $license,
                'tab' => 'files'
            ]);
        } catch (\Exception $e) {
            // \Log::error('File Upload Error: ' . $e->getMessage());
            dd($e->getMessage());
            toastr()->addError('License file upload failed');
            return redirect()->back();
        }
    }

    public function destroy_license_file(License $license, LicenseFiles $file)
    {
        try {
            // Get the full file path
            $filePath = public_path($file->file_path);

            // Check if file exists and delete it
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Remove from database
            $file->delete();

            toastr()->addSuccess('', 'File deleted successfully.');
        } catch (\Exception $e) {
            // \Log::error('File Deletion Error: ' . $e->getMessage());
            toastr()->addError('File deletion failed.');
        }

        return redirect()->back();
    }


    public function edit(Customer $customer, License $license): View
    {
        $companies = Company::select('id', 'name')->get();
        $studios   = Studio::select('id', 'name')->get();
        $packages   = LicenseSettings::select('id', 'name')->get();
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        $currentCurrency            = AdminSettings::first()->currency;
        $conversionRate             = $license->conversionRate()->where('currency_id', $currentCurrency->id)->first();
        $customer->description      = LicenseNotes::where('license_id', $license->id)->first()->body ?? NULL;
        $license->converted_price   = $license->price / 100 / $conversionRate->rate;
        $license->converted_deposit = ($license->deposit_amount) ? $license->deposit_amount / 100 / $conversionRate->rate : null;
        return view('admin.licenses.edit', compact('license', 'customer', 'studios', 'states_countries', 'companies', 'packages'));
    }

    public function paymentLink(Customer $customer, License $license)
    {
        $customer = Customer::where('id', $license->customer_id)->first();
        $license_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $license->id))));
        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'license', 'id' => $license_id])));

        toastr()->addSuccess('', 'Payment Link sent successfully.');
        return redirect()->back();
    }

    public function update(Customer $customer, License $license, UpdateLicenseRequest $request)
    {
        try {
            $this->licenseService->update($request->validated(), $license, $customer);
            toastr()->addSuccess('', 'License updated successfully.');
            return redirect()->route('admin.licenses.edit', ['customer' => $customer, 'license' => $license]);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('License update failed');
            return redirect()->back();
        }
    }

    public function destroy(Customer $customer, License $license)
    {
        try {
            $this->licenseService->delete($license);
            toastr()->addSuccess('', 'License deleted successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            toastr()->addError('License delete failed');
            return redirect()->back();
        }
    }

    public function destroy_note(License $license, Customer $customer, LicenseNotes $notes)
    {
        try {
            $notes->delete();
            toastr()->addSuccess('', 'License Note deleted successfully.');
            return redirect()->route('admin.licenses.show', ['customer' => $customer, 'license' => $license, 'tab' => 'notes']);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('License Note delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Customer $customer, Request $request)
    {
        try {
            $licenseIds = $request->input('selectedItems');
            foreach ($licenseIds as $licenseId) {
                $this->licenseService->delete(License::find($licenseId));
            }
            toastr()->addSuccess('', 'Selected licenses deleted successfully.');

            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'licence']);
        } catch (\Exception $e) {
            toastr()->addError('License delete failed');
            return redirect()->back();
        }
    }

    public function loadPaymentHistoryBlade(Customer $customer, License $license, Request $request)
    {
        return view('admin.licenses.payment-history', compact('customer', 'license'))->render();
    }

    public function loadPaymentHistoryContent(Customer $customer, License $license, Request $request)
    {
        return view('admin.licenses.payment-history-search', compact('customer', 'license'))->render();
    }

    public function loadTabContent(Customer $customer, License $license, Request $request)
    {
        $tab = $request->query('tab');

        switch ($tab) {
            case 'payment-history':
                return view('admin.licenses.payment-history-index', compact('customer', 'license'))->render();
            case 'upcoming-payments':
                return view('admin.licenses.upcoming-payments-index', compact('customer', 'license'))->render();
            case 'notes':
                $notes = $license->notes()->orderBy('created_at', 'desc')->get();
                return view('admin.license-notes.index', compact('notes', 'customer', 'license'))->render();
                // return view('admin.licenses.upcoming-payments-index', compact('customer', 'license'))->render();
            case 'files':
                $files = $license->files()->orderBy('created_at', 'desc')->get(); // Fetch files
                return view('admin.license-files.index', compact('files', 'customer', 'license'))->render();
            default:
                return response()->json(['error' => 'Invalid tab'], 400);
        }
    }

    public function changeStatus(Customer $customer, License $license, ChangeLicenseStatusRequest $request)
    {
        try {
            if ($request->get('status')) {                
                $license->update(['status' => $request->get('status')]);
            }
            if (!$request->get('is_active')) {
                $license->payments()
                    ->where('payment_date', '>', Carbon::now())
                    ->update(['status' => Constants::PAYMENT_STATUS['declined']]);

                $license->update(['is_active' => $request->get('is_active')]);
                
                toastr()->addSuccess('', 'License cancelled successfully.');
                return redirect()->back();
            }
            
            return redirect()->back();
        } catch (\Exception $e) {
            toastr()->addError('License cancellation failed');
            return redirect()->back();
        }
    }
}
