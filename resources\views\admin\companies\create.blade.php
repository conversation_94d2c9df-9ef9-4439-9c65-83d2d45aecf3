@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('new company') }}</h3>
            <a href="{{ route('admin.companies.index') }}" class="back-link">← Back</a>
        </div>

    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.companies.store') }}" id="studio-location-form" enctype="multipart/form-data">
            @csrf

            <h5 class="form-section-title first-title">{{ __('status') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'status',
                'field_label' => 'Radio group status',
                'values' => ['status_active' => ['text' => 'Active', 'value' => '1'], 'status_inactive' => ['text' => 'Inactive', 'value' => '0']],
                'field' => 'status',
                'required' => 'required',
                'checked' => '1',
            ])

            <h5 class="form-section-title">{{ __('company logo') }}</h5>

            <div class="d-flex align-items-center">
                @include('partials.forms.input-file', ['field_name' => 'logo', 'field_value' => ''])
                <div class="ms-5">
                    <p class="text-info mb-2">{{ __('Max. file size is 1 MB. Supported formats: PNG/JPG.') }}<br>{{ __('Desirable size: 1080px x 1080px.') }}</p>
                    <button class="imgRemove btn btn-link" onclick="myImgRemove()">{{ __('Remove') }}</button>
                </div>
            </div>

            <h5 class="form-section-title">{{ __('company info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'NAME *',
                'field_type' => 'text',
            ])

            @include('partials.forms.input', [
                'field_name' => 'email',
                'field_label' => 'EMAIL ADDRESS *',
                'field_type' => 'email',
            ])

            @include('partials.forms.input', [
               'field_name' => 'phone',
               'field_label' => 'PHONE *',
               'field_type' => 'text',
           ])

           <h5 class="form-section-title">{{ __('address info') }}</h5>

            @include('partials.forms.input', [
               'field_name' => 'address',
               'field_label' => 'ADDRESS',
               'field_type' => 'text',
           ])

            @include('partials.forms.input', [
               'field_name' => 'address2',
               'field_label' => 'ADDRESS 2',
               'field_type' => 'text',
           ])

            @include('partials.forms.input', [
               'field_name' => 'city',
               'field_label' => 'CITY',
               'field_type' => 'text',
           ])

           @include('partials.forms.input', [
               'field_name' => 'zip',
               'field_label' => 'ZIP CODE',
               'field_type' => 'text',
            ])

            @include('partials.forms.select', [
               'field_name' => 'state_id',
               'field_label' => 'STATE',
               'values' => $states,
               'field' => 'state',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('state_id'),
               'include_empty' => true
           ])

            

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.companies.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
<script>
    document.addEventListener('DOMContentLoaded', function () {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        // Initialize Google Places Autocomplete
        window.initGooglePlaces = function() {
            const autocompleteService = new GooglePlacesAutocomplete();

            const fieldConfigs = [
                {
                    inputId: 'address',
                    fields: {
                        address: 'address',
                        city: 'city',
                        zip: 'zip',
                        state: 'state_id'
                        // No country field or location radios for company form
                    }
                }
            ];

            autocompleteService.init(fieldConfigs);
        };

        // Load Google Maps API
        const googleApiKey = '{{ config("services.google_maps.api_key") }}';
        if (googleApiKey && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
            GooglePlacesAutocomplete.loadGoogleMapsAPI(googleApiKey, window.initGooglePlaces);
        }
    });
</script>

<!-- Google Places Autocomplete Script -->
@vite('resources/js/google-places-autocomplete.js')
